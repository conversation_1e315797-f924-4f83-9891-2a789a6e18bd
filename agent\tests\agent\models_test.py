import os
import unittest
from unittest.mock import Mock, patch

from src.agent.models import (
    Action,
    Intent,
    AgentResponse,
    HistoryEntry,
    ConversationEntry,
    AgentCache,
    AgentState,
    LLMOutput,
)


class TestActionModel(unittest.TestCase):
    """Test Action basic functionality."""

    def test_action_model_creation(self):
        """Action can be created with required fields."""
        action = Action(name="search", reason="Find relevant papers")
        self.assertEqual(action.name, "search")
        self.assertEqual(action.reason, "Find relevant papers")


class TestAgentResponse(unittest.TestCase):
    """Test AgentResponse parsing from LLM responses."""

    def test_from_llm_response_valid_json(self):
        """Parsing valid fenced JSON response from LLM works."""
        response_text = '''```json
        {
            "intent": "causal impact",
            "follow_up_question": false,
            "thought": "I need to search for papers",
            "action": {"name": "search", "reason": "Find papers"},
            "answer": null
        }
        ```'''

        response = AgentResponse.from_llm_response(response_text)
        self.assertEqual(response.intent.value, "causal impact")
        self.assertFalse(response.follow_up_question)
        self.assertEqual(response.thought, "I need to search for papers")
        self.assertIsNotNone(response.action)
        self.assertEqual(response.action.name, "search")
        self.assertEqual(response.action.reason, "Find papers")
        self.assertIsNone(response.answer)

    def test_from_llm_response_legacy_action_format(self):
        """Legacy action format with 'input' key is cleaned."""
        response_text = '''```json
        {
            "intent": "causal impact",
            "follow_up_question": false,
            "thought": "I need to search",
            "action": {"name": "search", "reason": "Find papers", "input": "deprecated"},
            "answer": null
        }
        ```'''

        response = AgentResponse.from_llm_response(response_text)
        self.assertIsNotNone(response.action)
        self.assertEqual(response.action.name, "search")
        self.assertEqual(response.action.reason, "Find papers")
        # Ensure 'input' was removed by the loader; accessing it should fail
        self.assertFalse(hasattr(response.action, "input"))

    def test_from_llm_response_no_json(self):
        """Error when no JSON can be found at all."""
        response_text = "This is just plain text without JSON"

        with self.assertRaises(ValueError) as context:
            AgentResponse.from_llm_response(response_text)
        self.assertIn("No JSON found", str(context.exception))

    def test_from_llm_response_invalid_json(self):
        """Invalid JSON should raise with the expected message."""
        response_text = '''```json
        {
            "intent": "causal impact",
            "invalid": json
        }
        ```'''

        with self.assertRaises(ValueError) as context:
            AgentResponse.from_llm_response(response_text)
        self.assertIn("Error parsing LLM response", str(context.exception))


class TestAgentCacheInMemory(unittest.TestCase):
    """Tests for AgentCache behavior when Redis is disabled."""

    def setUp(self):
        """Set up tests with Redis disabled."""
        with patch.dict(os.environ, {"REDIS_CACHE_ENABLED": "false"}):
            self.cache = AgentCache()

    def test_redis_disabled_initialization(self):
        """AgentCache initializes without Redis."""
        self.assertFalse(self.cache.redis_enabled)
        self.assertIsNone(self.cache.redis_cache)

    def test_history_operations_in_memory_only(self):
        """History operations use only in-memory storage."""
        key = "test_conversation"

        # Initially empty
        history = self.cache.get_or_init_history(key)
        self.assertEqual(history, [])

        # Add an entry with an answer so it persists past the filter
        entries = [HistoryEntry(
            intent="causal impact",
            follow_up_question=False,
            thought="Test thought",
            answer="Test answer",
            iteration=1
        )]
        self.cache.update_history(key, entries)

        # Retrieve history
        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].intent, "causal impact")
        self.assertEqual(retrieved[0].thought, "Test thought")
        self.assertEqual(retrieved[0].answer, "Test answer")

    def test_conversation_history_operations_in_memory_only(self):
        """Conversation history stores and retrieves entries in memory."""
        key = "test_conversation"

        # Initially empty
        conv_history = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(conv_history, [])

        # Add conversation entries
        entries = [ConversationEntry(
            user="What is AI?",
            agent="AI is artificial intelligence"
        )]
        self.cache.update_conversation_history(key, entries)

        # Retrieve conversation history
        retrieved = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].user, "What is AI?")
        self.assertEqual(retrieved[0].agent, "AI is artificial intelligence")

    def test_clear_cache_in_memory_only(self):
        """Clearing a specific key removes entries from memory."""
        key = "test_conversation"

        # Add data
        self.cache.update_history(key, [HistoryEntry(
            intent="test", follow_up_question=False, thought="test", iteration=1
        )])
        self.cache.update_conversation_history(key, [ConversationEntry(
            user="test", agent="response"
        )])

        # Clear cache
        self.cache.clear_cache(key)

        # Verify data is cleared from in-memory storage
        self.assertNotIn(key, self.cache.history)
        self.assertNotIn(key, self.cache.conversation_history)

    def test_history_filtering_entries_without_answers(self):
        """get_or_init_history filters out entries with answer=None."""
        key = "test_conversation"

        # Mixed entries (only one with an answer)
        entries = [
            HistoryEntry(
                intent="causal impact",
                follow_up_question=False,
                thought="Failed execution",
                action=None,
                answer=None,  # filtered out
                iteration=1
            ),
            HistoryEntry(
                intent="causal impact",
                follow_up_question=False,
                thought="Successful execution",
                answer="This is a successful answer",  # included
                iteration=2
            ),
            HistoryEntry(
                intent="causal impact",
                follow_up_question=False,
                thought="Another failed execution",
                answer=None,  # filtered out
                iteration=3
            )
        ]

        self.cache.update_history(key, entries)

        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].thought, "Successful execution")
        self.assertEqual(retrieved[0].answer, "This is a successful answer")
        self.assertEqual(retrieved[0].iteration, 2)

    def test_conversation_history_filtering_entries_without_agent_response(self):
        """Only entries with an agent response are returned."""
        key = "test_conversation"

        entries = [
            ConversationEntry(user="What is AI?", agent=None),  # filtered
            ConversationEntry(user="Tell me about machine learning", agent="Machine learning is a subset of AI"),
            ConversationEntry(user="How does deep learning work?", agent=None),  # filtered
            ConversationEntry(user="What are neural networks?", agent="They are computing systems inspired by biological networks"),
        ]

        self.cache.update_conversation_history(key, entries)
        retrieved = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(retrieved), 2)
        self.assertEqual(retrieved[0].user, "Tell me about machine learning")
        self.assertEqual(retrieved[0].agent, "Machine learning is a subset of AI")
        self.assertEqual(retrieved[1].user, "What are neural networks?")
        self.assertEqual(
            retrieved[1].agent,
            "They are computing systems inspired by biological networks"
        )

    def test_history_filtering_all_entries_filtered_out(self):
        """If all entries lack answers, returned history is empty."""
        key = "test_conversation"

        entries = [
            HistoryEntry(
                intent="search",
                follow_up_question=False,
                thought="Failed execution 1",
                answer=None,
                iteration=1
            ),
            HistoryEntry(
                intent="analyze",
                follow_up_question=False,
                thought="Failed execution 2",
                answer=None,
                iteration=2
            )
        ]

        self.cache.update_history(key, entries)
        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 0)

    def test_conversation_history_filtering_all_entries_filtered_out(self):
        """If all conversation entries lack agent responses, result is empty."""
        key = "test_conversation"

        entries = [
            ConversationEntry(user="What is AI?", agent=None),
            ConversationEntry(user="Tell me about ML", agent=None),
        ]
        self.cache.update_conversation_history(key, entries)

        retrieved = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(retrieved), 0)


class TestAgentCacheWithRedis(unittest.TestCase):
    """Tests for AgentCache behavior when Redis is enabled."""

    def setUp(self):
        """Initialize with Redis enabled and mock the Cache."""
        self.mock_redis_cache = Mock()

        with patch.dict(os.environ, {"REDIS_CACHE_ENABLED": "true"}):
            with patch('src.agent.models.Cache', return_value=self.mock_redis_cache):
                self.cache = AgentCache()

    def test_redis_enabled_initialization(self):
        """AgentCache initializes with Redis."""
        self.assertTrue(self.cache.redis_enabled)
        self.assertIsNotNone(self.cache.redis_cache)

    def test_history_operations_use_redis(self):
        """History get/update use Redis when enabled."""
        key = "test_conversation"

        redis_data = [{
            "intent": "causal impact",
            "follow_up_question": False,
            "thought": "Redis thought",
            "action": None,
            "observation": None,
            "answer": "Redis answer",  # to pass the filter
            "iteration": 1
        }]
        self.mock_redis_cache.get_json.return_value = redis_data

        history = self.cache.get_or_init_history(key)
        self.mock_redis_cache.get_json.assert_called_with(f"agent_history:{key}")
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].thought, "Redis thought")
        self.assertEqual(history[0].answer, "Redis answer")

        new_entries = [HistoryEntry(
            intent="update", follow_up_question=False, thought="new", iteration=2
        )]
        self.cache.update_history(key, new_entries)
        self.assertTrue(self.mock_redis_cache.store_json.called)

    def test_conversation_history_operations_use_redis(self):
        """Conversation history get/update use Redis when enabled."""
        key = "test_conversation"

        redis_data = [{"user": "Redis question", "agent": "Redis answer"}]
        self.mock_redis_cache.get_json.return_value = redis_data

        conv_history = self.cache.get_or_init_conversation_history(key)
        self.mock_redis_cache.get_json.assert_called_with(f"agent_conversation:{key}")
        self.assertEqual(len(conv_history), 1)
        self.assertEqual(conv_history[0].user, "Redis question")
        self.assertEqual(conv_history[0].agent, "Redis answer")

        new_entries = [ConversationEntry(user="new", agent="response")]
        self.cache.update_conversation_history(key, new_entries)
        self.assertTrue(self.mock_redis_cache.store_json.called)

    def test_redis_fallback_to_memory_on_error(self):
        """If Redis get fails, fallback to empty in-memory and still usable."""
        key = "test_conversation"

        self.mock_redis_cache.get_json.side_effect = Exception("Redis error")

        history = self.cache.get_or_init_history(key)
        self.assertEqual(history, [])  # Empty in-memory storage

        entries = [HistoryEntry(
            intent="fallback",
            follow_up_question=False,
            thought="memory",
            answer="memory answer",
            iteration=1
        )]
        self.cache.update_history(key, entries)

        # Make Redis work again (returning None)
        self.mock_redis_cache.get_json.side_effect = None
        self.mock_redis_cache.get_json.return_value = None

        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].thought, "memory")
        self.assertEqual(retrieved[0].answer, "memory answer")

    def test_clear_all_caches_with_redis(self):
        """Clearing all caches enumerates and clears keys in Redis."""
        self.mock_redis_cache.get_all_by_prefix.side_effect = [
            ["agent_history:conv1", "agent_history:conv2"],
            ["agent_conversation:conv1", "agent_conversation:conv2"]
        ]

        self.cache.clear_all_caches()

        self.mock_redis_cache.get_all_by_prefix.assert_any_call("agent_history:")
        self.mock_redis_cache.get_all_by_prefix.assert_any_call("agent_conversation:")
        # 4 keys cleared (two prefixes × two keys each)
        self.assertEqual(self.mock_redis_cache.store_json.call_count, 4)

    def test_history_filtering_entries_without_answers_redis(self):
        """History filter behavior is applied to Redis-fetched data."""
        key = "test_conversation"

        redis_data = [
            {
                "intent": "causal impact",
                "follow_up_question": False,
                "thought": "Failed execution",
                "action": None,
                "observation": None,
                "answer": None,  # filtered out
                "iteration": 1
            },
            {
                "intent": "causal impact",
                "follow_up_question": False,
                "thought": "Successful execution",
                "action": None,
                "observation": None,
                "answer": "This is a successful answer",  # included
                "iteration": 2
            },
            {
                "intent": "analyze",
                "follow_up_question": False,
                "thought": "Another failed execution",
                "action": None,
                "observation": None,
                "answer": None,  # filtered out
                "iteration": 3
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        history = self.cache.get_or_init_history(key)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].thought, "Successful execution")
        self.assertEqual(history[0].answer, "This is a successful answer")
        self.assertEqual(history[0].iteration, 2)

    def test_conversation_history_filtering_entries_without_agent_response_redis(self):
        """Only conversation entries with agent responses are kept (Redis)."""
        key = "test_conversation"

        redis_data = [
            {"user": "What is AI?", "agent": None},
            {"user": "Tell me about machine learning", "agent": "Machine learning is a subset of AI"},
            {"user": "How does deep learning work?", "agent": None},
            {"user": "What are neural networks?", "agent": "Neural networks are inspired by biological networks"},
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        conv_history = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(conv_history), 2)
        self.assertEqual(conv_history[0].user, "Tell me about machine learning")
        self.assertEqual(conv_history[0].agent, "Machine learning is a subset of AI")
        self.assertEqual(conv_history[1].user, "What are neural networks?")
        self.assertEqual(conv_history[1].agent, "Neural networks are inspired by biological networks")

    def test_history_filtering_all_entries_filtered_out_redis(self):
        """If all Redis entries lack answers, result is empty."""
        key = "test_conversation"

        redis_data = [
            {
                "intent": "causal impact",
                "follow_up_question": False,
                "thought": "Failed execution 1",
                "action": None,
                "observation": None,
                "answer": None,
                "iteration": 1
            },
            {
                "intent": "causal impact",
                "follow_up_question": False,
                "thought": "Failed execution 2",
                "action": None,
                "observation": None,
                "answer": None,
                "iteration": 2
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        history = self.cache.get_or_init_history(key)
        self.assertEqual(len(history), 0)

    def test_conversation_history_filtering_all_entries_filtered_out_redis(self):
        """If all Redis entries lack agent responses, result is empty."""
        key = "test_conversation"

        redis_data = [
            {"user": "What is AI?", "agent": None},
            {"user": "Tell me about ML", "agent": None},
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        conv_history = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(conv_history), 0)


class TestAgentState(unittest.TestCase):
    """Tests for AgentState functionality."""

    def setUp(self):
        self.state = AgentState(
            conversation_id="test_conv",
            query="What is machine learning?",
            max_iterations=3
        )

    def test_agent_state_initialization(self):
        """AgentState initializes with correct defaults."""
        self.assertEqual(self.state.conversation_id, "test_conv")
        self.assertEqual(self.state.query, "What is machine learning?")
        self.assertEqual(self.state.current_iteration, 0)
        self.assertEqual(self.state.max_iterations, 3)
        self.assertEqual(self.state.history, [])
        self.assertEqual(self.state.conversation_history, [])
        self.assertEqual(self.state.llm_outputs, [])

    def test_add_to_history(self):
        """Adding entries to history works and preserves fields."""
        self.state.add_to_history(
            intent="search",
            follow_up_question=False,
            thought="Need to search",
            action={"name": "search", "reason": "Find papers"},  # dict to fit ActionModel
            observation="Found 5 papers",
            answer="Here are the papers",
            iteration=1
        )

        self.assertEqual(len(self.state.history), 1)
        entry = self.state.history[0]
        self.assertEqual(entry.intent, "search")
        self.assertEqual(entry.thought, "Need to search")
        self.assertEqual(entry.observation, "Found 5 papers")
        self.assertEqual(entry.answer, "Here are the papers")
        self.assertEqual(entry.iteration, 1)

    def test_conversation_entry_operations(self):
        """Conversation add and update operations work."""
        self.state.add_conversation_entry("What is AI?")
        self.assertEqual(len(self.state.conversation_history), 1)
        self.assertEqual(self.state.conversation_history[0].user, "What is AI?")
        self.assertIsNone(self.state.conversation_history[0].agent)

        self.state.update_last_conversation_entry("AI is artificial intelligence")
        self.assertEqual(self.state.conversation_history[0].agent, "AI is artificial intelligence")

    def test_format_history(self):
        """History formatting includes expected sections."""
        self.state.add_to_history(
            intent="search",
            follow_up_question=False,
            thought="I need to search",
            action={"name": "search", "reason": "Find papers"},
            observation="Found papers",
            # Provide an answer so the 'Answer:' line appears (model only prints if present)
            answer="Here are the results",
            iteration=1
        )

        formatted = self.state.format_history()

        self.assertIn("=========Iteration: 1=========", formatted)
        self.assertIn("Thought: I need to search", formatted)
        self.assertIn("Action: search - Objective: Find papers", formatted)
        self.assertIn("Observation: Found papers", formatted)
        self.assertIn("Answer: Here are the results", formatted)
        self.assertIn("=========End of Iteration: 1=========", formatted)

    def test_format_conversation_history(self):
        """Conversation history formatting is correct."""
        self.state.add_conversation_entry("What is AI?", "AI is artificial intelligence")
        self.state.add_conversation_entry("Tell me more")

        formatted = self.state.format_conversation_history()

        self.assertIn("User: What is AI?", formatted)
        self.assertIn("Agent: AI is artificial intelligence", formatted)
        self.assertIn("User: Tell me more", formatted)

    def test_get_llm_output(self):
        """Retrieve LLM output by iteration."""
        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Test response",
            intent="search",
            follow_up_question=False
        )
        self.state.llm_outputs.append(llm_output)

        # Existing iteration
        retrieved = self.state.get_llm_output(1)
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.prompt, "Test prompt")

        # Non-existing iteration
        retrieved = self.state.get_llm_output(99)
        self.assertIsNone(retrieved)

    def test_get_recent_conversation_history(self):
        """Get recent conversation history with limits."""
        for i in range(6):
            self.state.add_conversation_entry(f"Message {i}", f"Response {i}")

        # Default limit (4)
        recent = self.state.get_recent_conversation_history()
        self.assertEqual(len(recent), 4)
        self.assertEqual(recent[0].user, "Message 2")
        self.assertEqual(recent[-1].user, "Message 5")

        # Custom limit
        recent = self.state.get_recent_conversation_history(max_entries=2)
        self.assertEqual(len(recent), 2)
        self.assertEqual(recent[0].user, "Message 4")
        self.assertEqual(recent[1].user, "Message 5")

        # Fewer entries than limit
        short_state = AgentState(conversation_id="short", query="test")
        short_state.add_conversation_entry("Only message")
        recent = short_state.get_recent_conversation_history(max_entries=4)
        self.assertEqual(len(recent), 1)


class TestLLMOutput(unittest.TestCase):
    """Tests for LLMOutput model functionality."""

    def test_llm_output_to_dict(self):
        """LLMOutput converts to a dict including parsed response details."""
        agent_response = AgentResponse(
            intent=Intent.causal_impact,
            follow_up_question=False,
            thought="I need to search",
            action=Action(name="search", reason="Find papers"),
            answer=None
        )

        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Test response",
            intent="search",
            follow_up_question=False,
            parsed_response=agent_response,
            thinking_time_seconds=1.5,
            action_time_seconds=2.3,
            input_tokens=100,
            output_tokens=50
        )

        result = llm_output.to_dict()
        self.assertEqual(result["iteration"], 1)
        self.assertEqual(result["prompt"], "Test prompt")
        self.assertEqual(result["intent"], "search")
        self.assertEqual(result["thought"], "I need to search")
        self.assertEqual(result["action"], agent_response.action)
        self.assertIsNone(result["answer"])
        self.assertEqual(result["thinking_time_seconds"], 1.5)
        self.assertEqual(result["action_time_seconds"], 2.3)
        self.assertEqual(result["input_tokens"], 100)
        self.assertEqual(result["output_tokens"], 50)

    def test_llm_output_to_dict_no_parsed_response(self):
        """When parsing failed, defaults are used in the dict."""
        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Invalid response",
            intent="unknown",
            follow_up_question=False,
            parsed_response=None
        )

        result = llm_output.to_dict()
        self.assertEqual(result["thought"], "Failed to parse response")
        self.assertIsNone(result["action"])
        self.assertIsNone(result["answer"])


if __name__ == "__main__":
    unittest.main()
