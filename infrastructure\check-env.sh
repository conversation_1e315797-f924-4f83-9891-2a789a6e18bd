#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Fetch consolidated environment secrets from Google Cloud Secret Manager and convert to YAML format"
            echo "Environment Variables:"
            echo "  TARGET       Required. Must be one of: development, experimental, testing, production"
            echo "  SERVICE      Required. Service name for environment extraction"
            echo ""
            echo "Notes:"
            echo "  - Fetches from consolidated secret: impactai-services-env-file"
            echo "  - Combines shared environment variables with service-specific ones"
            echo "  - Uses service-based organization: [service.<SERVICE>.<TARGET>]"
            echo "  - Output file: ${SCRIPT_DIR}/env.\${SERVICE}.tmp.yml"
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly OUTPUT_ENV_FILE="${SCRIPT_DIR}/env.${SERVICE}.tmp.yml"

# Custom cleanup function for this script
env_cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Script failed with exit code $exit_code"
        if [ "$DRY_RUN" != true ] && [ -f "$OUTPUT_ENV_FILE" ]; then
            log_info "Cleaning up incomplete env file: $OUTPUT_ENV_FILE"
            rm -f "$OUTPUT_ENV_FILE"
        fi
    fi
    exit $exit_code
}

# Set up cleanup trap
setup_cleanup env_cleanup

# Function to validate environment variables
validate_environment() {
    log_info "Validating environment variables..."

    require_env_var TARGET "TARGET" || exit 1
    require_env_var SERVICE "SERVICE" || exit 1

    # shellcheck disable=SC2153
    validate_enum "$TARGET" "TARGET" "development" "experimental" "testing" "production" || exit 1

    log_success "Environment variables validated"
}

# Function to get the consolidated secret name
get_secret_name() {
    # Always use the same consolidated secret file
    echo "impactai-services-env-file"
}

# Function to fetch consolidated secrets from Google Cloud Secret Manager
fetch_secrets() {
    local secret_name
    local secret_content

    secret_name=$(get_secret_name)

    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would fetch consolidated secret: $secret_name" >&2
        log_success "[DRY RUN] Secret fetch validation completed" >&2
        return 0
    fi

    log_info "Fetching consolidated secret: $secret_name" >&2

    # Capture the secret content separately from logging
    if ! secret_content=$(gcloud secrets versions access latest --secret="$secret_name" 2>/dev/null); then
        log_error "Failed to fetch consolidated secret: $secret_name" >&2
        log_info "Please verify that:" >&2
        log_info "  - The secret 'impactai-services-env-file' exists in Google Cloud Secret Manager" >&2
        log_info "  - You have the necessary permissions to access it" >&2
        log_info "  - The secret contains valid INI format with environment configuration" >&2
        exit 1
    fi

    log_success "Consolidated secret fetched successfully" >&2

    # Output only the secret content to stdout
    echo "$secret_content"
}

# Function to parse INI-style sections and extract environment variables
parse_env_section() {
    local secret_content="$1"
    local section_name="$2"
    local output_file="$3"
    local count=0

    # Extract lines for the specific section
    local in_section=false
    local section_pattern="^\[$section_name\]$"

    while IFS= read -r line; do
        # Skip empty lines and comments
        [[ "$line" =~ ^[[:space:]]*$ ]] && continue
        [[ "$line" =~ ^[[:space:]]*# ]] && continue

        # Check if we're entering our target section
        if [[ "$line" =~ $section_pattern ]]; then
            in_section=true
            continue
        fi

        # Check if we're entering a different section (exit our section)
        if [[ "$line" =~ ^\[.*\]$ ]] && [ "$in_section" = true ]; then
            break
        fi

        # If we're in our section and this is a KEY=VALUE line
        if [ "$in_section" = true ] && [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
            local env_name="${BASH_REMATCH[1]}"
            local env_value="${BASH_REMATCH[2]}"

            # Trim whitespace from key and value
            env_name=$(echo "$env_name" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            env_value=$(echo "$env_value" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

            # Escape double quotes in the value for proper YAML formatting
            env_value="${env_value//\"/\\\"}"

            echo "$env_name: \"$env_value\"" >> "$output_file"
            count=$((count + 1))
        fi
    done <<< "$secret_content"

    echo $count
}

# Function to extract and convert environment variables to YAML format
convert_to_yaml() {
    local secret_content="$1"
    local target="$TARGET"
    local service="$SERVICE"

    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would extract $service environment for $target and convert to YAML"
        log_info "[DRY RUN] Output file would be: $OUTPUT_ENV_FILE"
        log_success "[DRY RUN] YAML conversion validation completed"
        return 0
    fi

    log_info "Extracting environment variables for service '$service' in '$target' environment..."
    log_info "Output file: $OUTPUT_ENV_FILE"

    # Validate that we have content
    if [[ -z "$secret_content" ]]; then
        log_error "Empty secret content received"
        exit 1
    fi

    # Check if service shared section exists
    local shared_section="service.shared.$target"
    if ! echo "$secret_content" | grep -q "^\[$shared_section\]"; then
        log_error "Service shared section for '$target' environment not found in consolidated secret"
        log_info "Available environments:"
        echo "$secret_content" | grep -o '^\[service\.shared\.\([^]]*\)\]' | sed 's/^\[service\.shared\.\([^]]*\)\]/  - \1/' | sort -u
        exit 1
    fi

    # Check if service section exists in target environment
    local service_section="service.$service.$target"
    if ! echo "$secret_content" | grep -q "^\[$service_section\]"; then
        log_error "Service '$service' not found in '$target' environment"
        log_info "Available services in '$target':"
        echo "$secret_content" | grep "^\[service\.[^.]*\.$target\]" | sed "s/^\[service\.\([^.]*\)\.$target\]/  - \1/"
        exit 1
    fi

    # Ensure the output directory exists
    local output_dir
    output_dir=$(dirname "$OUTPUT_ENV_FILE")
    if [[ ! -d "$output_dir" ]]; then
        log_info "Creating output directory: $output_dir"
        mkdir -p "$output_dir" || {
            log_error "Failed to create output directory: $output_dir"
            exit 1
        }
    fi

    # Clear the output file if it exists
    true > "$OUTPUT_ENV_FILE" || {
        log_error "Failed to create/clear output file: $OUTPUT_ENV_FILE"
        exit 1
    }

    local processed_count=0

    # Extract shared environment variables first
    local shared_count
    shared_count=$(parse_env_section "$secret_content" "$shared_section" "$OUTPUT_ENV_FILE")
    processed_count=$((processed_count + shared_count))

    if [ $shared_count -gt 0 ]; then
        log_info "Added $shared_count shared environment variables from [$shared_section]"
    fi

    # Extract service-specific environment variables
    local service_count
    service_count=$(parse_env_section "$secret_content" "$service_section" "$OUTPUT_ENV_FILE")
    processed_count=$((processed_count + service_count))

    if [ $service_count -gt 0 ]; then
        log_info "Added $service_count service-specific environment variables from [$service_section]"
    fi

    if [ $processed_count -eq 0 ]; then
        log_error "No environment variables found for service '$service' in '$target' environment"
        log_info "Please verify the consolidated secret contains configuration for this service"
        exit 1
    fi

    log_success "Converted $processed_count environment variables to YAML format"
}

# Function to validate output file
validate_output() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would validate output file"
        log_success "[DRY RUN] Output validation completed"
        return 0
    fi

    if [[ ! -f "$OUTPUT_ENV_FILE" ]]; then
        log_error "Output file was not created: $OUTPUT_ENV_FILE"
        exit 1
    fi

    local file_size
    file_size=$(wc -l < "$OUTPUT_ENV_FILE")

    if [ "$file_size" -eq 0 ]; then
        log_error "Output file is empty: $OUTPUT_ENV_FILE"
        exit 1
    fi

    log_success "Output file validated: $file_size lines written to $OUTPUT_ENV_FILE"
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        log_info "Starting consolidated environment configuration validation (DRY RUN MODE)"
    else
        log_info "Starting consolidated environment configuration generation"
    fi

    log_info "Target environment: ${TARGET:-'<not set>'}"
    log_info "Service: ${SERVICE:-'<not set>'}"
    log_info "Using consolidated secret: impactai-services-env-file"
    log_info "Using service-based organization: [service.<SERVICE>.<TARGET>]"

    # Validation and pre-flight checks
    validate_environment
    check_gcloud_auth

    # Fetch consolidated secrets
    local secret_content
    secret_content=$(fetch_secrets)

    # Extract environment variables and convert to YAML format
    convert_to_yaml "$secret_content"

    # Validate output
    validate_output

    if [ "$DRY_RUN" = true ]; then
        log_success "Consolidated environment configuration validation completed successfully!"
    else
        log_success "Consolidated environment configuration generated successfully!"
        log_info "Environment variables extracted for service '$SERVICE' in '$TARGET' environment"
        log_info "Output: $OUTPUT_ENV_FILE"
    fi
}

# Run main function
main "$@"
