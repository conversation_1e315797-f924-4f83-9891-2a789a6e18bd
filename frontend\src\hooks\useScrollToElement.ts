import { useEffect, RefObject } from "react";

interface UseScrollToElementProps {
    sectionRefs: RefObject<HTMLElement[]>;
    trigger: boolean;
    lastScrolledIndex: number;
    onScrollEnd: (index: number) => void;
}

const useScrollToElement = ({
    sectionRefs,
    trigger,
    lastScrolledIndex,
    onScrollEnd,
}: UseScrollToElementProps) => {
    useEffect(() => {
        if (trigger) {
            const nextSectionIndex = lastScrolledIndex + 1;
            const targetElement = sectionRefs.current?.[nextSectionIndex];

            if (targetElement) {
                targetElement.scrollIntoView({ behavior: "smooth", block: "start" });
                onScrollEnd(nextSectionIndex);
            }
        }
    }, [trigger, lastScrolledIndex, sectionRefs, onScrollEnd]);
};

export default useScrollToElement;
