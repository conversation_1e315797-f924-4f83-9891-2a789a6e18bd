import { useState, useLayoutEffect } from 'react';

const useScrollbarWidth = (): number => {
  const [scrollbarWidth, setScrollbarWidth] = useState(0);

  useLayoutEffect(() => {
    const outer = document.createElement('div');
    outer.style.visibility = 'hidden';
    outer.style.overflow = 'scroll';
    outer.style.msOverflowStyle = 'scrollbar';
    document.body.appendChild(outer);

    const inner = document.createElement('div');
    outer.appendChild(inner);

    const widthNoScroll = outer.offsetWidth;
    const widthWithScroll = inner.offsetWidth;

    setScrollbarWidth(widthNoScroll - widthWithScroll);

    document.body.removeChild(outer);
  }, []);

  return scrollbarWidth;
};

export default useScrollbarWidth;