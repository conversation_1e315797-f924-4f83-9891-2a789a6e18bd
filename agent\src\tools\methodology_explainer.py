"""Methodology explainer tool for explaining the agent's methodology."""

import logging
from typing import Any, Dict

from jinja2 import Template
from pydantic import BaseModel

from src.tools.base import Tool
from src.tools.settings import MethodologySettings

logger = logging.getLogger(__name__)


class MethodologyExplanation(BaseModel):
    """Result of a methodology explanation."""

    text: str


class MethodologyExplainer(Tool):
    """Tool for explaining ImpactAI's methodology using predefined documentation."""

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the methodology explainer tool."""
        super().__init__(
            name="methodology_explainer",
            description="Explains the methodology behind ImpactAI's data collection, processing, analysis, and calculations",
            func=self.run,
            arguments=[("query", "str")],
            outputs="MethodologyExplanation(text: str)",
            config=config,
        )
        logger.info("Initializing MethodologyExplainer tool")
        self.settings = MethodologySettings()
        self.methodology_text = self._load_methodology_texts()
        self.prompt_template = self._load_prompt_template()

    def _load_methodology_texts(self) -> str:
        """Load and combine the methodology texts from all appendix files."""
        combined_text = []
        for file_path in self.settings.full_methodology_paths:
            logger.info(f"Loading methodology text from: {file_path}")
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    combined_text.append(content)
                    logger.info(
                        f"Successfully loaded methodology text from {file_path} ({len(content)} characters)"
                    )
            except Exception as e:
                logger.error(f"Failed to load methodology text from {file_path}: {e}")
                combined_text.append(
                    f"Error: Methodology text could not be loaded from {file_path}."
                )

        return "\n\n---\n\n".join(combined_text)

    def _load_prompt_template(self) -> Template:
        """Load the prompt template from the prompts file."""
        prompt_file = self.settings.prompt_path
        logger.info(f"Loading prompt template from: {prompt_file}")
        try:
            with open(prompt_file, "r", encoding="utf-8") as f:
                content = f.read()
                logger.info(
                    f"Successfully loaded prompt template ({len(content)} characters)"
                )
                return Template(content)
        except Exception as e:
            logger.error(f"Failed to load prompt template: {e}")
            return Template("Error: Prompt template could not be loaded.")

    async def run(self, query: str) -> MethodologyExplanation:
        """
        Generate a methodology explanation based on the user's query.

        Args:
            query: The user's methodology-related question

        Returns:
            MethodologyExplanation containing the response
        """
        logger.info(f"Generating methodology explanation for query: {query}")

        # Render the prompt template using Jinja2
        prompt = self.prompt_template.render(
            methodology_text=self.methodology_text, query=query
        )

        logger.info("Sending prompt to LLM")
        llm_response = await self.llm.generate(prompt)
        logger.info(f"Received response from LLM ({len(llm_response.text)} characters)")

        return MethodologyExplanation(text=llm_response.text)
