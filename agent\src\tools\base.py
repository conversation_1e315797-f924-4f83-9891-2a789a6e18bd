"""
Refactored `Tool` base class.

* Guarantees every tool output has a `metadata` dict with
  `prompt_tokens`, `completion_tokens`, and `thoughts_token_count` (default 0).
* Fixes the stale `_arguments` attribute reference.
* Consolidates logging under `tool.<tool‑name>`.

Down‑stream tools can stay unchanged—their return objects just need an
attribute `metadata` (or be a plain dict).  The helper `_normalise_metadata`
will patch in missing keys so the agent logic can safely read them.
"""

from __future__ import annotations

import logging
from typing import Any, Callable, Dict, List, Sequence, Tuple

from src.agent.config import AgentConfig


class Tool:
    """Common ABC for all callable tools.

    Sub‑classes provide a coroutine `func` that does the actual work.
    The wrapper ensures a *uniform* `metadata` structure on the result.
    """

    _METADATA_KEYS = ("prompt_tokens", "completion_tokens", "thoughts_token_count")

    def __init__(
        self,
        name: str,
        description: str,
        func: Callable[..., Any],
        arguments: List[Tuple[str, str]],
        outputs: Sequence[Tuple[str, str]],
        config: Dict[str, Any] | None = None,
    ) -> None:
        self.name = name
        self.description = description
        self.func = func  # coroutine or regular callable
        self.arguments = arguments
        self.outputs = outputs
        self.config = AgentConfig.from_dict(config or {})

        self.logger = logging.getLogger(f"tool.{self.name}")
        self.llm = None  # some tools lazily attach an LLM client

    # ------------------------------------------------------------------
    # Public helpers
    # ------------------------------------------------------------------

    def to_string(self) -> str:  # noqa: D401
        """Return a human‑readable summary suitable for prompts."""
        args_str = ", ".join(f"{name}: {typ}" for name, typ in self.arguments)
        outs_str = ", ".join(f"{name}: {typ}" for name, typ in self.outputs)
        return (
            f"Tool Name: {self.name}\n"
            f"Description: {self.description}\n"
            f"Arguments: {args_str}\n"
            f"Outputs: {outs_str}\n"
        )
    

    # ------------------------------------------------------------------
    # Main entry‑point
    # ------------------------------------------------------------------

    async def __call__(self, *args: Any, **kwargs: Any) -> Any:  # noqa: D401
        """Execute the wrapped `func` and normalise its metadata."""
        result = (
            await self.func(*args, **kwargs)
            if callable(self.func) else None
        )
        self._normalise_metadata(result)
        return result

    # ------------------------------------------------------------------
    # Internal utilities
    # ------------------------------------------------------------------

    def _normalise_metadata(self, result: Any) -> None:  # noqa: D401
        """Ensure token‑count keys exist on the result's `metadata`."""
        if result is None:
            return

        # Case 1 – pydantic / dataclass with a `metadata` attribute
        if hasattr(result, "metadata"):
            md = getattr(result, "metadata") or {}
            if not isinstance(md, dict):
                self.logger.warning(
                    "Result.metadata is not a dict on %s (type=%s)",
                    self.name,
                    type(md),
                )
                return
            for key in self._METADATA_KEYS:
                md.setdefault(key, 0)
            setattr(result, "metadata", md)
            return

        # Case 2 – plain dict
        if isinstance(result, dict):
            result.setdefault("metadata", {})
            for key in self._METADATA_KEYS:
                result["metadata"].setdefault(key, 0)
            return

        # Case 3 – unsupported object; we log once
        self.logger.warning(
            "Tool %s returned unsupported type %s; cannot attach metadata",
            self.name,
            type(result),
        )
