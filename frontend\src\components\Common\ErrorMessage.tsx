import { useState } from 'react';
import { Alert, Typography, IconButton } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import WarningIcon from '@mui/icons-material/Warning';
import CloseIcon from '@mui/icons-material/Close';

const ErrorMessage = ({ error, onClose, severity = 'error' }) => {
    const [visible, setVisible] = useState(true);
    const theme = useTheme();

    const handleClose = () => {
        setVisible(false);
        if (onClose) {
            onClose();
        }
    };

    return visible ? (
        <Alert
            severity={severity}
            variant="outlined"
            icon={
                severity === 'error' ? (
                    <WarningIcon sx={{ color: theme.palette.error.main }} />
                ) : false
            }
            action={
                <IconButton
                    aria-label="close"
                    color="inherit"
                    size="small"
                    onClick={handleClose}
                >
                    <CloseIcon fontSize="inherit" />
                </IconButton>
            }
            sx={{
                display: 'flex',
                alignItems: 'center',
                borderRadius: severity === 'error' ? '4px' : '8px',
                backgroundColor: severity === 'error' ? '#FDEDED' : theme.palette.background.paper,
                padding: '6px 16px',
                border: 0,
                width: '100%',
                color: severity === 'error' ? '#5F2120' : theme.palette.text.secondary,
                "& .MuiAlert-message": {
                    padding: '0px',
                }
            }}
        >
            <Typography variant="subtitle2" fontWeight="medium">
                {error}
            </Typography>
        </Alert>
    ) : null;
};

export default ErrorMessage;
