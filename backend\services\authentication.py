from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
import jwt
import bcrypt
from dataclasses_json import dataclass_json
from dataclasses import dataclass
from database.models import User
from typing import Optional
from sqlalchemy import select
from services.emails import send_password_reset
from database.userdata import get_userdata_db_session
from utils.measure import measure_async_time, measure_time

secret = "some-secret"
algorithm = "HS256"


@dataclass_json
@dataclass
class TokenData:
    user_id: str


class AuthenticationError(HTTPException):
    status_code = 401
    detail = "Unauthorized"


def jwt_encode(data):
    return jwt.encode(data, secret, algorithm=algorithm)


def jwt_decode(data):
    try:
        return jwt.decode(data, secret, algorithms=[algorithm])
    except jwt.ExpiredSignatureError:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
        )
    except jwt.InvalidTokenError:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        )


@measure_async_time
async def fetch_user_by_email(email: str) -> Optional[User]:
    """Fetch user by email."""
    async with get_userdata_db_session() as session:
        result = await session.execute(select(User).where(User.email == email))
        return result.scalars().first()


@measure_async_time
async def create_user(email: str, password: str) -> User:
    """Create a new user with an auto-generated ID."""
    async with get_userdata_db_session() as session:

        existing_user = await fetch_user_by_email(email)
        if existing_user:
            raise AuthenticationError(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User already exists",
            )

        password_hash = bcrypt.hashpw(
            password.encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8")

        new_user = User(email=email, password_hash=password_hash)

        session.add(new_user)
        await session.commit()
        await session.refresh(new_user)
        return new_user


@measure_async_time
async def fetch_user_by_id(id: str) -> User | None:
    """Fetch a user by ID."""
    async with get_userdata_db_session() as session:
        result = await session.execute(select(User).where(User.id == id))
        return result.scalars().first()


@measure_async_time
async def authenticate(email: str, password: str) -> Optional[User]:
    """Authenticate user by email and password."""
    user = await fetch_user_by_email(email)
    if not user:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials"
        )

    if not verify_password(password, user.password_hash):
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid password"
        )

    return user


@measure_async_time
async def handle_forgot_password(email: str) -> str:
    """Handle forgot password."""
    user = await fetch_user_by_email(email)
    if not user:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="User doesnt exist."
        )

    token = jwt_encode({"user_id": str(user.id)})

    await send_password_reset(email, token)


def hash_password(password: str) -> str:
    """Hash a password."""
    return bcrypt.hashpw(password.encode("utf-8"), bcrypt.gensalt()).decode("utf-8")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hashed password."""
    return bcrypt.checkpw(
        plain_password.encode("utf-8"), hashed_password.encode("utf-8")
    )


@measure_async_time
async def change_password(id: str, password: str) -> User:
    """Change password."""
    async with get_userdata_db_session() as session:
        user = await fetch_user_by_id(id)
        if not user:
            raise AuthenticationError(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User doesnt exist.",
            )

        user.password_hash = hash_password(password)
        session.add(user)
        await session.commit()
        await session.refresh(user)
        return user


@measure_time
def user_id_from_token(token: str) -> str:
    """Extract user ID from JWT token."""
    data = jwt_decode(token)
    user_id = data.get("user_id")
    if not user_id:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        )
    return user_id


@measure_async_time
async def handle_change_password(token: str, password: str) -> User:
    """Handle change password."""
    user_id = user_id_from_token(token)
    user = await change_password(
        id=user_id,
        password=password,
    )

    authed_user = await authenticate(
        email=user.email,
        password=password,
    )
    return authed_user
