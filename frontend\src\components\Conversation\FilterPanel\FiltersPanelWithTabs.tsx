import React, { useState, SyntheticEvent, useEffect } from 'react';
import {
    Box,
    Typography,
    Divider,
    Select,
    MenuItem,
    Checkbox,
    ListItemText,
    IconButton,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CloseIcon from '@mui/icons-material/Close';
import { FilterOptions, FilterState, FilterField } from '../../../types/ConversationTypes';
import FilterSlider from './FilterSlider';
import FilterChipList from './FilterChipList';
import FilterTabs from './FilterTabs';

interface FiltersPanelWithTabsProps {
    theme: any;
    onFiltersChange: (filters: FilterState) => void;
    filterOptions: FilterOptions;
    activeFilters: FilterState;
    activeFilterTab: 'General' | 'Intervention' | 'Outcome';
    onFilterTabChange: (tab: 'General' | 'Intervention' | 'Outcome') => void;
}

const FiltersPanelWithTabs: React.FC<FiltersPanelWithTabsProps> = ({
    theme,
    onFiltersChange,
    filterOptions,
    activeFilters,
    activeFilterTab,
    onFilterTabChange,
}) => {
    const [activeGeographies, setActiveGeographies] = useState<string[]>(activeFilters?.countries || []);
    const [isGeographyMenuOpen, setIsGeographyMenuOpen] = useState(false);

    useEffect(() => {
        if (activeFilters?.countries) {
            setActiveGeographies(activeFilters.countries);
        } else {
            setActiveGeographies([]);
        }
    }, [activeFilters?.countries]);

    const handleGeographyChange = (event: any) => {
        const newGeographies = event.target.value as string[];
        setActiveGeographies(newGeographies);
        onFiltersChange({
            ...activeFilters,
            countries: newGeographies,
        });
    };

    const handleClearGeography = (event: SyntheticEvent) => {
        event.stopPropagation();
        setActiveGeographies([]);
        onFiltersChange({
            ...activeFilters,
            countries: [],
        });
    };

    const labelStyles = {
        color: 'rgba(0, 51, 128, 0.70)',
        fontFeatureSettings: "'liga' off, 'clig' off",
        fontFamily: 'Roboto',
        fontSize: '12px',
        fontStyle: 'normal',
        fontWeight: 600,
        lineHeight: '12px',
        letterSpacing: '0.15px',
        mb: 2,
    };

    const CustomIcon = () => {
        const iconButtonStyles = {
            width: '30px',
            height: '30px',
            padding: '5px',
            borderRadius: '100px',
            color: theme.palette.action.disabled,
        };

        if (activeGeographies.length > 0) {
            return (
                <IconButton sx={iconButtonStyles} onClick={handleClearGeography}>
                    <CloseIcon
                        sx={{
                            fontSize: '16px',
                            color: theme.palette.primary.main,
                        }}
                    />
                </IconButton>
            );
        }

        return (
            <IconButton sx={iconButtonStyles}>
                {isGeographyMenuOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
        );
    };

    const tabKeys = filterOptions?.tabs ? Object.keys(filterOptions.tabs) : [];

    const handleChange = (event: React.SyntheticEvent, newValue: string) => {
        onFilterTabChange(newValue as 'General' | 'Intervention' | 'Outcome');
    };

    const handleFilterChange = (filterKey: string, value: string | number[]) => {
        const filterConfig = filterOptions[filterKey] as FilterField;

        if (filterConfig.type === 'slider') {
            onFiltersChange({ ...activeFilters, [filterKey]: value });
        } else if (filterConfig.type === 'chips' || filterConfig.type === 'checkboxes') {
            const currentValues = activeFilters?.[filterKey as keyof FilterState] as string[] || [];
            const newValues = currentValues.includes(value as string)
                ? currentValues.filter(item => item !== value)
                : [...currentValues, value as string];
            onFiltersChange({ ...activeFilters, [filterKey]: newValues });
        }
    };

    const getTabCounts = () => {
        const counts: { [key: string]: number } = {};
        tabKeys.forEach(tabKey => {
            let tabCount = 0;
            const filterKeysForTab = filterOptions.tabs?.[tabKey] || [];
            filterKeysForTab.forEach(filterKey => {
                const activeValues = activeFilters?.[filterKey as keyof FilterState];
                if (activeValues) {
                    if (Array.isArray(activeValues)) {
                        if (filterKey === 'years') {
                            const yearsConfig = filterOptions.years as FilterField;
                            if (yearsConfig.range) {
                                if (activeValues[0] !== yearsConfig.range[0] || activeValues[1] !== yearsConfig.range[1]) {
                                    tabCount += 1;
                                }
                            }
                        } else {
                            tabCount += activeValues.length;
                        }
                    }
                }
            });
            counts[tabKey] = tabCount;
        });
        return counts;
    };
    const tabCounts = getTabCounts();

    const renderFilterComponent = (filterKey: string) => {
        const filterConfig = filterOptions[filterKey] as FilterField;

        if (filterKey === 'countries') {
            return (
                <Box key={filterKey}>
                    <Typography sx={labelStyles}>{filterConfig.label}</Typography>
                    <Select
                        multiple
                        displayEmpty
                        value={activeGeographies}
                        onChange={handleGeographyChange}
                        onOpen={() => setIsGeographyMenuOpen(true)}
                        onClose={() => setIsGeographyMenuOpen(false)}
                        IconComponent={CustomIcon}
                        renderValue={(selected) =>
                            selected.length === 0 ? (
                                <Typography sx={{ ...labelStyles, mb: 0 }}>Filter Geographies</Typography>
                            ) : (
                                <Typography sx={{ fontFamily: 'Roboto', fontSize: '13px', fontWeight: 400, lineHeight: '18px', letterSpacing: '0.16px', color: theme.palette.text.primary, padding: '0 4px', }}>
                                    {selected.join(', ')}
                                </Typography>
                            )
                        }
                        MenuProps={{ PaperProps: { sx: { maxHeight: '206px', padding: '3px 4px', borderRadius: '0 0 12px 12px', border: `1px solid ${theme.palette.secondary.main}`, borderTop: 'none', background: 'none', boxShadow: 'none', }, }, sx: { marginTop: '-1px', '& .MuiList-root': { p: 0, }, }, }}
                        sx={{ display: 'flex', height: '24px', padding: '0 4px', justifyContent: 'space-between', alignItems: 'center', alignSelf: 'stretch', borderRadius: isGeographyMenuOpen ? '16px 16px 0 0' : '16px', border: `1px solid ${activeGeographies.length > 0 || isGeographyMenuOpen ? theme.palette.secondary.main : 'rgba(0, 51, 128, 0.70)'}`, backgroundColor: activeGeographies.length > 0 || isGeographyMenuOpen ? 'transparent' : 'transparent', '&:hover': { backgroundColor: 'transparent', }, '&.Mui-focused': { backgroundColor: 'transparent', border: `1px solid ${theme.palette.secondary.main}`, }, '& .MuiSelect-select': { display: 'flex', flexWrap: 'wrap', gap: '4px', height: '18px', minHeight: '18px !important', paddingTop: '3px', paddingBottom: '3px', }, '& .MuiOutlinedInput-notchedOutline': { border: 'none', }, }}
                    >
                        {filterConfig.options?.map((geo, index) => (
                            <MenuItem key={geo} value={geo} sx={{ display: 'flex', minHeight: '36px', padding: '0 4px', alignItems: 'center', backgroundColor: 'transparent', borderTop: index !== 0 ? '1px solid #D4E4FC' : 'none', '&:hover': { backgroundColor: 'transparent', }, '& .MuiListItemText-root': { padding: '0 4px', }, }}>
                                <Checkbox checked={activeGeographies.indexOf(geo) > -1} sx={{ padding: '0px', '& .MuiSvgIcon-root': { width: '20px', height: '20px', }, }} />
                                <ListItemText primary={geo} sx={{ '& .MuiListItemText-primary': { fontSize: '13px', }, }} />
                            </MenuItem>
                        ))}
                    </Select>
                </Box>
            );
        }

        switch (filterConfig.type) {
            case 'slider':
                return (
                    <FilterSlider
                        key={filterKey}
                        label={filterConfig.label}
                        min={filterConfig.range?.[0] || 0}
                        max={filterConfig.range?.[1] || 0}
                        activeRange={activeFilters?.[filterKey as keyof FilterState] as number[] || []}
                        onChangeCommitted={(value) => handleFilterChange(filterKey, value)}
                    />
                );
            case 'chips':
                return (
                    <FilterChipList
                        key={filterKey}
                        label={filterConfig.label}
                        options={filterConfig.options || []}
                        activeValues={activeFilters?.[filterKey as keyof FilterState] as string[] || []}
                        onChipChange={(value) => handleFilterChange(filterKey, value)}
                    />
                );
            default:
                return null;
        }
    };

    const renderTabContent = () => {
        const filterKeysForTab = filterOptions.tabs?.[activeFilterTab] || [];
        return (
            <Box sx={{ p: 2 }}>
                {filterKeysForTab.map(filterKey => (
                    <React.Fragment key={filterKey}>
                        {renderFilterComponent(filterKey)}
                        {filterKey !== filterKeysForTab[filterKeysForTab.length - 1] && <Divider sx={{ my: 2 }} />}
                    </React.Fragment>
                ))}
            </Box>
        );
    };

    return (
        <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
            <FilterTabs
                tabKeys={tabKeys}
                activeTab={activeFilterTab}
                tabCounts={tabCounts}
                onTabChange={handleChange}
            />
            <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
                {renderTabContent()}
            </Box>
        </Box>
    );
};

export default FiltersPanelWithTabs;