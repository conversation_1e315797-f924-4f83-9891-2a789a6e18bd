#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
IMPORT_LOCAL=true
SKIP_DOWNLOAD=false
FORCE_CONFIRM=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-import)
            IMPORT_LOCAL=false
            shift
            ;;
        --skip-download)
            SKIP_DOWNLOAD=true
            shift
            ;;
        --force)
            FORCE_CONFIRM=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Sync development database to local init directory and MySQL"
            echo "  --dry-run        Perform validation checks only, skip actual sync"
            echo "  --skip-import    Skip importing to local MySQL database (download only)"
            echo "  --skip-download  Skip download, use existing local database file"
            echo "  --force          Skip confirmation prompts (auto-confirm database replacement)"
            echo "  --help           Show this help message"
            echo ""
            echo "Description:"
            echo "  This script exports the development database, downloads it to the"
            echo "  local backend/database/init directory with a timestamped filename,"
            echo "  and imports it to a local MySQL database by default."
            echo "  Use --skip-import to only download without importing to MySQL."
            echo ""
            echo "Examples:"
            echo "  # Download and import to MySQL (default behavior)"
            echo "  $0"
            echo ""
            echo "  # Download only, skip MySQL import"
            echo "  $0 --skip-import"
            echo ""
            echo "  # Skip download, just import existing file"
            echo "  $0 --skip-download"
            echo ""
            echo "  # Import without confirmation prompts"
            echo "  $0 --force"
            echo ""
            echo "Environment Variables (required for MySQL import):"
            echo "  MYSQL_HOST      Must be 'localhost' or '127.0.0.1' for safety"
            echo "  MYSQL_USER      MySQL username"
            echo "  MYSQL_PASSWORD  MySQL password"
            echo "  MYSQL_CORE_DATABASE  MySQL database name (optional, defaults to impactai-silver-db)"
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly DEVELOPMENT_INSTANCE_NAME="impactai-db"
readonly BUCKET_NAME="impactai-db"
readonly DATABASE_NAME="impactai-silver-db"
readonly EXPORT_FILE="$(create_timestamped_filename "silverdb")"
readonly LOCAL_INIT_DIR="$SCRIPT_DIR/../backend/database/init"

# Local database file will be determined later based on --skip-download flag
LOCAL_DATABASE_FILE=""

# Custom cleanup function for this script
db_cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Script failed with exit code $exit_code"
        if [ "$DRY_RUN" != true ]; then
            log_info "You may need to manually clean up the export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        fi
    fi
    exit $exit_code
}

# Set up cleanup trap
setup_cleanup db_cleanup

# Function to check if required instance exists
check_instance() {
    log_info "Checking if development Cloud SQL instance exists..."
    
    check_sql_instance "$DEVELOPMENT_INSTANCE_NAME" "Development instance"
    
    log_success "Development instance is accessible"
}

# Function to check required commands
check_required_commands() {
    log_info "Checking required commands..."
    
    local missing_commands=()
    
    # Check commands based on what operations will be performed
    if [ "$SKIP_DOWNLOAD" != true ]; then
        if ! command -v gcloud >/dev/null 2>&1; then
            missing_commands+=("gcloud")
        fi
        if ! command -v gsutil >/dev/null 2>&1; then
            missing_commands+=("gsutil")
        fi
    fi
    
    if [ "$IMPORT_LOCAL" = true ]; then
        if ! command -v mysql >/dev/null 2>&1; then
            missing_commands+=("mysql")
        fi
    fi
    
    if [ ${#missing_commands[@]} -gt 0 ]; then
        log_error "Missing required commands: ${missing_commands[*]}"
        if [[ " ${missing_commands[*]} " =~ " gcloud " ]] || [[ " ${missing_commands[*]} " =~ " gsutil " ]]; then
            log_info "Please install Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
        fi
        if [[ " ${missing_commands[*]} " =~ " mysql " ]]; then
            log_info "Please install MySQL client or ensure it's in your PATH"
        fi
        return 1
    fi
    
    log_success "All required commands are available"
    return 0
}

# Function to validate script environment
validate_script_environment() {
    log_info "Validating script environment..."
    
    # Check if we can resolve the LOCAL_INIT_DIR path
    local resolved_dir
    if ! resolved_dir=$(realpath "$LOCAL_INIT_DIR" 2>/dev/null); then
        # Try to resolve parent directory
        local parent_dir=$(dirname "$LOCAL_INIT_DIR")
        if [ ! -d "$parent_dir" ]; then
            log_error "Parent directory does not exist: $parent_dir"
            log_info "Please ensure the project structure is correct"
            return 1
        fi
    fi
    
    # Validate timestamp generation works
    local test_timestamp
    if ! test_timestamp=$(date +%Y%m%d-%H%M%S 2>/dev/null); then
        log_error "Failed to generate timestamp"
        return 1
    fi
    
    log_success "Script environment validation completed"
    return 0
}

# Function to determine local database file path
determine_database_file() {
    if [ "$SKIP_DOWNLOAD" = true ]; then
        log_info "Looking for existing database file to import..."
        
        # Ensure directory exists before searching
        if [ ! -d "$LOCAL_INIT_DIR" ]; then
            log_error "Cannot find existing files - init directory does not exist: $LOCAL_INIT_DIR"
            log_info "Remove --skip-download flag to download a new database file"
            return 1
        fi
        
        # Find the most recent silverdb file when skipping download
        LOCAL_DATABASE_FILE=$(find "$LOCAL_INIT_DIR" -name "silverdb-*.sql" -type f -exec ls -t {} + 2>/dev/null | head -n1)
        if [ -z "$LOCAL_DATABASE_FILE" ]; then
            log_error "No existing silverdb-*.sql file found in $LOCAL_INIT_DIR"
            log_info "Available files:"
            ls -la "$LOCAL_INIT_DIR"/*.sql 2>/dev/null || log_info "  No .sql files found"
            log_info "Remove --skip-download flag to download a new database file"
            return 1
        fi
        
        # Validate the file is readable
        if [ ! -r "$LOCAL_DATABASE_FILE" ]; then
            log_error "Database file is not readable: $LOCAL_DATABASE_FILE"
            return 1
        fi
        
        local file_size=$(du -h "$LOCAL_DATABASE_FILE" 2>/dev/null | cut -f1 || echo "unknown")
        log_success "Found existing database file: $(basename "$LOCAL_DATABASE_FILE") ($file_size)"
    else
        # Generate new filename with timestamp
        local timestamp=$(date +%Y%m%d-%H%M%S)
        LOCAL_DATABASE_FILE="$LOCAL_INIT_DIR/silverdb-$timestamp.sql"
        log_info "New database file will be: $(basename "$LOCAL_DATABASE_FILE")"
        
        # Check if file already exists (very unlikely but possible)
        if [ -f "$LOCAL_DATABASE_FILE" ]; then
            log_warning "File already exists: $LOCAL_DATABASE_FILE"
            log_info "Adding random suffix to avoid conflicts"
            LOCAL_DATABASE_FILE="$LOCAL_INIT_DIR/silverdb-$timestamp-$RANDOM.sql"
            log_info "Updated filename: $(basename "$LOCAL_DATABASE_FILE")"
        fi
    fi
    return 0
}

# Function to check and create local directory
check_local_directory() {
    log_info "Checking local init directory..."
    
    if [ ! -d "$LOCAL_INIT_DIR" ]; then
        log_info "Local init directory does not exist: $LOCAL_INIT_DIR"
        log_info "Creating directory..."
        
        if [ "$DRY_RUN" = true ]; then
            log_info "[DRY RUN] Would create directory: $LOCAL_INIT_DIR"
            log_success "[DRY RUN] Directory creation validation completed"
            return 0
        fi
        
        if mkdir -p "$LOCAL_INIT_DIR"; then
            log_success "Created local init directory: $LOCAL_INIT_DIR"
        else
            log_error "Failed to create local init directory: $LOCAL_INIT_DIR"
            log_info "Please check directory permissions and path validity"
            return 1
        fi
    fi
    
    if [ "$DRY_RUN" != true ]; then
        if [ ! -w "$LOCAL_INIT_DIR" ]; then
            log_error "Local init directory is not writable: $LOCAL_INIT_DIR"
            log_info "Please check directory permissions"
            return 1
        fi
    fi
    
    log_success "Local init directory is accessible and writable"
}

# Function to read and validate .env file
read_env_file() {
    local env_file="$SCRIPT_DIR/../.env"
    
    log_info "Reading .env file for MySQL configuration..."
    
    if [ ! -f "$env_file" ]; then
        log_error ".env file not found: $env_file"
        log_info "Create a .env file in the project root with MySQL configuration"
        log_info "Required variables: MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD"
        log_info "Optional: MYSQL_CORE_DATABASE (defaults to impactai-silver-db)"
        return 1
    fi
    
    if [ ! -r "$env_file" ]; then
        log_error ".env file is not readable: $env_file"
        log_info "Please check file permissions"
        return 1
    fi
    
    # Source the .env file safely
    set -a  # automatically export all variables
    if ! source "$env_file" 2>/dev/null; then
        log_error "Failed to source .env file - check for syntax errors"
        set +a
        return 1
    fi
    set +a  # stop auto-exporting
    
    # Validate required variables with detailed error messages (safe check for unbound variables)
    local missing_vars=()
    
    if [ -z "${MYSQL_HOST:-}" ]; then
        missing_vars+=("MYSQL_HOST")
    fi
    
    if [ -z "${MYSQL_USER:-}" ]; then
        missing_vars+=("MYSQL_USER")
    fi
    
    if [ -z "${MYSQL_PASSWORD:-}" ]; then
        missing_vars+=("MYSQL_PASSWORD")
    fi
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing required environment variables in .env file: ${missing_vars[*]}"
        log_info "Please add these variables to your .env file:"
        for var in "${missing_vars[@]}"; do
            case "$var" in
                "MYSQL_HOST") log_info "  MYSQL_HOST=localhost" ;;
                "MYSQL_USER") log_info "  MYSQL_USER=your_mysql_username" ;;
                "MYSQL_PASSWORD") log_info "  MYSQL_PASSWORD=your_mysql_password" ;;
            esac
        done
        return 1
    fi
    
    # Validate variable content (only if variables are set)
    if [ -n "${MYSQL_HOST:-}" ] && [[ "$MYSQL_HOST" =~ [[:space:]] ]]; then
        log_error "MYSQL_HOST contains whitespace: '$MYSQL_HOST'"
        return 1
    fi
    
    if [ -n "${MYSQL_USER:-}" ] && [[ "$MYSQL_USER" =~ [[:space:]] ]]; then
        log_error "MYSQL_USER contains whitespace: '$MYSQL_USER'"
        return 1
    fi
    
    # Default database name if not set (safe check for unbound variable)
    if [ -z "${MYSQL_CORE_DATABASE:-}" ]; then
        MYSQL_CORE_DATABASE="impactai-silver-db"
        log_info "Using default database name: $MYSQL_CORE_DATABASE"
    fi
    
    # Validate database name format (only if set)
    if [ -n "${MYSQL_CORE_DATABASE:-}" ] && ! [[ "$MYSQL_CORE_DATABASE" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        log_error "Invalid database name format: '$MYSQL_CORE_DATABASE'"
        log_info "Database name should only contain letters, numbers, underscores, and hyphens"
        return 1
    fi
    
    log_success ".env file loaded and validated successfully"
    log_info "MySQL configuration: $MYSQL_USER@$MYSQL_HOST/$MYSQL_CORE_DATABASE"
}

# Function to validate MySQL host for safety
validate_mysql_host() {
    log_info "Validating MySQL host for safety..."
    
    # Only allow localhost connections for safety
    case "$MYSQL_HOST" in
        "localhost"|"127.0.0.1"|"0.0.0.0")
            log_success "MySQL host is safe for local import: $MYSQL_HOST"
            return 0
            ;;
        *)
            log_error "MySQL host '$MYSQL_HOST' is not allowed for safety reasons"
            log_error "Only localhost, 127.0.0.1, or 0.0.0.0 are permitted"
            log_info "This prevents accidental imports to production databases"
            return 1
            ;;
    esac
}

# Function to check MySQL connection and database existence
check_mysql_connection() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would test MySQL connection..."
        log_info "[DRY RUN] Host: $MYSQL_HOST, User: $MYSQL_USER, Database: $MYSQL_CORE_DATABASE"
        log_success "[DRY RUN] MySQL connection validation completed"
        return 0
    fi
    
    log_info "Testing MySQL server connection..."
    
    # First test basic MySQL server connection
    if ! mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "Failed to connect to MySQL server"
        log_info "Please check your .env file configuration and ensure MySQL is running"
        return 1
    fi
    
    log_success "MySQL server connection successful"
    
    # Check if database exists
    log_info "Checking if database '$MYSQL_CORE_DATABASE' exists..."
    
    local db_exists=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME='$MYSQL_CORE_DATABASE';" 2>/dev/null | grep -c "$MYSQL_CORE_DATABASE")
    
    if [ "$db_exists" -gt 0 ]; then
        log_warning "Database '$MYSQL_CORE_DATABASE' already exists and will be REPLACED"
        
        # Get table count for user information
        local table_count=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -s -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$MYSQL_CORE_DATABASE';" 2>/dev/null || echo "unknown")
        log_info "Current database contains $table_count tables"
        
        # Ask for confirmation unless --force is used
        if [ "$FORCE_CONFIRM" != true ]; then
            echo ""
            echo "⚠️  WARNING: This will completely replace the existing database!"
            echo "   Database: $MYSQL_CORE_DATABASE"
            echo "   Tables: $table_count"
            echo ""
            read -p "Do you want to continue? [y/N]: " -r
            echo ""
            
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "Operation cancelled by user"
                return 1
            fi
        else
            log_info "Auto-confirming database replacement (--force flag used)"
        fi
        
        log_success "Database replacement confirmed"
    else
        log_info "Database '$MYSQL_CORE_DATABASE' does not exist - it will be created"
        
        # Create the database
        log_info "Creating database '$MYSQL_CORE_DATABASE'..."
        if mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$MYSQL_CORE_DATABASE\`;" >/dev/null 2>&1; then
            log_success "Database '$MYSQL_CORE_DATABASE' created successfully"
        else
            log_error "Failed to create database '$MYSQL_CORE_DATABASE'"
            log_info "Please check your MySQL user permissions"
            return 1
        fi
    fi
    
    return 0
}

# Function to export database
export_database() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would export from development database..."
        log_info "[DRY RUN] Export file would be: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_success "[DRY RUN] Export validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Starting export from development database..."
    log_info "Export file: gs://$BUCKET_NAME/$EXPORT_FILE"
    
    if gcloud sql export sql "$DEVELOPMENT_INSTANCE_NAME" "gs://$BUCKET_NAME/$EXPORT_FILE" \
        --database="$DATABASE_NAME" --quiet; then
        log_success "Export completed successfully"
        return 0
    else
        log_error "Export failed"
        return 1
    fi
}

# Function to check available disk space
check_disk_space() {
    log_info "Checking available disk space..."
    
    local target_dir="$LOCAL_INIT_DIR"
    if [ ! -d "$target_dir" ]; then
        target_dir=$(dirname "$LOCAL_INIT_DIR")
    fi
    
    # Get available space in KB
    local available_kb
    if available_kb=$(df "$target_dir" 2>/dev/null | tail -1 | awk '{print $4}'); then
        local available_mb=$((available_kb / 1024))
        local available_gb=$((available_mb / 1024))
        
        log_info "Available disk space: ${available_gb}GB (${available_mb}MB)"
        
        # Warn if less than 1GB available
        if [ "$available_gb" -lt 1 ]; then
            log_warning "Low disk space: ${available_mb}MB available"
            if [ "$available_mb" -lt 100 ]; then
                log_error "Insufficient disk space: ${available_mb}MB available"
                log_info "Please free up disk space before continuing"
                return 1
            fi
        fi
    else
        log_warning "Could not determine available disk space"
    fi
    
    return 0
}

# Function to download database file
download_database() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would download database file..."
        log_info "[DRY RUN] Source: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_info "[DRY RUN] Destination: $LOCAL_DATABASE_FILE"
        log_success "[DRY RUN] Download validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Downloading database file to local directory..."
    log_info "Source: gs://$BUCKET_NAME/$EXPORT_FILE"
    log_info "Destination: $LOCAL_DATABASE_FILE"
    
    # Check disk space before downloading
    if ! check_disk_space; then
        return 1
    fi
    
    # Ensure the destination directory exists
    local dest_dir=$(dirname "$LOCAL_DATABASE_FILE")
    if [ ! -d "$dest_dir" ]; then
        log_error "Destination directory does not exist: $dest_dir"
        return 1
    fi
    
    # Attempt download with progress and error handling
    log_info "Starting download (this may take a while for large databases)..."
    if gsutil -m cp "gs://$BUCKET_NAME/$EXPORT_FILE" "$LOCAL_DATABASE_FILE" 2>/dev/null; then
        # Verify the downloaded file
        if [ ! -f "$LOCAL_DATABASE_FILE" ]; then
            log_error "Download completed but file not found: $LOCAL_DATABASE_FILE"
            return 1
        fi
        
        if [ ! -s "$LOCAL_DATABASE_FILE" ]; then
            log_error "Downloaded file is empty: $LOCAL_DATABASE_FILE"
            rm -f "$LOCAL_DATABASE_FILE" 2>/dev/null
            return 1
        fi
        
        log_success "Database file downloaded successfully"
        
        # Display file information
        local file_size=$(du -h "$LOCAL_DATABASE_FILE" 2>/dev/null | cut -f1 || echo "unknown")
        local line_count=$(wc -l < "$LOCAL_DATABASE_FILE" 2>/dev/null || echo "unknown")
        log_info "Local file: $(basename "$LOCAL_DATABASE_FILE")"
        log_info "File size: $file_size"
        log_info "Lines: $line_count"
        
        # Basic validation that it looks like a SQL file
        if head -5 "$LOCAL_DATABASE_FILE" 2>/dev/null | grep -qi "sql\|mysql\|database\|create\|insert" >/dev/null; then
            log_success "File appears to be a valid SQL dump"
        else
            log_warning "File may not be a valid SQL dump - please verify manually"
        fi
        
        return 0
    else
        log_error "Download failed"
        log_info "Please check:"
        log_info "  - Google Cloud authentication (gcloud auth list)"
        log_info "  - Network connectivity"
        log_info "  - Export file exists: gs://$BUCKET_NAME/$EXPORT_FILE"
        
        # Clean up any partial download
        if [ -f "$LOCAL_DATABASE_FILE" ]; then
            log_info "Cleaning up partial download..."
            rm -f "$LOCAL_DATABASE_FILE"
        fi
        
        return 1
    fi
}

# Function to import database file to local MySQL
import_to_local_mysql() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would import database file to local MySQL..."
        log_info "[DRY RUN] Source file: $LOCAL_DATABASE_FILE"
        log_info "[DRY RUN] Target: $MYSQL_HOST:$MYSQL_CORE_DATABASE"
        log_success "[DRY RUN] Local MySQL import validation completed"
        return 0
    fi
    
    log_info "Importing database file to local MySQL..."
    log_info "Source file: $(basename "$LOCAL_DATABASE_FILE")"
    log_info "Target database: $MYSQL_HOST:$MYSQL_CORE_DATABASE"
    
    # Validate the source file
    if [ ! -f "$LOCAL_DATABASE_FILE" ]; then
        log_error "Database file not found: $LOCAL_DATABASE_FILE"
        return 1
    fi
    
    if [ ! -r "$LOCAL_DATABASE_FILE" ]; then
        log_error "Database file is not readable: $LOCAL_DATABASE_FILE"
        return 1
    fi
    
    if [ ! -s "$LOCAL_DATABASE_FILE" ]; then
        log_error "Database file is empty: $LOCAL_DATABASE_FILE"
        return 1
    fi
    
    # Show file information before import
    local file_size=$(du -h "$LOCAL_DATABASE_FILE" 2>/dev/null | cut -f1 || echo "unknown")
    local line_count=$(wc -l < "$LOCAL_DATABASE_FILE" 2>/dev/null || echo "unknown")
    log_info "File size: $file_size"
    log_info "Lines to import: $line_count"
    
    # Re-verify MySQL connection before import
    log_info "Verifying MySQL connection before import..."
    if ! mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "Lost MySQL connection before import"
        log_info "Please check if MySQL server is still running"
        return 1
    fi
    
    # Import the database file with better error handling
    log_info "Starting MySQL import (this may take a while for large databases)..."
    local import_start_time=$(date +%s)
    
    # Use a more robust import method with error checking
    if mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_CORE_DATABASE" < "$LOCAL_DATABASE_FILE" 2>/tmp/mysql_import_error.log; then
        local import_end_time=$(date +%s)
        local import_duration=$((import_end_time - import_start_time))
        
        log_success "Database imported successfully to local MySQL"
        log_info "Import completed in ${import_duration} seconds"
        
        # Show detailed statistics
        local table_count=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -s -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$MYSQL_CORE_DATABASE';" 2>/dev/null || echo "unknown")
        local row_count=$(mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -s -e "SELECT SUM(table_rows) FROM information_schema.tables WHERE table_schema='$MYSQL_CORE_DATABASE';" 2>/dev/null || echo "unknown")
        
        log_info "Import statistics:"
        log_info "  Tables: $table_count"
        log_info "  Estimated rows: $row_count"
        log_info "  Duration: ${import_duration}s"
        
        # Clean up error log if import was successful
        rm -f /tmp/mysql_import_error.log 2>/dev/null
        
        return 0
    else
        local import_end_time=$(date +%s)
        local import_duration=$((import_end_time - import_start_time))
        
        log_error "Failed to import database to MySQL (after ${import_duration}s)"
        
        # Show MySQL error details if available
        if [ -f /tmp/mysql_import_error.log ] && [ -s /tmp/mysql_import_error.log ]; then
            log_info "MySQL error details:"
            head -10 /tmp/mysql_import_error.log | while read line; do
                log_info "  $line"
            done
            rm -f /tmp/mysql_import_error.log 2>/dev/null
        fi
        
        log_info "Troubleshooting tips:"
        log_info "  - Check MySQL server logs for detailed errors"
        log_info "  - Verify MySQL user has sufficient privileges"
        log_info "  - Ensure database has enough storage space"
        log_info "  - Check if SQL file is compatible with MySQL version"
        
        return 1
    fi
}

# Function to clean up export file
cleanup_export_file() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would clean up export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_success "[DRY RUN] Cleanup validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Cleaning up export file from bucket..."
    if gsutil rm "gs://$BUCKET_NAME/$EXPORT_FILE" >/dev/null 2>&1; then
        log_success "Export file cleaned up successfully"
    else
        log_warning "Failed to clean up export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_info "You may need to clean it up manually"
    fi
}

# Function to show completion summary
show_completion_summary() {
    echo ""
    echo "==============================================="
    if [ "$DRY_RUN" = true ]; then
        echo "          🧪 DRY RUN VALIDATION SUMMARY"
        echo "==============================================="
        log_success "Database sync validation completed successfully!"
        log_info "All checks passed - script would execute without errors"
    else
        echo "          ✅ OPERATION SUMMARY"
        echo "==============================================="
        log_success "Database sync completed successfully!"
    fi
    
    echo ""
    echo "📂 Local File:"
    if [ -n "$LOCAL_DATABASE_FILE" ]; then
        echo "   $(basename "$LOCAL_DATABASE_FILE")"
        if [ "$DRY_RUN" != true ] && [ -f "$LOCAL_DATABASE_FILE" ]; then
            local file_size=$(du -h "$LOCAL_DATABASE_FILE" 2>/dev/null | cut -f1 || echo "unknown")
            echo "   Size: $file_size"
        fi
        echo "   Path: $LOCAL_DATABASE_FILE"
    else
        echo "   Not determined"
    fi
    
    echo ""
    echo "🔄 Operations Performed:"
    if [ "$SKIP_DOWNLOAD" = true ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "   📁 Would use existing local file"
        else
            echo "   📁 Used existing local file"
        fi
    else
        if [ "$DRY_RUN" = true ]; then
            echo "   ☁️  Would export from Cloud SQL: $DEVELOPMENT_INSTANCE_NAME"
            echo "   📥 Would download to local directory"
        else
            echo "   ☁️  Exported from Cloud SQL: $DEVELOPMENT_INSTANCE_NAME"
            echo "   📥 Downloaded to local directory"
        fi
    fi
    
    if [ "$IMPORT_LOCAL" = true ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "   🗄️  Would import to MySQL: $MYSQL_HOST:$MYSQL_CORE_DATABASE"
        else
            echo "   🗄️  Imported to MySQL: $MYSQL_HOST:$MYSQL_CORE_DATABASE"
        fi
    else
        echo "   ⏭️  Skipped MySQL import (--skip-import used)"
    fi
    
    echo ""
    if [ "$DRY_RUN" = true ]; then
        echo "💡 To execute for real, run the same command without --dry-run"
    else
        echo "💡 Database is ready for use!"
        if [ "$IMPORT_LOCAL" = true ]; then
            echo "   Connect with: mysql -h $MYSQL_HOST -u $MYSQL_USER -p $MYSQL_CORE_DATABASE"
        else
            echo "   Import manually with: mysql -h localhost -u [user] -p [database] < $(basename "$LOCAL_DATABASE_FILE")"
        fi
    fi
    echo "==============================================="
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        if [ "$IMPORT_LOCAL" = true ]; then
            log_info "Starting database sync and import validation (DRY RUN MODE)"
        else
            log_info "Starting database sync validation - download only (DRY RUN MODE)"
        fi
    else
        if [ "$IMPORT_LOCAL" = true ]; then
            log_info "Starting database sync from development to local directory and MySQL"
        else
            log_info "Starting database sync from development to local directory (download only)"
        fi
    fi
    log_info "Development instance: $DEVELOPMENT_INSTANCE_NAME"
    log_info "Database: $DATABASE_NAME"
    log_info "Local directory: $LOCAL_INIT_DIR"
    
    # Early validation checks
    log_info "Performing pre-flight validation checks..."
    
    # Validate script environment
    if ! validate_script_environment; then
        exit 1
    fi
    
    # Check required commands
    if ! check_required_commands; then
        exit 1
    fi
    
    # Check and create local directory
    if ! check_local_directory; then
        exit 1
    fi
    
    # Determine database file path
    if ! determine_database_file; then
        exit 1
    fi
    
    # Cloud-specific checks (only if downloading)
    if [ "$SKIP_DOWNLOAD" != true ]; then
        log_info "Performing Cloud SQL and GCS validation..."
        if ! check_gcloud_auth; then
            exit 1
        fi
        if ! check_instance; then
            exit 1
        fi
        if ! check_gcs_bucket "$BUCKET_NAME"; then
            exit 1
        fi
    fi
    
    # MySQL-specific checks (default behavior unless --skip-import)
    if [ "$IMPORT_LOCAL" = true ]; then
        log_info "Performing MySQL validation checks..."
        if ! read_env_file; then
            exit 1
        fi
        if ! validate_mysql_host; then
            exit 1
        fi
        if ! check_mysql_connection; then
            exit 1
        fi
    else
        log_info "MySQL import will be skipped (--skip-import flag used)"
    fi
    
    log_success "All pre-flight checks completed successfully!"
    
    # Export from development (skip if --skip-download)
    if [ "$SKIP_DOWNLOAD" != true ]; then
        if ! export_database; then
            exit 1
        fi
        
        # Download to local directory
        if ! download_database; then
            log_error "Download failed. Export file remains at: gs://$BUCKET_NAME/$EXPORT_FILE"
            exit 1
        fi
    else
        log_info "Skipping download, using existing file: $(basename "$LOCAL_DATABASE_FILE")"
    fi
    
    # Import to local MySQL (default behavior unless --skip-import)
    if [ "$IMPORT_LOCAL" = true ]; then
        if ! import_to_local_mysql; then
            log_error "MySQL import failed. Database file remains at: $LOCAL_DATABASE_FILE"
            if [ "$SKIP_DOWNLOAD" != true ]; then
                cleanup_export_file
            fi
            exit 1
        fi
    else
        log_info "Skipping MySQL import - database file saved for manual use"
    fi
    
    # Clean up (only if we downloaded)
    if [ "$SKIP_DOWNLOAD" != true ]; then
        cleanup_export_file
    fi
    
    # Final summary
    show_completion_summary
}

# Run main function
main "$@" 