import { PlotData } from "../../../types/ThreadResponse";

export interface NewForestPlotProps {
  data: PlotData[];
  hoveredPair: { intervention_id: number; outcome_id: number } | undefined;
  onInterventionHover: (id: number | undefined) => void;
  onOutcomeHover: (id: number | undefined) => void;
}

export interface Score {
  lower: number;
  upper: number;
  value: number;
}

export interface ResearchData {
  paper_id: string;
  title: string;
  score: Score;
  label: string;
}

export interface Pair {
  outcome_id: number;
  outcome: string;
  intervention_id: number;
  intervention: string;
  data: ResearchData[];
  aggregate: Score;
}

export interface Edge {
  outcome_id: number;
  outcome: string;
  intervention_id: number;
  intervention: string;
  from: number;
  to: number;
}

export interface Label {
  id: number;
  label: string;
  y: number;
}

export interface LabelProps {
  onLabelPointerOver: (details: string) => void;
  onLabelPointerOut: () => void;
  isHovered: boolean;
  isNothingHovered: boolean;
  pairHeight: number;
  pairMarginBottom: number;
  label: Label;
  x: number;
}

export interface LabelBox {
  label: string;
  y: number;
}

export interface HoverState {
  outcomes: number[];
  interventions: number[];
}

export interface SelectedState {
  intervention_id?: number;
  outcome_id?: number;
}

export interface Outcome {
  label: string;
  id: number;
}
