import "./StudyDetails.css";

const StudyDetails = ({ study, intervention }) => {
  console.log("STUDY", study, intervention);
  return (
    <div className="study-container" style={{ background: "rgba(245, 249, 254, 1)"}}>
      <h4>Effect sizes</h4>
      <div className="study-details">
      {study && (intervention === undefined || intervention === study.intervention_id) && (
        <div>
          
            <div className="label">{study.label}</div>
            <div className="countries"><span className="key">countries:</span><span className="value">{study.country}</span></div>
            <div className="population"><span className="key">population:</span><span className="value">{-99}</span></div>
          </div>
        
        )}
        </div>
    </div>
  );
};

export default StudyDetails;
