"""<PERSON><PERSON>t to run methodology questions through analytics service."""

import asyncio
import json
import os
from src.services.analytics import run_analysis_from_file

# List of methodology questions
questions = [
    "How do you calculate standardized effect sizes?",
    "What criteria do you use to assess the quality of research papers?",
    "How do you handle heterogeneous outcome measures across different studies?",
    "What methods do you use to determine statistical significance in meta-analyses?",
    "How do you account for publication bias in your analysis?",
    "How do you weight studies in your analysis based on their sample size and quality?",
    "What methodology do you use to classify interventions and outcomes into categories?",
    "How do you handle missing data or incomplete reporting in research papers?",
    "What criteria do you use to determine if studies are comparable enough to be analyzed together?",
    "How do you assess and adjust for heterogeneity across studies?",
    "What methods do you use to identify and handle outlier studies?",
    "How do you synthesize qualitative evidence alongside quantitative findings?",
    "What approach do you take to evaluate the external validity of findings?",
    "How do you handle and standardize different units of measurement across studies?",
    "What methods do you use to detect and address selective reporting in studies?",
    "How do you assess the robustness of meta-analysis results?",
    "What criteria do you use to determine the strength of evidence for a finding?",
    "How do you handle studies with multiple treatment arms or outcomes?",
]


async def main():
    """Run the analytics service with our questions."""
    # Ensure analytics directory exists
    os.makedirs("data/analytics", exist_ok=True)

    # Save questions to file
    questions_file = "data/analytics/methodology_questions.json"
    with open(questions_file, "w") as f:
        json.dump(questions, f, indent=4)

    # Run the analysis
    await run_analysis_from_file(
        questions_file="methodology_questions.json",
        output_filename="methodology_results.json",
        force=True,
    )


if __name__ == "__main__":
    asyncio.run(main())
