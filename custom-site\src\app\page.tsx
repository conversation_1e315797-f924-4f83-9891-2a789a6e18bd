"use client";
import { Box, Grid } from "@mui/material";
import Section1Header from "./home/<USER>";
import Section2 from "./home/<USER>";
import Section3 from "./home/<USER>";
import Section4 from "./home/<USER>";
import Insights from "./home/<USER>";
import JoinWaitlistSection from "./home/<USER>";
import NewsAndEventsFrame from "./home/<USER>";
import { useIsMobile, useIsTablet } from "./components/MobileUtils";
import DynamicPageTitle from './components/DynamicPageTitle';

const Home = () => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  return (
    <>
      <DynamicPageTitle
        title="Impact AI - Tranform Evidence into Action - World Bank Group"
        description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
      />
      <Box
        sx={{
          width: "100%",
          background: "none",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          padding: 0,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "30px",
            padding: 0,
            width: "100%",
            alignItems: "center",
          }}
        >
          <Grid container sx={{ mb: 0, p: 0 }}>
            <Grid item xs={12} sx={{ p: 0, mt: isMobile ? "48px" : isTablet ? "90px" : "102px", }}>
              <Section1Header />
            </Grid>
            <Grid item xs={12} sx={{ p: 0 }}>
              <Section4 />
            </Grid>
            <Grid item xs={12}
              sx={{
                p: 0,
                mt: isMobile ? "31.3px" : isTablet ? "70px" : "70px",
              }}>
              <Section2 />
            </Grid>
            <Grid item xs={12}
              sx={{
                p: 0,
                mt: isMobile ? "31.3px" : isTablet ? "56px" : "105.67px",
              }}>
              <Section3 />
            </Grid>
            {/* <Grid item xs={12}
              sx={{
                p: 0,
                mt: isMobile ? "31.3px" : isTablet ? "56px" : "105.67px",
              }}>
              <Section1 />
            </Grid> */}
            <Grid item xs={12}
              sx={{
                p: 0,
                mt: isMobile ? "31.3px" : isTablet ? "56px" : "105.67px",
              }}>
              <Insights />
            </Grid>
            <Grid item xs={12}
              sx={{
                p: 0,
                mt: isMobile ? "31.3px" : isTablet ? "56px" : "105.67px",
              }}>
              <JoinWaitlistSection />
            </Grid>
            <Grid item xs={12}
              sx={{
                p: 0,
                my: isMobile ? "31.3px" : isTablet ? "56px" : "105.67px",
              }}>
              <NewsAndEventsFrame />
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default Home;
