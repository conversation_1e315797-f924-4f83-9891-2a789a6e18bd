#!/bin/bash

# Source logging if not already loaded
if ! declare -f log_info >/dev/null 2>&1; then
    source "$(dirname "${BASH_SOURCE[0]}")/logging.sh"
fi

# Function to check if gcloud is installed
check_gcloud_installed() {
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud is not installed. Please install it to proceed."
        exit 1
    fi
}

# Function to check if gcloud is authenticated
check_gcloud_auth() {
    log_info "Checking gcloud authentication..."
    
    check_gcloud_installed
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "gcloud is not authenticated. Please run 'gcloud auth login' first."
        exit 1
    fi
    
    log_success "gcloud authentication verified"
}

# Function to check if a Cloud SQL instance exists and is accessible
check_sql_instance() {
    local instance_name="$1"
    local instance_type="${2:-instance}"
    
    log_info "Checking if $instance_type '$instance_name' exists and is accessible..."
    
    if ! gcloud sql instances describe "$instance_name" --quiet >/dev/null 2>&1; then
        log_error "$instance_type '$instance_name' not found or not accessible"
        exit 1
    fi
    
    log_success "$instance_type is accessible"
}

# Function to check if a Google Cloud Storage bucket exists and is accessible
check_gcs_bucket() {
    local bucket_name="$1"
    
    log_info "Checking if bucket '$bucket_name' exists and is accessible..."
    
    if ! gsutil ls "gs://$bucket_name" >/dev/null 2>&1; then
        log_error "Bucket 'gs://$bucket_name' not found or not accessible"
        exit 1
    fi
    
    log_success "Bucket is accessible"
} 