import React, { useEffect, useState, useRef, useContext, useMemo, useCallback } from "react";
import { Box, IconButton } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import { Message, Source, FilterState } from "../../../types/ConversationTypes";
import { LayoutContext } from "../../Layout/LayoutContext";
import useChatScroll from '../../../hooks/useChatScroll';
import { CenteredPageLoader } from "../LoadingState";
import { hasContent } from '../../../utils/dataUtils';
import { extractFilterOptionsFromData, resolvePlotData, getActiveFiltersCount } from '../../../utils/Conversation/ConversationUtil';
import { TwoColumnLayout } from './TwoColumnLayout';
import { SingleColumnLayout } from './SingleColumnLayout';
import ArrowCircleDownIcon from '../../Common/Icons/ArrowCircleDownIcon';

interface DisplayConversationProps {
    loadingMessages: boolean;
    localConversationId: string;
    displaySystemLoader: boolean;
    setDisplaySystemLoader: (flag: boolean) => void;
    streamingEnded: boolean;
    setStreamingEnded: (flag: boolean) => void;
    handleUserPrompt: (message: any) => void;
    informationMessageId: string | null;
    onScrollComplete: () => void;
    newQuestionId: string;
    initialConversationMessages: Message[];
    summaryStreamedText: string;
    setSummaryStreamedText: (text: string) => void;
    setLocalMessageList: (messages: Message[]) => void;
    forceScrollToEnd: boolean;
    onForceScrollComplete: () => void;
    handleFiltersChange: (messageId: string, filters: FilterState, type: 'sources' | 'plot') => void;
    handleResetFilters: (messageId: string, type: 'sources' | 'plot') => void;
}

const DisplayConversation: React.FC<DisplayConversationProps> = ({
    loadingMessages,
    localConversationId,
    displaySystemLoader,
    setDisplaySystemLoader,
    streamingEnded,
    setStreamingEnded,
    handleUserPrompt,
    informationMessageId,
    onScrollComplete,
    newQuestionId,
    initialConversationMessages,
    summaryStreamedText,
    setSummaryStreamedText,
    setLocalMessageList,
    forceScrollToEnd,
    onForceScrollComplete,
    handleFiltersChange,
    handleResetFilters,
}) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;
    const [selectedStudy, setSelectedStudy] = useState("");
    const [activeQuestionId, setActiveQuestionId] = useState<string | null>(null);
    const [hoveredMessageId, setHoveredMessageId] = useState<string | null>(null);
    const lastMessageRef = useRef<HTMLDivElement | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const leftColumnScrollRef = useRef<HTMLDivElement>(null);
    const conversationWrapperScrollRef = useRef<HTMLDivElement>(null);
    const [openRightSection, setOpenRightSection] = useState<"sources" | "charts" | null>(null);
    const [activeInfoMessageIdForPanel, setActiveInfoMessageIdForPanel] = useState<string | null>(null);
    const [panelSources, setPanelSources] = useState<Source[]>([]);
    const {
        updateActiveMessageInfo,
        isSidebarCollapsed,
    } = useContext(LayoutContext);
    const [fullMessageInfoFetched, setFullMessageInfoFetched] = useState<Message | null>(null);
    const [previousScrollTop, setPreviousScrollTop] = useState(0);
    const [activePlotDetails, setActivePlotDetails] = useState<{
        citation_ids: { key: string; value: string }[];
        messageId: string;
        plotDataInfo?: any;
    } | null>(null);
    const [activeSourcePaperIds, setActiveSourcePaperIds] = useState<string[]>([]);
    const [activeSourceMessageId, setActiveSourceMessageId] = useState<string | null>(null);
    const [filterOptions, setFilterOptions] = useState({});

    // Track whether there is content below the viewport and center position for the arrow
    const [hasBelow, setHasBelow] = useState(false);
    const [arrowLeft, setArrowLeft] = useState<number | null>(null);
    const rafRef = useRef<number | null>(null);
    const trackUntilRef = useRef<number>(0);

    useChatScroll({
        scrollContainerRef: openRightSection ? leftColumnScrollRef : conversationWrapperScrollRef,
        displaySystemLoader: displaySystemLoader,
        forceScrollToEnd: forceScrollToEnd,
        onForceScrollComplete: onForceScrollComplete,
        manualScroll: true,
        onBelowChange: setHasBelow,
    });

    const commonFocusHoverActiveStyles = {
        border: `1px solid ${theme.components.input.standard.hoverBorder}`,
        color: theme.palette.text.primary,
        backgroundColor: theme.palette.common.white,
    };

    const buttonStyle = {
        color: theme.palette.text.secondary,
        fontSize: '13px',
        fontStyle: 'normal',
        fontWeight: 600,
        lineHeight: '22px',
        letterSpacing: '1px',
        textTransform: 'uppercase',
        height: '30px',
        "&:hover": {
            ...commonFocusHoverActiveStyles,
        },
    };

    // Center the arrow horizontally over the active chat scroll container
    const updateArrowPosition = useCallback(() => {
        const el = (openRightSection ? leftColumnScrollRef.current : conversationWrapperScrollRef.current);
        if (!el) return;
        const rect = el.getBoundingClientRect();
        setArrowLeft(rect.left + rect.width / 2);
    }, [openRightSection]);

    const schedulePositionTracking = useCallback((durationMs = 600) => {
        if (rafRef.current) cancelAnimationFrame(rafRef.current);
        trackUntilRef.current = performance.now() + durationMs;
        const tick = () => {
            updateArrowPosition();
            if (performance.now() < trackUntilRef.current) {
                rafRef.current = requestAnimationFrame(tick);
            } else {
                rafRef.current = null;
            }
        };
        rafRef.current = requestAnimationFrame(tick);
    }, [updateArrowPosition]);

    useEffect(() => {
        updateArrowPosition();
        const onResize = () => {
            updateArrowPosition();
            schedulePositionTracking(800);
        };
        window.addEventListener('resize', onResize);

        const el = (openRightSection ? leftColumnScrollRef.current : conversationWrapperScrollRef.current);
        let ro: ResizeObserver | null = null;
        if (el && 'ResizeObserver' in window) {
            ro = new ResizeObserver(() => {
                requestAnimationFrame(() => updateArrowPosition());
            });
            ro.observe(el);
        }

        return () => {
            window.removeEventListener('resize', onResize);
            if (ro) ro.disconnect();
            if (rafRef.current) cancelAnimationFrame(rafRef.current);
        };
    }, [updateArrowPosition, schedulePositionTracking, openRightSection]);

    useEffect(() => {
        schedulePositionTracking(800);
    }, [isSidebarCollapsed, openRightSection, isMobileOrTablet, schedulePositionTracking]);

    useEffect(() => {
        if (hasBelow) {
            updateArrowPosition();
            schedulePositionTracking(500);
        }
    }, [hasBelow, updateArrowPosition, schedulePositionTracking]);

    const scrollToBottom = useCallback(() => {
        const el = (openRightSection ? leftColumnScrollRef.current : conversationWrapperScrollRef.current);
        if (!el) return;
        el.scrollTo({ top: el.scrollHeight, behavior: 'smooth' });
        // After click, track position briefly to follow any layout transition
        schedulePositionTracking(500);
    }, [openRightSection, schedulePositionTracking]);

    const handleCloseRightSection = useCallback(() => {
        const currentScrollPositionInLeftColumn = leftColumnScrollRef.current?.scrollTop || 0;
        setOpenRightSection(null);
        setActiveInfoMessageIdForPanel(null);
        updateActiveMessageInfo(null);
        setPanelSources([]);
        setActivePlotDetails(null);
        setActiveSourcePaperIds([]);
        setActiveSourceMessageId(null);
        setSelectedStudy("");
        requestAnimationFrame(() => {
            if (conversationWrapperScrollRef.current) {
                conversationWrapperScrollRef.current.scrollTop = currentScrollPositionInLeftColumn;
                setPreviousScrollTop(0);
            }
        });
    }, [
        leftColumnScrollRef,
        conversationWrapperScrollRef,
        updateActiveMessageInfo,
        activeInfoMessageIdForPanel,
        handleResetFilters,
        openRightSection,
    ]);

    useEffect(() => {
        if (localConversationId) {
            handleCloseRightSection();
        }
    }, [localConversationId]);

    useEffect(() => {
        if (newQuestionId) {
            setActiveQuestionId(newQuestionId);
        }
    }, [newQuestionId]);

    const activeFilters = useMemo(() => {
        if (!activeInfoMessageIdForPanel) return null;
        const activeMessage = initialConversationMessages.find(msg => msg.id === activeInfoMessageIdForPanel);
        if (openRightSection === 'sources') {
            return activeMessage?.filters_sources || null;
        } else if (openRightSection === 'charts') {
            return activeMessage?.filters_plot || null;
        }
        return null;
    }, [initialConversationMessages, activeInfoMessageIdForPanel, openRightSection]);

    const activeFiltersCount = useMemo(() => {
        return getActiveFiltersCount(activeFilters, filterOptions);
    }, [activeFilters, filterOptions]);

    useEffect(() => {
        if (informationMessageId) {
            setLocalMessageList(prevMessages =>
                prevMessages.map(msg => {
                    if (msg.id === 'system-loading') {
                        const updatedText = streamingEnded
                            ? (fullMessageInfoFetched?.text || msg.text)
                            : summaryStreamedText;

                        const updatedSources = (streamingEnded && fullMessageInfoFetched?.sources !== undefined)
                            ? fullMessageInfoFetched.sources
                            : msg.sources;

                        const updatedPlot = (streamingEnded && fullMessageInfoFetched?.plot !== undefined)
                            ? fullMessageInfoFetched.plot
                            : msg.plot;

                        const updatedTopics = (streamingEnded && fullMessageInfoFetched?.choices !== undefined)
                            ? fullMessageInfoFetched.choices
                            : msg.choices;

                        const updatedHasSources = (streamingEnded && fullMessageInfoFetched?.has_sources !== undefined)
                            ? fullMessageInfoFetched.has_sources
                            : msg.has_sources;

                        const updatedHasPlot = (streamingEnded && fullMessageInfoFetched?.has_plot !== undefined)
                            ? fullMessageInfoFetched.has_plot
                            : msg.has_plot;

                        return {
                            ...msg,
                            sources: updatedSources,
                            plot: updatedPlot,
                            text: updatedText,
                            choices: updatedTopics,
                            has_sources: updatedHasSources,
                            has_plot: updatedHasPlot,
                        };
                    }
                    return msg;
                })
            );

            if (streamingEnded && informationMessageId) {
                if (openRightSection === "sources") {
                    if (fullMessageInfoFetched?.sources && fullMessageInfoFetched.sources.length > 0) {
                        setPanelSources(fullMessageInfoFetched.sources);
                    }
                }
            }
        }
    }, [
        summaryStreamedText,
        fullMessageInfoFetched,
        informationMessageId,
        streamingEnded,
        openRightSection,
        setLocalMessageList
    ]);

    useEffect(() => {
        if (activeQuestionId && localConversationId) {
            setTimeout(() => {
                const activeQuestionElement = document.getElementById(activeQuestionId);
                const container = containerRef.current;
                if (activeQuestionElement && container) {
                    const messageTop = activeQuestionElement.offsetTop;
                    const messageBottom = messageTop + activeQuestionElement.offsetHeight;
                    const visibleTop = container.scrollTop;
                    const visibleBottom = visibleTop + container.clientHeight;
                    if (messageBottom > visibleBottom || messageTop < visibleTop) {
                        activeQuestionElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                        });
                    }
                    requestAnimationFrame(() => {
                        setTimeout(() => {
                            onScrollComplete();
                        }, 500);
                    });
                } else {
                    console.error('Active question element or container not found for scroll.');
                }
            }, 0);
        }
    }, [activeQuestionId, localConversationId]);

    const setFiltersFromPlotData = (rawPlotData: any, messageId: string, filterType: 'sources' | 'plot') => {
        const resolvedPlotData = resolvePlotData(rawPlotData);
        if (resolvedPlotData) {
            const extractedOptions = extractFilterOptionsFromData(resolvedPlotData);
            setFilterOptions(extractedOptions);

            if (extractedOptions.years && extractedOptions.years.length > 0) {
                const newFilters: FilterState = {
                    years: [extractedOptions.years[0], extractedOptions.years[extractedOptions.years.length - 1]],
                    sectors: [],
                    countries: [],
                };
                if (messageId) {
                    handleFiltersChange(messageId, newFilters, filterType);
                }
            }
        } else {
            setFilterOptions({});
            if (messageId) {
                handleResetFilters(messageId, filterType);
            }
        }
    };

    const handleOpenCharts = (messageId: string, plotPayload?: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any; } | null) => {
        if (conversationWrapperScrollRef.current) {
            setPreviousScrollTop(conversationWrapperScrollRef.current.scrollTop);
        }
        setOpenRightSection("charts");
        setActiveInfoMessageIdForPanel(messageId);
        const clickedMessage = initialConversationMessages.find(msg => msg.id === messageId);
        setSelectedStudy("");
        let sourcesToSet: Source[] = [];
        if (messageId === informationMessageId && fullMessageInfoFetched && fullMessageInfoFetched.sources) {
            sourcesToSet = fullMessageInfoFetched.sources;
        } else if (clickedMessage?.sources) {
            sourcesToSet = clickedMessage.sources;
        }

        let rawPlotData;
        if (plotPayload && hasContent(plotPayload.plotDataInfo)) {
            setActivePlotDetails({
                citation_ids: plotPayload.citation_ids || [],
                messageId: messageId,
                plotDataInfo: plotPayload.plotDataInfo
            });
            rawPlotData = plotPayload.plotDataInfo;
        } else if (clickedMessage?.plot && hasContent(clickedMessage.plot)) {
            setActivePlotDetails({
                citation_ids: [],
                messageId: messageId,
                plotDataInfo: clickedMessage.plot
            });
            rawPlotData = clickedMessage.plot;
        } else {
            setActivePlotDetails(null);
        }
        setPanelSources(sourcesToSet);

        if (clickedMessage?.filters_plot) {
            setFilterOptions(extractFilterOptionsFromData(resolvePlotData(rawPlotData)));
        } else {
            setFiltersFromPlotData(rawPlotData, messageId, 'plot');
        }

        updateActiveMessageInfo(clickedMessage);
    };

    const handleOpenSources = (messageId: string, fromSourcesButton = false) => {
        if (conversationWrapperScrollRef.current) {
            setPreviousScrollTop(conversationWrapperScrollRef.current.scrollTop);
        }
        setOpenRightSection("sources");
        setActiveInfoMessageIdForPanel(messageId);
        setSelectedStudy("");
        const clickedMessage = initialConversationMessages.find(m => m.id === messageId);
        setPanelSources(clickedMessage?.sources || []);

        if (fromSourcesButton) {
            setActiveSourcePaperIds([]);
            setActiveSourceMessageId(null);
        }

        if (clickedMessage?.filters_sources) {
            setFilterOptions(extractFilterOptionsFromData(resolvePlotData(clickedMessage.plot)));
        } else {
            setFiltersFromPlotData(clickedMessage?.plot, messageId, 'sources');
        }

        updateActiveMessageInfo(clickedMessage || null);
    };


    const handleViewOnPlotClickedInternal = useCallback((payload: {
        citation_ids: { key: string; value: string }[];
        messageId: string;
        plotDataInfo?: any;
    }) => {
        if (hasContent(payload.plotDataInfo)) {
            handleOpenCharts(payload.messageId, payload);
        }
    }, [handleOpenCharts]);

    const handleViewOnSourceClickedInternal = useCallback((payload: { paper_ids: string[]; messageId: string }) => {
        const clickedMessage = initialConversationMessages.find(m => m.id === payload.messageId);
        if (clickedMessage?.sources && clickedMessage.sources.length > 0 && clickedMessage?.has_sources) {
            const messageSourcePaperIds = new Set(clickedMessage.sources.map(source => source.short_paper_id));
            const hasMatchingPaperId = payload.paper_ids.some(paperId =>
                messageSourcePaperIds.has(paperId)
            );
            if (hasMatchingPaperId) {
                setActiveSourcePaperIds(payload.paper_ids);
                setActiveSourceMessageId(payload.messageId);
                handleOpenSources(payload.messageId, false);
            }
        }
    }, [handleOpenSources]);

    useEffect(() => {
        if (openRightSection && leftColumnScrollRef.current && previousScrollTop !== 0) {
            leftColumnScrollRef.current.scrollTop = previousScrollTop;
        }
    }, [openRightSection, previousScrollTop]);

    const isTwoColumnLayoutWithPanelOpen = useMemo(() => {
        return !isMobileOrTablet && openRightSection !== null;
    }, [isMobileOrTablet, openRightSection]);

    const lastSystemMessageId = useMemo(() => {
        const validMessages = initialConversationMessages.filter(msg =>
            (msg !== null && msg !== undefined) || (msg?.id === 'system-loading' && !msg?.text)
        );
        const lastSystemMessage = [...validMessages]
            .reverse()
            .find(msg =>
                msg.type === 'information' &&
                msg.author === 'system' &&
                (
                    (msg?.text !== null && msg?.text !== undefined) ||
                    (informationMessageId && msg?.id === 'system-loading')
                )
            );
        return lastSystemMessage?.id || null;
    }, [initialConversationMessages]);

    if (initialConversationMessages.length === 0 || loadingMessages) {
        return (
            <Box
                display="flex"
                flexDirection="column"
                width={isMobileOrTablet ? "90%" : "848px"}
                mx="auto"
                className="chat-conversation"
                sx={{
                    height: 'calc(100vh - 160px)',
                    overflowY: "hidden",
                    transition: "padding-top 0.3s ease",
                }}
            >
                <Box
                    className="chat-history-container"
                    display="flex"
                    flexDirection="column"
                    flexGrow={1}
                    px={0}
                    height="100%"
                    justifyContent="center"
                    alignItems="center"
                    position="relative"
                >
                    <CenteredPageLoader isMobile={isMobile} />
                </Box>
            </Box>
        );
    }
    return (
        <Box
            ref={containerRef}
            className="chat-conversation"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                mx: 'auto',
                height: window.location.href.includes('impact-ai-prod.app') ? '100vh' : 'calc(100vh - 82px)',
                flexGrow: 1,
            }}
        >
            {isMobileOrTablet ? (
                <SingleColumnLayout
                    isMobileOrTablet={isMobileOrTablet}
                    conversationWrapperScrollRef={conversationWrapperScrollRef}
                    initialConversationMessages={initialConversationMessages}
                    handleUserPrompt={handleUserPrompt}
                    displaySystemLoader={displaySystemLoader}
                    loadingMessages={loadingMessages}
                    lastSystemMessageId={lastSystemMessageId}
                    hoveredMessageId={hoveredMessageId}
                    setHoveredMessageId={setHoveredMessageId}
                    informationMessageId={informationMessageId}
                    streamingEnded={streamingEnded}
                    summaryStreamedText={summaryStreamedText}
                    openRightSection={openRightSection}
                    activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                    isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                    localConversationId={localConversationId}
                    handleViewOnPlotClickedInternal={handleViewOnPlotClickedInternal}
                    handleViewOnSourceClickedInternal={handleViewOnSourceClickedInternal}
                    handleOpenSources={handleOpenSources}
                    handleOpenCharts={handleOpenCharts}
                    setDisplaySystemLoader={setDisplaySystemLoader}
                    setStreamingEnded={setStreamingEnded}
                    setSummaryStreamedText={setSummaryStreamedText}
                    setFullMessageInfoFetched={setFullMessageInfoFetched}
                    activeQuestionId={activeQuestionId}
                    lastMessageRef={lastMessageRef}
                    buttonStyle={buttonStyle}
                    theme={theme}
                    handleCloseRightSection={handleCloseRightSection}
                    panelSources={panelSources}
                    selectedStudy={selectedStudy}
                    activeSourcePaperIds={activeSourcePaperIds}
                    activeSourceMessageId={activeSourceMessageId}
                    setActiveSourcePaperIds={setActiveSourcePaperIds}
                    setActiveSourceMessageId={setActiveSourceMessageId}
                    handleFiltersChange={handleFiltersChange}
                    handleResetFilters={handleResetFilters}
                    filterOptions={filterOptions}
                    activeFilters={activeFilters}
                    activeFiltersCount={activeFiltersCount}
                    activePlotDetails={activePlotDetails}
                    setSelectedStudy={setSelectedStudy}
                />
            ) : (
                <TwoColumnLayout
                    openRightSection={openRightSection}
                    isSidebarCollapsed={isSidebarCollapsed}
                    isMobileOrTablet={isMobileOrTablet}
                    initialConversationMessages={initialConversationMessages}
                    handleUserPrompt={handleUserPrompt}
                    displaySystemLoader={displaySystemLoader}
                    loadingMessages={loadingMessages}
                    conversationWrapperScrollRef={conversationWrapperScrollRef}
                    leftColumnScrollRef={leftColumnScrollRef}
                    lastSystemMessageId={lastSystemMessageId}
                    hoveredMessageId={hoveredMessageId}
                    setHoveredMessageId={setHoveredMessageId}
                    informationMessageId={informationMessageId}
                    streamingEnded={streamingEnded}
                    summaryStreamedText={summaryStreamedText}
                    activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                    isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                    localConversationId={localConversationId}
                    handleViewOnPlotClickedInternal={handleViewOnPlotClickedInternal}
                    handleViewOnSourceClickedInternal={handleViewOnSourceClickedInternal}
                    handleOpenSources={handleOpenSources}
                    handleOpenCharts={handleOpenCharts}
                    setDisplaySystemLoader={setDisplaySystemLoader}
                    setStreamingEnded={setStreamingEnded}
                    setSummaryStreamedText={setSummaryStreamedText}
                    setFullMessageInfoFetched={setFullMessageInfoFetched}
                    activeQuestionId={activeQuestionId}
                    lastMessageRef={lastMessageRef}
                    buttonStyle={buttonStyle}
                    theme={theme}
                    handleCloseRightSection={handleCloseRightSection}
                    panelSources={panelSources}
                    selectedStudy={selectedStudy}
                    activeSourcePaperIds={activeSourcePaperIds}
                    activeSourceMessageId={activeSourceMessageId}
                    setActiveSourcePaperIds={setActiveSourcePaperIds}
                    setActiveSourceMessageId={setActiveSourceMessageId}
                    handleFiltersChange={handleFiltersChange}
                    handleResetFilters={handleResetFilters}
                    filterOptions={filterOptions}
                    activeFilters={activeFilters}
                    activeFiltersCount={activeFiltersCount}
                    activePlotDetails={activePlotDetails}
                    setSelectedStudy={setSelectedStudy}
                />
            )}

            {hasBelow && arrowLeft !== null && (
                <IconButton
                    aria-label="Scroll to bottom"
                    onClick={scrollToBottom}
                    sx={{
                        position: 'fixed',
                        left: arrowLeft,
                        transform: 'translateX(-50%)',
                        bottom: isMobileOrTablet ? 120 : 136,
                        width: 40,
                        height: 40,
                        zIndex: 10,
                        color: theme.palette.text.secondary,
                        bgcolor: theme.palette.common.white,
                        border: `1px solid ${theme.palette.divider}`,
                        boxShadow: 1,
                        '&:hover': {
                            bgcolor: theme.palette.common.white,
                        },
                    }}
                >
                    <ArrowCircleDownIcon sx={{ fontSize: 40 }} />
                </IconButton>
            )}
        </Box>
    );
};

export default DisplayConversation;