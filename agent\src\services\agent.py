"""Agent service and progress monitoring.

# pyright: reportUnusedClass=false
# pylint: disable=unused-variable
"""

import asyncio
import json
import time
from datetime import datetime, timedelta

from typing import Any, Dict, Optional
from src.agent.main import Agent
from src.services.cache import Cache
from redis.exceptions import RedisError
from src.tools.llm_client import LLMClient
from src.agent.models import AgentCache


class ProgressMonitor:  # pylint: disable=too-many-instance-attributes
    """Monitors agent execution progress and emits SSE events for status updates."""

    # Static fallback descriptions if LLM is unavailable
    FALLBACK_DESCRIPTIONS = {
        "entity_extractor": "Understanding your research question",
        "sql_generator": "Searching studies database for relevant research",
        "rag_search": "Expanding knowledge from research literature",
        "structured_data_organizer": "Organizing research data and findings",
        "final_answer_generator": "Preparing comprehensive research answer",
        "methodology_explainer": "Explaining research methodology",
    }

    def __init__(
        self,
        cache: Cache,
        conversation_id: str,
        query: Optional[str] = None,
        llm_client: Optional[LLMClient] = None,
        model_name: str = "gemini-2.0-flash-001",
        agent_cache: Optional[AgentCache] = None,
    ):
        self.cache = cache
        self.conversation_id = conversation_id
        self.execution_key = f"agent_execution_steps:{conversation_id}"
        self.last_seen_count = 0
        self.seen_tools: Dict[str, int] = {}
        self.query = query or ""
        # Internal buffering to ensure ordered, paced delivery of steps
        self._buffered_steps: list[dict] = []
        self._buffer_index: int = 0
        self._next_ready_time: Optional[float] = None
        # Direct access to agent cache avoids Redis fallback delays
        self.agent_cache: Optional[AgentCache] = agent_cache

        # Lazy-init LLM client with safe fallback
        self.llm: Optional[LLMClient] = None
        try:
            self.llm = llm_client or LLMClient(model_name=model_name)
        except (ValueError, TypeError):
            # If initialization fails (e.g., env missing), we will fallback to static messages
            self.llm = None

    def _extract_subject_terms(self, data: Any) -> str:
        """Build a short, comma-separated subject string from step data."""
        try:
            if not data:
                return ""
            names: list[str] = []
            # outcomes
            outcomes = data.get("outcomes") if isinstance(data, dict) else None
            if isinstance(outcomes, list):
                for o in outcomes:
                    if isinstance(o, dict):
                        name = (
                            o.get("short_label") or o.get("label") or o.get("mention")
                        )
                        if name:
                            names.append(str(name))
            # interventions
            interventions = (
                data.get("interventions") if isinstance(data, dict) else None
            )
            if isinstance(interventions, list):
                for i in interventions:
                    if isinstance(i, dict):
                        # interventions may carry various keys
                        name = (
                            i.get("short_label")
                            or i.get("label")
                            or i.get("mention")
                            or i.get("name")
                        )
                        if name:
                            names.append(str(name))
            # fallback user_query
            uq = None
            if isinstance(data, dict):
                uq = data.get("user_query")
            if uq and not names:
                names.append(str(uq))
            # Join and trim conservatively
            subject = ", ".join(names)
            return subject[:40]
        except (ValueError, TypeError):
            return ""

    def _build_status_prompt(self, step: Dict[str, Any]) -> str:
        """Create the robust prompt for the LLM to generate a ≤40 char message."""
        step_json = json.dumps(step, ensure_ascii=False)
        question = self.query or ""
        entities_str = self._extract_subject_terms(step.get("data"))

        # Check if this tool has been seen before for refining language
        tool_name = step.get("tool_name", "")
        tool_run_count = self.seen_tools.get(tool_name, 0)
        is_repeat_run = tool_run_count > 0

        return self.llm.load_prompt(
            "status_message_generator",
            step_json=step_json,
            question=question,
            entities_str=entities_str,
            is_repeat_run=is_repeat_run,
            run_count=tool_run_count + 1,
        )

    @staticmethod
    def _apply_refining_language(base_desc: str, is_repeat_run: bool) -> str:
        """Apply refining language to base description if this is a repeat run."""
        if not is_repeat_run:
            return base_desc

        replacements = [
            ("Understanding", "Refining understanding"),
            ("Searching", "Expanding search"),
            ("Expanding", "Further expanding"),
            ("Organizing", "Refining"),
            ("Preparing", "Improving"),
        ]

        updated = f"Refining {base_desc.lower()}"
        for needle, repl in replacements:
            if needle in base_desc:
                updated = base_desc.replace(needle, repl)
                break
        return updated

    @staticmethod
    def _sanitize_llm_message(text: str, fallback: str) -> str:
        """Ensure the output is a single line ≤ 100 chars; fallback if empty."""
        if not isinstance(text, str):
            return fallback
        # Take first line, strip quotes and spaces
        line = text.splitlines()[0].strip().strip('"').strip("'")
        # Remove trailing punctuation
        while line and line[-1] in ".!?":
            line = line[:-1]
        if not line:
            return fallback
        return line

    def _generate_fallback_description(
        self, tool_name: str, is_repeat_run: bool
    ) -> str:
        base_desc = self.FALLBACK_DESCRIPTIONS.get(
            tool_name, f"Working on {tool_name.replace('_', ' ')}"
        )
        return self._apply_refining_language(base_desc, is_repeat_run)

    async def _generate_description(self, step: Dict[str, Any]) -> str:
        tool_name = step.get("tool_name", "")
        status = step.get("status", "")

        tool_run_count = self.seen_tools.get(tool_name, 0)
        is_repeat_run = tool_run_count > 0

        # Base fallback with optional query context
        base_desc = self._generate_fallback_description(tool_name, is_repeat_run)
        if self.query and len(self.query) > 5:
            query_terms = self.query.lower()
            if len(query_terms) > 50:
                query_terms = query_terms[:47] + "..."
            fallback_desc = f"{base_desc} about {query_terms}"[:100]
        else:
            fallback_desc = base_desc

        description = fallback_desc
        # Try dynamic LLM generation
        if self.llm is not None:
            try:
                prompt = self._build_status_prompt(step)
                llm_resp = await self.llm.generate(
                    prompt=prompt,
                    temperature=0.1,
                    max_tokens=64,
                )
                description = self._sanitize_llm_message(
                    llm_resp.text, fallback=fallback_desc
                )
            except (ValueError, TypeError):
                description = fallback_desc

        # Record repeat runs on start events only
        if status == "started":
            self.seen_tools[tool_name] = self.seen_tools.get(tool_name, 0) + 1

        return description

    async def check_for_updates(self):  # pylint: disable=too-many-branches
        """Check for new execution steps and return one status update if available.

        Guarantees:
        - Every step is shown exactly once in FIFO order (no skips)
        - If multiple new steps arrive at once, they are paced at ~1s intervals
        - Both "started" and "finished" events are emitted
        """
        try:
            # If we have buffered steps, emit them first with pacing
            if self._buffer_index < len(self._buffered_steps):
                if (
                    self._next_ready_time is not None
                    and time.monotonic() < self._next_ready_time
                ):
                    return None

                step = self._buffered_steps[self._buffer_index]
                tool_name = step.get("tool_name")
                status = step.get("status")
                if tool_name and status in {"started", "finished"}:
                    description = await self._generate_description(step)
                    self._buffer_index += 1
                    self.last_seen_count += 1

                    # If more buffered steps remain, schedule next after 1s
                    if self._buffer_index < len(self._buffered_steps):
                        self._next_ready_time = time.monotonic() + 1.0
                    else:
                        # Clear buffer state when exhausted
                        self._buffered_steps = []
                        self._buffer_index = 0
                        self._next_ready_time = None

                    return {"title": description, "status": status}

                # Skip invalid entries and advance pointer
                self._buffer_index += 1
                self.last_seen_count += 1

            # No buffered steps: check for new ones via direct AgentCache when provided
            if self.agent_cache is not None:
                current_count = self.agent_cache.get_execution_steps_count(
                    self.conversation_id
                )
            else:
                agent = Agent.get_current_agent()
                if agent is not None:
                    current_count = agent.agent_cache.get_execution_steps_count(
                        self.conversation_id
                    )
                else:
                    # Fallback to direct Redis check
                    current_count = await asyncio.to_thread(
                        self.cache.llen, self.execution_key
                    )
            if current_count <= self.last_seen_count:
                return None

            # Fetch only the new steps
            if self.agent_cache is not None:
                new_steps = self.agent_cache.get_execution_steps(
                    self.conversation_id, self.last_seen_count, -1
                )
            else:
                agent = Agent.get_current_agent()
                if agent is not None:
                    new_steps = agent.agent_cache.get_execution_steps(
                        self.conversation_id, self.last_seen_count, -1
                    )
                else:
                    new_steps = await asyncio.to_thread(
                        self.cache.lrange_json,
                        self.execution_key,
                        self.last_seen_count,
                        -1,
                    )
            # Initialize buffer with new steps
            self._buffered_steps = list(new_steps or [])
            self._buffer_index = 0
            self._next_ready_time = None

            # Immediately emit the first valid step if present
            while self._buffer_index < len(self._buffered_steps):
                step = self._buffered_steps[self._buffer_index]
                tool_name = step.get("tool_name")
                status = step.get("status")
                if tool_name and status in {"started", "finished"}:
                    description = await self._generate_description(step)
                    self._buffer_index += 1
                    self.last_seen_count += 1
                    # If more buffered steps remain, schedule next after 0.3s
                    if self._buffer_index < len(self._buffered_steps):
                        self._next_ready_time = time.monotonic() + 0.3
                    else:
                        self._buffered_steps = []
                        self._buffer_index = 0
                        self._next_ready_time = None
                    return {"title": description, "status": status}

                self._buffer_index += 1
                self.last_seen_count += 1

            # If we reach here, there were new items but none valid
            return None

        except (RedisError, ValueError, TypeError):
            # Silently continue on errors
            return None


class AgentService:  # noqa: F401  # pyright: ignore[reportUnusedClass]
    def __init__(self):
        self.configs = {
            "model_name": "gemini-2.0-flash-001",
            "temperature": 0.1,
            "max_tokens": 8192,
            "max_iterations": 10,
            "verbose": True,
            "bucket_name": "scihub-papers-processed",
            "use_gcp": True,
        }
        self.cache = Cache()

    async def generated_summary_data(self, conversation_id: str, message_id: str):
        cache_key = f"summary_data:{conversation_id}:{message_id}"

        async def get_summary_data():
            parsed_data = self.cache.get_json(cache_key)
            if not parsed_data:
                return None

            if parsed_data.get("stream_ended_at"):
                return parsed_data

            stream_started_at = parsed_data.get("stream_started_at")
            stream_age = datetime.now() - datetime.strptime(
                stream_started_at, "%Y-%m-%d %H:%M:%S.%f"
            )

            if stream_age > timedelta(minutes=5):
                return parsed_data

            while True:
                await asyncio.sleep(1)  # wait 1 second before trying again.
                return await get_summary_data()

        return await get_summary_data()

    def format_to_sse_event(self, event_type: str, data: dict) -> str:
        """Format an SSE event with the given event type and JSON data."""
        if event_type not in ["status", "summary", "error"]:
            raise ValueError("Event type must be 'status', 'summary', or 'error'")
        json_data = json.dumps(data)

        return f"event: {event_type}\ndata: {json_data}\n\n"

    async def generate_summary(self, conversation_id: str, message_id: str, query: str):
        cache_key = f"summary_data:{conversation_id}:{message_id}"
        stream_status = {
            "query": query,
            "stream_started_at": str(datetime.now()),
            "stream_ended_at": None,
            "summary_data": None,
            "summary_text": None,
        }
        self.cache.store_json(cache_key, stream_status)

        # Initial status
        yield self.format_to_sse_event(
            "status",
            {
                "title": "Thinking.",
                "description": "Initializing research process for your query",
            },
        )

        # Create agent first so the monitor can read its cache immediately
        agent = Agent(
            self.configs,
            conversation_id=conversation_id,
        )

        # Create progress monitor with direct pointer to agent cache
        progress_monitor = ProgressMonitor(
            self.cache,
            conversation_id,
            query=query,
            model_name=self.configs.get("model_name", "gemini-2.0-flash-001"),
            agent_cache=agent.agent_cache,
        )

        # Start agent execution in background using the created agent
        execution_task = asyncio.create_task(
            self.execute(conversation_id, query, agent=agent)
        )

        # Simple monitoring loop
        while not execution_task.done():
            try:
                # Bound the progress check to keep loop responsive
                new_status = await asyncio.wait_for(
                    progress_monitor.check_for_updates(), timeout=0.5
                )
                if new_status:
                    yield self.format_to_sse_event("status", new_status)
            except asyncio.TimeoutError:
                # Timed out waiting for updates; continue loop
                pass

            # Brief pause before next iteration
            await asyncio.sleep(0.5)

        # Get the execution result
        response = await execution_task

        # Update final status
        stream_status["summary_text"] = response.get("response", "")
        stream_status["summary_data"] = response.get("context", {}).get("tool_data", {})
        stream_status["stream_ended_at"] = str(datetime.now())
        self.cache.store_json(cache_key, stream_status)

        yield self.format_to_sse_event("summary", stream_status["summary_text"])

    async def execute(
        self, conversation_id: str, query: str, agent: Agent | None = None
    ):
        if agent is None:
            agent = Agent(
                self.configs,
                conversation_id=conversation_id,
            )
        response = await agent.execute(query=query)
        tool_data = agent.tool_manager.get_tool_data()

        structured_data = None
        if tool_data.get("structured_data"):
            structured_data = tool_data.get("structured_data").get("text", "")
        rag_results = None
        if tool_data.get("rag_results"):
            rag_results = tool_data.get("rag_results").get("documents", [])

        structured_data_v2 = None
        if tool_data.get("structured_data_v2"):
            structured_data_v2 = tool_data.get("structured_data_v2")

        data_used = tool_data.get("data_used")
        data_used_url = tool_data.get("data_used_url")
        url_post_processed = tool_data.get("url_post_processed")

        return {
            "response": response,
            "context": {
                "query": query,
                "conversation_id": agent.conversation_id,
                "tool_data": {
                    "structured_data": structured_data,
                    "structured_data_v2": structured_data_v2,
                    "rag_results": rag_results,
                    "data_used": data_used,
                    "data_url": data_used_url,
                    "url_post_processed": url_post_processed,
                },
            },
        }


def _touch_exports():
    """Reference exported names to satisfy strict linters."""
    return AgentService, ProgressMonitor


_touch_exports()
