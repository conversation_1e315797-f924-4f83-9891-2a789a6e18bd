import { Box, Button as MUIButton } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import { NEW_CHAT_BUTTON_LABEL } from '../utils/labels';

interface NewChatButtonControllerProps {
  onClick: () => void;
  isCollapsed: boolean;
}

const NewChatButtonController: React.FC<NewChatButtonControllerProps> = ({ onClick, isCollapsed }) => {
  const navigate = useNavigate();
  const theme = useTheme();


  const handleClick = () => {
    onClick();
    navigate(`/`);
  };

  return (
    <Box sx={{ width: isCollapsed ? 'auto' : '100%', display: 'flex', justifyContent: 'center' }}>
      <MUIButton
        variant="outlined"
        color="secondary"
        size="medium"
        onClick={handleClick}
        startIcon={<AddIcon />}
        sx={{
          backgroundColor: `${theme.palette.background.default}`,
          borderColor: `${theme.palette.divider}`,
          color: `${theme.palette.text.secondary}`,
          fontSize: isCollapsed ? '15px' : '14px',
          fontFamily: 'Roboto',
          fontWeight: 600,
          width: isCollapsed ? '36px' : '100%',
          height: '36px',
          padding: isCollapsed ? '0px' : '8px 22px',
          borderRadius: isCollapsed ? '50%' : '40px',
          minWidth: 'auto',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          margin: 0,
          transition: 'all 0.2s ease-in-out',
          '& .MuiButton-startIcon': {
            margin: isCollapsed ? '0px' : '0px 8px 0px -2px',
          },
          '&:hover': {
            backgroundColor: `${theme.palette.background.default}`,
            borderColor: `${theme.palette.secondary.main}`,
            borderWidth: '1px',
            color: `${theme.palette.text.secondary}`,
          },
        }}
      >
        {!isCollapsed && NEW_CHAT_BUTTON_LABEL}
      </MUIButton>
    </Box>
  );
};

export default NewChatButtonController;