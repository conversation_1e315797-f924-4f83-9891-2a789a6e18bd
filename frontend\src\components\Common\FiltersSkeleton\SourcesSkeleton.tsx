import { Box, Skeleton } from '@mui/material';
import BaseSkeleton from './BaseSkeleton';

const SourcesSkeleton = ({ hideFiltersPanel, onClose, theme }) => {
    const headerContent = (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
                sx={{
                    width: '71px',
                    height: '20px',
                    borderRadius: '4px',
                    background: '#F2F6FC',
                }}
            />
        </Box>
    );

    const bodyContent = (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                overflowY: 'auto',
                flexGrow: 1,
                p: 1.5,
                pt: 1
            }}
        >
            {Array.from(new Array(4)).map((_, index) => (
                <Box
                    key={index}
                    sx={{
                        flexGrow: 1,
                        mb: 1.5,
                        display: 'flex'
                    }}
                >
                    <Skeleton variant="rectangular" sx={{ width: '100%', height: '100%' }} />
                </Box>
            ))}
        </Box>
    );

    return (
        <BaseSkeleton
            hideFiltersPanel={hideFiltersPanel}
            onClose={onClose}
            theme={theme}
            rightHeaderContent={headerContent}
            rightBodyContent={bodyContent}
            showFilterIcon={false}
        />
    );
};

export default SourcesSkeleton;