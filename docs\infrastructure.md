# Infrastructure and Deployment

This document describes the infrastructure setup, deployment configuration, and management scripts for the ImpactAI platform.

## Cloud Architecture

### Google Cloud Platform Components

1. **Cloud Run Services**
   - Backend API service
   - Agent service
   - Proxy service
   - Website service

2. **Load Balancing**
   - Global HTTPS load balancer
   - Cloud CDN integration
   - SSL certificate management
   - Static IP allocation

3. **Security**
   - Cloud Armor policies
   - VPC configuration
   - IAM roles and permissions
   - Service account management

4. **Storage**
   - Cloud Storage buckets
   - Static content delivery
   - Build artifacts storage

## Database Configuration

### Connection Pool Settings

The database layer uses optimized connection pooling:

#### Core Database Pool Settings
- **Pool Size**: 20 concurrent connections
- **Max Overflow**: 20 additional connections during traffic spikes
- **Pool Timeout**: 30 seconds for connection acquisition
- **Pool Recycle**: 1800 seconds (30 minutes) for connection refresh
- **Pre-ping**: Enabled for automatic stale connection detection
- **Connect Timeout**: 10 seconds for new connection establishment

#### Features
- **Compiled Cache**: Query compilation caching for fast execution
- **Session Management**: Context-managed sessions with automatic cleanup
- **Error Handling**: Robust rollback mechanisms with comprehensive logging
- **Connection Health**: Automatic monitoring and replacement of failed connections

#### Research Operations Support
The configuration provides:
- **High concurrent capacity** for handling research queries
- **Fast response times** for paper abstract retrieval
- **Reliable performance** during peak usage periods
- **Efficient resource utilization** across development and production environments

## Database Migration Management

The platform uses **Alembic** for database schema versioning and migration management. This provides a robust, version-controlled approach to database schema changes across all environments.

### Alembic Configuration

#### Core Components
- **`alembic/`** - Main Alembic configuration directory
- **`alembic/versions/`** - Individual migration files
- **`alembic/env.py`** - Alembic environment configuration
- **`alembic.ini`** - Alembic settings and database connection
- **`database/models.py`** - SQLAlchemy model definitions

#### Migration Workflow
1. **Model Changes** - Update SQLAlchemy models in `database/models.py`
2. **Migration Generation** - Create migration files using `make db-create`
3. **Review** - Examine generated migration in `alembic/versions/`
4. **Apply** - Deploy migration using `make db-migrate`
5. **Verify** - Check migration status with `make db-current`

### Make Commands for Database Management

The backend provides convenient Make commands for common database operations:

| Command | Description | Usage |
|---------|-------------|-------|
| `make db-current` | Show current migration state | `make db-current` |
| `make db-history` | Show all migration history | `make db-history` |
| `make db-create` | Create new migration (interactive) | `make db-create` |
| `make db-migrate` | Apply all pending migrations | `make db-migrate` |
| `make db-downgrade` | Rollback one migration | `make db-downgrade` |
| `make db-downgrade-to REV=id` | Rollback to specific revision | `make db-downgrade-to REV=abc123` |
| `make db-reset` | Reset database (interactive) | `make db-reset` |

### Environment Setup for Migrations

Ensure these environment variables are configured:

```bash
# Local Development
export MYSQL_HOST=localhost
export MYSQL_DATABASE=impactai-silver-db
export MYSQL_USER=your_username
export MYSQL_PASSWORD=your_password

# Cloud Environments
# Variables are managed via Google Cloud Secret Manager
# and injected during deployment
```

### Migration Best Practices

#### Development Workflow
1. **Always create migrations for model changes**:
   ```bash
   # Edit database/models.py
   # Add new field: profile_picture = Column(String(255), nullable=True)
   
   # Create migration
   make db-create
   # Enter: "Add profile picture to user model"
   
   # Review generated migration in alembic/versions/
   # Apply migration
   make db-migrate
   ```

2. **Test migrations in development first**:
   ```bash
   # Check current state
   make db-current
   
   # Apply pending migrations
   make db-migrate
   
   # Verify application works correctly
   ```

#### Production Deployment
1. **Migrations are applied automatically** during service deployment
2. **Backup before major migrations** using infrastructure scripts
3. **Monitor migration logs** in Cloud Run service logs
4. **Rollback capability** using `make db-downgrade` if needed

#### Troubleshooting Common Issues

**"Target database is not up to date"**
```bash
make db-current  # Check current state
make db-migrate  # Apply pending migrations
```

**"Cannot drop index: needed in foreign key constraint"**
- Delete problematic migration and create empty sync migration:
  ```bash
  # Remove problematic migration
  rm alembic/versions/problematic_migration_id.py
  
  # Create empty sync migration
  poetry run alembic revision -m "Sync models with database"
  
  # Apply the empty migration
  make db-migrate
  ```

**"Revision not found"**
```bash
make db-history  # Find correct revision ID
```

### Integration with Infrastructure Scripts

Database migrations integrate seamlessly with existing infrastructure:

- **Deployment**: Migrations are applied automatically during service deployment
- **Database Sync**: Migration state is preserved when syncing between environments
- **Backup/Restore**: Migration history is maintained during database operations
- **Environment Management**: Each environment maintains its own migration state

## Deployment Configuration

### Service Deployments

All service deployments are managed via shell scripts and Makefile commands. There are no YAML or nginx.conf files required for deployment. Each service is deployed to Google Cloud Run with environment configuration handled via environment variable files and Cloud Secret Manager.

1. **Backend Service**
   - Region: us-east1
   - Platform: Cloud Run managed
   - Memory: Variable allocation
   - VPC Connector: Enabled
   - Authentication: Public access
   - Database Pool: 20 connections

2. **Frontend Static Site**
   - Bucket: GCS static hosting
   - CDN: Enabled
   - Cache Control: Optimized
   - SSL: Custom domain

3. **Agent Service**
   - Region: us-east1
   - Memory: Configurable
   - Service Account: Custom compute SA
   - VPC Access: Private ranges

4. **Website Service**
   - Memory: 512Mi
   - CPU: 1 core
   - Port: 3000
   - Environment: Configurable

### Load Balancer Setup

- Global HTTPS load balancer
- Serverless NEG configuration
- Cloud Armor security policy
- SSL certificate management
- Static IP allocation

## Deployment Process

### Automated Deployments

All deployments are performed using Makefile commands, which invoke shell scripts to build and deploy each service to Cloud Run. Example commands:

- Backend Deployment:
  ```bash
  make deploy-backend
  ```
- Frontend Deployment:
  ```bash
  make deploy-frontend
  ```
- Website Deployment:
  ```bash
  make deploy-website
  ```
- Agent Deployment:
  ```bash
  make deploy-agent
  ```

### Environment Configuration

- Development and production environments
- Secret management via Cloud Secret Manager
- Environment variable injection using env files
- Service-specific configurations

### Security Measures

1. **Cloud Armor Rules**
   - IP range restrictions
   - DDoS protection
   - Custom security policies

2. **VPC Configuration**
   - Private connectivity
   - Egress control
   - Service isolation

3. **SSL/TLS**
   - Managed certificates
   - Custom domain support
   - Secure communication

## Monitoring and Maintenance

### Health Checks
- Automated service monitoring
- Load balancer health probes
- Database connection pool monitoring
- Error rate tracking
- Performance metrics

### Scaling
- Automatic scaling configuration
- Resource allocation
- Traffic management
- Load distribution
- Database connection scaling

### Logging
- Centralized logging
- Database session lifecycle tracking
- Connection pool performance metrics
- Error tracking
- Audit trails
- Performance monitoring

## Infrastructure Management Scripts

The `infrastructure/` directory contains a collection of shell scripts for managing deployments, database operations, and environment configuration. All scripts follow consistent patterns with shared libraries, comprehensive error handling, and dry-run capabilities.

### Architecture

#### Shared Libraries (`infrastructure/lib/`)

All infrastructure scripts leverage a modular library system for consistency and maintainability:

- **`common.sh`** - Core utilities (error handling, validation, help formatting)
- **`gcloud.sh`** - Google Cloud operations and authentication
- **`deploy.sh`** - Cloud Run deployment functions
- **`logging.sh`** - Color-coded logging functions
- **`colors.sh`** - Terminal color constants
- **`container.sh`** - Container engine detection (Docker/Podman)

#### Script Features

All infrastructure scripts include:
- **Strict error handling** with `set -euo pipefail`
- **Dry-run mode** for validation without execution
- **Comprehensive logging** with color-coded output
- **Help documentation** with `--help` flag
- **Cleanup functions** for resource management
- **Input validation** and environment checks

### Core Scripts

#### 1. Service Deployment (`deploy-service.sh`)

Deploys backend services to Google Cloud Run with full environment configuration.

**Usage:**
```bash
# Deploy backend service to development
TARGET=development SERVICE=backend ./infrastructure/deploy-service.sh

# Validate deployment without executing
TARGET=production SERVICE=agent ./infrastructure/deploy-service.sh --dry-run
```

**Environment Variables:**
- `TARGET` - Deployment environment (`development`, `testing`, `production`)
- `SERVICE` - Service directory name to deploy

**Features:**
- Builds and pushes Docker images to Google Container Registry
- Configures Cloud Run services with VPC connectivity
- Manages environment variables via Secret Manager
- Sets up database connections and service accounts
- Handles service-specific configurations (memory, CPU, scaling)

**Process:**
1. Validates environment variables and authentication
2. Fetches environment configuration from Secret Manager
3. Builds Docker image with timestamp tag
4. Pushes image to Container Registry
5. Deploys service to Cloud Run with environment config
6. Verifies deployment success

#### 2. Environment Configuration (`check-env.sh`)

Fetches secrets from Google Cloud Secret Manager and converts them to YAML format for service deployment.

**Usage:**
```bash
# Fetch environment config for backend service
TARGET=production SERVICE=backend ./infrastructure/check-env.sh

# Validate configuration access
TARGET=development SERVICE=agent ./infrastructure/check-env.sh --dry-run
```

**Environment Variables:**
- `TARGET` - Environment to fetch secrets from
- `SERVICE` - Service name for secret identification

**Features:**
- Retrieves service-specific secrets from Secret Manager
- Converts key-value pairs to YAML format
- Handles special cases (testing uses production secrets)
- Creates temporary environment files for deployment
- Validates secret accessibility and permissions

**Output:**
- Creates `env.${SERVICE}.tmp.yml` file for deployment
- Temporary files are automatically cleaned up after use

#### 3. Service Account Management (`fetch-service-key.sh`)

Downloads service account keys from Secret Manager for local development.

**Usage:**
```bash
# Fetch service account key for local development
./infrastructure/fetch-service-key.sh

# Validate key access
./infrastructure/fetch-service-key.sh --dry-run
```

**Features:**
- Downloads compute service account key from Secret Manager
- Creates `agent/.secrets/` directory structure
- Saves key as `service-account-key.json` for local use
- Handles permission validation and error cases
- Provides clear feedback on success/failure

**Use Cases:**
- Local development environment setup
- Agent service authentication for Cloud SQL access
- Development database connectivity testing

### Database Management Scripts

#### 4. Production to Development Sync (`sync-production-database.sh`)

Safely exports production database and imports it to development environment.

**Usage:**
```bash
# Sync production data to development
./infrastructure/sync-production-database.sh

# Validate sync operation
./infrastructure/sync-production-database.sh --dry-run
```

**Features:**
- Exports production database (`impactai-db-production`) to Cloud Storage
- Imports data to development database (`impactai-db`)
- Comprehensive pre-flight validation
- Automatic cleanup of temporary export files
- Safety checks to prevent accidental data loss
- **Migration State Preservation**: Maintains Alembic migration history during sync

**Process:**
1. Validates Cloud SQL instances exist and are accessible
2. Checks Google Cloud Storage bucket permissions
3. Exports production database to timestamped SQL file
4. Downloads and imports to development database
5. Cleans up temporary files from Cloud Storage
6. **Migration Verification**: Ensures migration state is consistent

**Safety Measures:**
- Read-only access to production database
- Validates target environment before import
- Comprehensive logging of all operations
- Automatic rollback on failure
- **Migration Safety**: Preserves Alembic version table during sync operations

**Migration Considerations:**
- The sync process includes the `alembic_version` table to maintain migration state
- After sync, verify migration state with `make db-current`
- If migration state is inconsistent, use `make db-stamp head` to sync

#### 5. Development to Local Sync (`sync-local-database.sh`)

Advanced script for syncing development database to local MySQL with comprehensive safety features.

**Usage:**
```bash
# Download and import to local MySQL (default)
./infrastructure/sync-local-database.sh

# Download only, skip MySQL import
./infrastructure/sync-local-database.sh --skip-import

# Skip download, import existing file
./infrastructure/sync-local-database.sh --skip-download

# Import without confirmation prompts (automation)
./infrastructure/sync-local-database.sh --force

# Validate all operations
./infrastructure/sync-local-database.sh --dry-run
```

**Environment Variables (`.env` file):**
```env
MYSQL_HOST=localhost
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_CORE_DATABASE=impactai-silver-db  # optional, defaults to impactai-silver-db
```

**Advanced Features:**
- **Multi-mode operation** - download only, import only, or both
- **Safety validation** - only allows localhost MySQL connections
- **Database management** - creates databases, confirms replacements
- **File management** - automatic directory creation, conflict resolution
- **Progress tracking** - detailed import statistics and timing
- **Error recovery** - comprehensive error handling and troubleshooting
- **Resource monitoring** - disk space checking, connection validation
- **Migration State Sync** - preserves Alembic migration history for local development

**Safety Features:**
- **Host validation** - Blocks non-localhost connections to prevent production accidents
- **Confirmation prompts** - Warns about database replacement with table counts
- **File validation** - Verifies SQL file integrity and format
- **Environment isolation** - Strict separation between cloud and local operations
- **Cleanup mechanisms** - Automatic cleanup of failed operations

**Process Flow:**
1. **Pre-flight validation** - Commands, directories, environment variables
2. **Database file management** - Download new or use existing files
3. **MySQL validation** - Connection testing, database existence checking
4. **Data operations** - Export from Cloud SQL, download, import to MySQL
5. **Cleanup and reporting** - Temporary file cleanup, detailed summary

**Output Example:**
```
===============================================
          ✅ OPERATION SUMMARY
===============================================
📂 Local File: silverdb-20241220-143022.sql (25MB)
🔄 Operations Performed:
   ☁️  Exported from Cloud SQL: impactai-db
   📥 Downloaded to local directory
   🗄️  Imported to MySQL: localhost:impactai-silver-db
💡 Database is ready for use!
   Connect with: mysql -h localhost -u user -p impactai-silver-db
===============================================
```

### Best Practices

#### Script Usage Patterns

1. **Always use dry-run first**:
   ```bash
   ./infrastructure/script-name.sh --dry-run
   ./infrastructure/script-name.sh  # Execute after validation
   ```

2. **Environment variable validation**:
   ```bash
   # Check required variables before running
   echo "TARGET=$TARGET SERVICE=$SERVICE"
   ```

3. **Error handling**:
   - All scripts use strict error handling (`set -euo pipefail`)
   - Failed operations trigger automatic cleanup
   - Detailed error messages with troubleshooting tips

#### Security Considerations

- **Authentication**: All scripts require valid `gcloud` authentication
- **Permissions**: Scripts validate required IAM permissions before execution
- **Local MySQL**: Database sync scripts only allow localhost connections
- **Secret Management**: Environment variables are fetched from Secret Manager
- **Cleanup**: Temporary files are automatically removed after use

#### Monitoring and Troubleshooting

- **Logging**: All operations are logged with timestamps and color coding
- **Validation**: Comprehensive pre-flight checks before any destructive operations
- **Recovery**: Clear error messages with specific troubleshooting steps
- **Dry-run**: All scripts support validation mode for testing

### Integration with Makefile

These infrastructure scripts are integrated with the main Makefile for convenience:

```bash
# Service deployments
make deploy-backend    # Calls deploy-service.sh with backend configuration
make deploy-agent      # Calls deploy-service.sh with agent configuration

# Database operations
make sync-prod-db      # Calls sync-production-database.sh
make sync-local-db     # Calls sync-local-database.sh

# Environment setup
make fetch-env         # Calls check-env.sh for environment configuration
make fetch-keys        # Calls fetch-service-key.sh for service account keys
```

This integration provides a consistent interface while maintaining the flexibility and power of the underlying shell scripts.

## Quick Reference

### Common Development Workflows

#### Setting Up Local Development
```bash
# 1. Fetch service account key for Cloud SQL access
./infrastructure/fetch-service-key.sh

# 2. Sync development database to local MySQL
./infrastructure/sync-local-database.sh

# 3. Verify local database connection
mysql -h localhost -u your_user -p impactai-silver-db

# 4. Check migration state (if using backend)
cd backend && make db-current
```

#### Deploying Services
```bash
# 1. Validate deployment configuration
TARGET=development SERVICE=backend ./infrastructure/deploy-service.sh --dry-run

# 2. Deploy to development environment
TARGET=development SERVICE=backend ./infrastructure/deploy-service.sh

# 3. Deploy to production
TARGET=production SERVICE=backend ./infrastructure/deploy-service.sh
```

#### Database Operations
```bash
# Sync production data to development (use carefully!)
./infrastructure/sync-production-database.sh --dry-run
./infrastructure/sync-production-database.sh

# Update local development database
./infrastructure/sync-local-database.sh

# Import existing database file without downloading
./infrastructure/sync-local-database.sh --skip-download

# Database Migration Operations (backend directory)
cd backend

# Check migration status
make db-current

# Create new migration from model changes
make db-create

# Apply pending migrations
make db-migrate

# View migration history
make db-history

# Rollback last migration
make db-downgrade

# Reset database (development only)
make db-reset
```

### Troubleshooting

#### Authentication Issues
```bash
# Check gcloud authentication
gcloud auth list
gcloud auth application-default login

# Verify project setting
gcloud config get-value project
```

#### Database Connection Problems
```bash
# Test Cloud SQL connectivity
gcloud sql instances describe impactai-db

# Check local MySQL service
systemctl status mysql  # Linux
brew services list | grep mysql  # macOS
```

#### Migration Issues
```bash
# Check migration state
cd backend && make db-current

# View migration history
make db-history

# Apply pending migrations
make db-migrate

# If migration state is inconsistent, stamp current state
poetry run alembic stamp head

# Reset database (development only - destroys all data!)
make db-reset
```

#### Deployment Failures
```bash
# Check Cloud Run service logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=impactai-backend-development"

# Verify container image exists
gcloud container images list --repository=gcr.io/your-project-id
```

### Environment Variables Reference

#### Required for Deployments
- `TARGET` - Environment (`development`, `testing`, `production`)
- `SERVICE` - Service name (`backend`, `agent`, `website`)

#### Required for Local Database Sync (`.env` file)
- `MYSQL_HOST` - Must be `localhost` for safety
- `MYSQL_USER` - Local MySQL username
- `MYSQL_PASSWORD` - Local MySQL password
- `MYSQL_CORE_DATABASE` - Database name (optional, defaults to `impactai-silver-db`)

#### Required for Database Migrations (backend directory)
- `MYSQL_HOST` - Database host (localhost for local development)
- `MYSQL_DATABASE` - Database name for migrations
- `MYSQL_USER` - Database username with migration permissions
- `MYSQL_PASSWORD` - Database password
- **Note**: Cloud environments use Secret Manager for these variables

### Script Exit Codes

All infrastructure scripts follow consistent exit code patterns:
- `0` - Success
- `1` - General error (validation failure, missing dependencies)
- `2` - Authentication error
- `3` - Permission error
- `130` - Script interrupted by user (Ctrl+C)

### Safety Features Summary

| Script | Safety Features |
|--------|-----------------|
| `deploy-service.sh` | Dry-run validation, environment checks, rollback on failure |
| `check-env.sh` | Permission validation, temporary file cleanup |
| `fetch-service-key.sh` | Local-only operation, secure file permissions |
| `sync-production-database.sh` | Read-only production access, comprehensive validation, migration state preservation |
| `sync-local-database.sh` | Localhost-only MySQL, confirmation prompts, file validation, migration state sync |
| `make db-*` | Interactive confirmations, migration state tracking, rollback capabilities |

All scripts implement comprehensive error handling, automatic cleanup, and detailed logging to ensure safe and reliable operations.
