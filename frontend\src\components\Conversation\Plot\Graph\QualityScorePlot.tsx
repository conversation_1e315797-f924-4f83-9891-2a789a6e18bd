import { useRef, useEffect, useState } from "react";
import * as d3 from "d3";

import "./QualityScorePlot.css";

const QualityScorePlot = ({ plotData, chartWidth }) => {
  console.log("STUDIES YEAR PLOT DATA", plotData);

  const uniqueStudies = plotData.map((d) => ({
    ...d,
    quality_score_group:
      d.quality_score <= 0.3
        ? "Low"
        : d.quality_score <= 0.7
        ? "Medium"
        : "High",
  }));

  const byQualityScore = d3.groups(uniqueStudies, (d) => d.quality_score);
  const byQualityScoreGroup = d3.groups(
    uniqueStudies,
    (d) => d.quality_score_group
  );
  console.log(byQualityScore, byQualityScoreGroup);

  const margin = {
    top: 20,
    right: 40,
    bottom: 20,
    left: 0,
  };

  const height = 200;

  const w = chartWidth - margin.left - margin.right;
  const h = height - margin.top - margin.bottom;

  const xScale = d3
    .scaleBand()
    .domain(["Low", "Medium", "High"])
    .range([0, w])
    .padding(0.1);

  const yScale = d3
    .scaleLinear()
    .domain([0, d3.max(byQualityScoreGroup, (d) => Math.max(10, d[1].length))])
    .range([0, h]);

  return (
    <div className="studies-year-plot container">
      <svg width={chartWidth} height={height}>
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {byQualityScoreGroup.map((d) => (
            <g transform={`translate(${xScale(d[0])}, 0)`}>
              <rect
                width={xScale.bandwidth()}
                y={h - yScale(d[1].length)}
                height={yScale(d[1].length)}
                fill="black"
              />
              <g transform={`translate(${xScale.bandwidth() / 2}, 0)`}>
                <text
                  y={h - yScale(d[1].length)}
                  dy={-4}
                  style={{
                    textAnchor: "middle",
                    fontSize: 12,
                    fill: "rgb(22 54 97)",
                  }}
                >
                  {d[1].length}
                </text>
                <text
                  y={h}
                  dy={12}
                  style={{
                    textAnchor: "middle",
                    fontSize: 12,
                    fill: "rgb(22 54 97)",
                  }}
                >
                  {d[0]}
                </text>
              </g>
            </g>
          ))}
          {/* <g>
            <line x1={0} x2={w} y1={h} y2={h} />
            {xScale.ticks(10).map((tick) => (
              <g
                className="tick"
                transform={`translate(${xScale(tick)}, ${h})`}
              >
                <line y2={4} />
                    {tick <= 1 && <text dx={w / 11 / 2} dy={20}>{tick}</text>}
              </g>
            ))}
          </g> */}
        </g>
      </svg>
    </div>
  );
};

export default QualityScorePlot;
