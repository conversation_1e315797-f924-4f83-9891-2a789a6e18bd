import { defineConfig } from "eslint/config";

export default defineConfig([
	{
		files: ["**/*.js", "**/*.cjs", "**/*.mjs"],
		languageOptions: {
			ecmaVersion: 2020,
			sourceType: "module",
			parser: await import("@typescript-eslint/parser"),
			parserOptions: {
				ecmaFeatures: {
					jsx: true,
				},
				project: ["./tsconfig.json"]
			},
			globals: {
				browser: true,
				es2020: true
			}
		},

		rules: {
			"prefer-const": "warn",
			"no-constant-binary-expression": "error",
		},
	},
]);
