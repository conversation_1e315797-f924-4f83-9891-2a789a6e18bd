"use client";
import { useState } from "react";
import { Box, Grid } from "@mui/material";
import Section1 from "./Section1";
import FaqInfo from "./FaqInfo";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import DynamicPageTitle from '../components/DynamicPageTitle';

const FAQ = () => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const [chipId, setChipId] = useState<string | null>(null);

    const handleChipClick = (id: string) => {
        setChipId(id);
    };

    return (
        <>
            <DynamicPageTitle
                title="Impact AI - Frequently Asked Questions"
                description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
            />
            <Box
                sx={{
                    width: "100%",
                    background: "none",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: 0,
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "30px",
                        padding: 0,
                        width: "100%",
                        alignItems: "center",
                    }}
                >
                    <Grid container sx={{ mb: 0, p: 0 }}>
                        <Grid item xs={12} sx={{ p: 0, mt: isMobile ? "48px" : isTablet ? "90px" : "100px", }}>
                            <Section1 onChipClick={handleChipClick} />
                        </Grid>
                        <Grid item xs={12}
                            sx={{
                                p: 0,
                                mt: isMobile ? "48px" : isTablet ? "122px" : "132px",
                            }}>
                            <FaqInfo chipId={chipId} />
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </>
    );
};

export default FAQ;
