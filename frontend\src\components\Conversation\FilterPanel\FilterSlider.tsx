import React, { useState, SyntheticEvent, useEffect } from 'react';
import {
  Box,
  Typography,
  Slider,
  styled,
  useTheme,
} from '@mui/material';

const StyledSlider = styled(Slider)(({ theme }) => ({
  '& .MuiSlider-rail': {
    height: '2px',
    borderRadius: '100px',
    opacity: 0.38,
    backgroundColor: theme.elevation.paperElevationSixteen,
  },
  '& .MuiSlider-track': {
    height: '2px',
    borderRadius: '100px',
    background: 'none',
    border: 'none',
  },
  '& .MuiSlider-mark': {
    width: '2px',
    height: '2px',
    borderRadius: '100px',
    opacity: 0.38,
    background: theme.palette.action.active,
  },
  '& .MuiSlider-markActive': {
    background: theme.palette.action.active,
  },
  '& .MuiSlider-thumb': {
    width: '12px',
    height: '12px',
    border: 'none',
    boxShadow:
      '0 1px 5px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.05), 0 3px 1px -2px rgba(0, 0, 0, 0.05)',
    backgroundColor: theme.palette.action.disabledBackground,
    '&::before': {
      backgroundColor: '#D4E4FC',
    },
  },
  '& .MuiSlider-valueLabel': {
    top: -10,
    backgroundColor: 'transparent',
    color: theme.palette.text.secondary,
    padding: 0,
    '&::before': {
      display: 'none',
    },
    '& .MuiSlider-valueLabelLabel': {
      fontSize: 13,
      fontWeight: 600,
      lineHeight: '12px',
      letterSpacing: '0.15px',
    },
  },
}));

interface FilterSliderProps {
  label: string;
  min: number;
  max: number;
  activeRange: number[];
  onChangeCommitted: (range: number[]) => void;
}

const FilterSlider: React.FC<FilterSliderProps> = ({
  label,
  min,
  max,
  activeRange,
  onChangeCommitted,
}) => {
  const theme = useTheme();

  const initialValue = activeRange && activeRange.length === 2 ? activeRange : [min, max];
  const [value, setValue] = useState<number[]>(initialValue);

  useEffect(() => {
    if (activeRange && activeRange.length === 2) {
      setValue(activeRange);
    }
  }, [activeRange]);

  const handleYearChange = (event: Event, newValue: number | number[]) => {
    if (Array.isArray(newValue)) {
      setValue(newValue);
    }
  };

  const handleYearChangeCommitted = (
    event: Event | SyntheticEvent,
    newValue: number | number[]
  ) => {
    if (Array.isArray(newValue)) {
      onChangeCommitted(newValue);
    }
  };

  const labelStyles = {
    color: 'rgba(0, 51, 128, 0.70)',
    fontFeatureSettings: "'liga' off, 'clig' off",
    fontFamily: 'Roboto',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 600,
    lineHeight: '12px',
    letterSpacing: '0.15px',
    mb: 2,
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography sx={labelStyles}>{label}</Typography>
      <Box sx={{ width: '100%' }}>
        <StyledSlider
          aria-label="Year range"
          value={value}
          onChange={handleYearChange}
          onChangeCommitted={handleYearChangeCommitted}
          valueLabelDisplay="auto"
          step={1}
          marks
          min={min}
          max={max}
        />
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
        <Typography
          sx={{
            color: theme.palette.text.secondary,
            fontSize: '13px',
            fontWeight: 600,
          }}
        >
          {value[0]}
        </Typography>
        <Typography
          sx={{
            color: theme.palette.text.secondary,
            fontSize: '13px',
            fontWeight: 600,
          }}
        >
          {value[1]}
        </Typography>
      </Box>
    </Box>
  );
};

export default FilterSlider;