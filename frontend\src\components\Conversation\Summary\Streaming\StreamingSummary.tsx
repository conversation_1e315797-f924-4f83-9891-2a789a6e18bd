import { useState, useEffect, useRef, useCallback, useContext } from "react";
import { BASE_URL } from "../../../../services/apiService";
import { Message } from "../../../../types/ConversationTypes";
import { Box } from '@mui/material';
import MemoizedMarkdown from "./MemoizedMarkdown";
import { NormalizeSpaces } from "./Utils";
import { LayoutContext } from "../../../Layout/LayoutContext";
import SystemLoadingIndicator from "./SystemLoadingIndicator";
import usePlotAndSourcesData from "../../../../hooks/usePlotAndSourceData";
import PlaceholderComponent from "../../../Common/PlaceholderComponent";

interface StreamingSummaryProps {
  conversationId: string;
  informationMessageId: string;
  setDisplaySystemLoader: (flag: boolean) => void;
  setStreamingEnded: (flag: boolean) => void;
  setSummaryStreamedText: (text: string | null) => void;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  messageId: string;
  setDisplayText: (text: string | null) => void;
  setFullMessageInfoFetched: (message: Message) => void;
}

function safeJsonParse<T = any>(value: string): T | string {
  try {
    return JSON.parse(value);
  } catch {
    return value;
  }
}

const StreamingSummary: React.FC<StreamingSummaryProps> = ({
  conversationId,
  informationMessageId,
  setDisplaySystemLoader,
  setStreamingEnded,
  setSummaryStreamedText,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  messageId,
  setDisplayText,
  setFullMessageInfoFetched
}) => {
  const {
    streamingError,
    updateStreamingErrorState
  } = useContext(LayoutContext);
  const [displayChunks, setDisplayChunks] = useState<any[]>([]);
  const chunkQueueRef = useRef<any[]>([]);
  const processingChunkRef = useRef(false);
  const MAX_RETRIES = 1;
  const [hasReceivedText, setHasReceivedText] = useState(false);
  const [currentTextLength, setCurrentTextLength] = useState(0);
  const [systemLoaderData, setSystemLoaderData] = useState<{ title: string; description: string } | null>(null);
  const accumulatedTextRef = useRef("");
  const currentChunkIdCounter = useRef(0);
  const placeholderChunkIdsRef = useRef<{ code?: string; table?: string }>({});
  const [isStreamingFinished, setIsStreamingFinished] = useState(false);
  // Track whether any chunk has been rendered to avoid premature finish
  const [hasStreamingStarted, setHasStreamingStarted] = useState(false);
  // Track server stream completion so we only mark finished after the stream ends
  const hasSeenStreamEndRef = useRef(false);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const currentProcessingChunkIdRef = useRef<string | null>(null);
  // Page visibility tracking for background tab handling
  const [wasInBackground, setWasInBackground] = useState(false);
  const backgroundChunkQueue = useRef<any[]>([]);

  const {
    fullMessageInfo,
    hasAttemptedGetInfoFetch,
  } = usePlotAndSourcesData({
    conversationId: conversationId,
    informationMessageId: informationMessageId,
    hasStreamingEndedForDataFetch: isStreamingFinished,
  });

  // Handle background chunk processing
  const processBackgroundChunks = useCallback(() => {
    if (backgroundChunkQueue.current.length > 0) {
      const chunks = backgroundChunkQueue.current;
      backgroundChunkQueue.current = [];

      // Add chunks with faster but still sequential timing
      chunks.forEach((chunk, index) => {
        setTimeout(() => {
          chunkQueueRef.current.push(chunk);
          if (!processingChunkRef.current) {
            processNextChunk();
          }
        }, index * 100); // 100ms between chunks instead of 400ms
      });
    }
  }, []);

  // Listen for visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Tab going to background - start accumulating chunks
        setWasInBackground(true);
      } else if (wasInBackground) {
        // Tab coming back to foreground - process accumulated chunks with controlled timing
        setWasInBackground(false);
        processBackgroundChunks();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [wasInBackground, processBackgroundChunks]);

  const processNextChunk = useCallback(() => {
    if (chunkQueueRef.current.length > 0 && !processingChunkRef.current) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      processingChunkRef.current = true;
      const nextChunk = chunkQueueRef.current.shift();
      if (!hasStreamingStarted) {
        setHasStreamingStarted(true);
      }
      if (nextChunk) {
        currentProcessingChunkIdRef.current = nextChunk.id;
        setDisplayChunks(prevChunks => [...prevChunks, { ...nextChunk, shouldAnimate: true }]);

        const newTextLength = (nextChunk.text || "").length;
        if (newTextLength > 0 && !hasReceivedText) {
          setHasReceivedText(true);
        }
        setCurrentTextLength(prevLength => prevLength + newTextLength);
        accumulatedTextRef.current += nextChunk.text || "";

        const thisChunkId = nextChunk.id as string;
        const shouldBypassDelay = typeof document !== 'undefined' && document.hidden;
        if (shouldBypassDelay) {
          // Store chunk in background queue instead of processing immediately
          backgroundChunkQueue.current.push(...chunkQueueRef.current);
          chunkQueueRef.current = [];
          // Complete current chunk immediately
          setDisplayChunks(prev => prev.map(c => c.id === thisChunkId ? { ...c, shouldAnimate: false } : c));
          processingChunkRef.current = false;
          currentProcessingChunkIdRef.current = null;
        } else {
          timeoutRef.current = setTimeout(() => {
            if (processingChunkRef.current && currentProcessingChunkIdRef.current === thisChunkId) {
              setDisplayChunks(prev => prev.map(c => c.id === thisChunkId ? { ...c, shouldAnimate: false } : c));
              processingChunkRef.current = false;
              currentProcessingChunkIdRef.current = null;
              processNextChunk();
            }
          }, 400);
        }
      } else {
        processingChunkRef.current = false;
        currentProcessingChunkIdRef.current = null;
      }
    } else {
      // If stream has ended and there is nothing left to process and no active animation, mark finished
      if (
        !processingChunkRef.current &&
        chunkQueueRef.current.length === 0 &&
        hasSeenStreamEndRef.current &&
        hasStreamingStarted &&
        !isStreamingFinished
      ) {
        setIsStreamingFinished(true);
      }
    }
  }, [hasReceivedText, hasStreamingStarted, isStreamingFinished]);

  useEffect(() => {
    if (conversationId && informationMessageId) {
      setDisplayChunks([]);
      setHasReceivedText(false);
      setCurrentTextLength(0);
      chunkQueueRef.current = [];
      backgroundChunkQueue.current = [];
      processingChunkRef.current = false;
      accumulatedTextRef.current = "";
      currentChunkIdCounter.current = 0;
      setDisplaySystemLoader(true);
      setStreamingEnded(false);
      setSummaryStreamedText(null);
      updateStreamingErrorState(false);
      setDisplayText("");
      placeholderChunkIdsRef.current = {};
      setIsStreamingFinished(false);
      setHasStreamingStarted(false);
      hasSeenStreamEndRef.current = false;
      setWasInBackground(false);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      currentProcessingChunkIdRef.current = null;

      fetchSummaryData();
    }
  }, [conversationId, informationMessageId]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleRetryOrError = async (retryCount: number) => {
    if (retryCount < MAX_RETRIES) {
      setDisplaySystemLoader(true);
      await new Promise(res => setTimeout(res, 1000));
      fetchSummaryData(retryCount + 1);
    } else {
      setDisplaySystemLoader(false);
      setStreamingEnded(true);
      updateStreamingErrorState(true);
      setSummaryStreamedText(accumulatedTextRef.current);
    }
  };

  const fetchSummaryData = useCallback(async (retryCount = 0) => {
    try {
      setDisplaySystemLoader(true);
      hasSeenStreamEndRef.current = false;
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${BASE_URL}/conversations/${conversationId}/messages/${informationMessageId}/summary`,
        {
          headers: {
            'Cache-Control': 'no-cache',
            Authorization: token ? `Bearer ${token}` : "",
          }
        }
      );

      if (response.status === 200 && response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let done = false;
        let sseBuffer = "";
        let currentBlockBuffer = "";
        let inTable = false;
        let inCodeBlock = false;

        while (!done) {
          const { value, done: streamDone } = await reader.read();
          done = streamDone;

          if (value) {
            const chunkText = decoder.decode(value, { stream: true });
            // Accumulate raw SSE text and process complete event blocks
            sseBuffer += chunkText;
            const blocks = sseBuffer.split(/\n\n/);
            sseBuffer = blocks.pop() || ""; // keep incomplete block

            for (const block of blocks) {
              if (!block.trim()) continue;
              const lines = block.split(/\r?\n/);
              let eventType: string | null = null;
              const dataLines: string[] = [];
              for (const line of lines) {
                if (line.startsWith('event:')) {
                  eventType = line.slice(6).trim();
                } else if (line.startsWith('data:')) {
                  dataLines.push(line.slice(5).trim());
                }
              }

              const dataPayloadRaw = dataLines.join('\n');
              const dataPayload = safeJsonParse<{ title: string; description: string } | string>(dataPayloadRaw);

              if (eventType === 'status') {
                setDisplaySystemLoader(true);
                setSystemLoaderData(dataPayload as { title: string; description: string });
                continue;
              }

              if (eventType === 'summary') {
                setDisplaySystemLoader(false);
                const textPayload = typeof dataPayload === 'string' ? dataPayload : String(dataPayload);
                const payloadLines = textPayload.split(/\r?\n/);

                for (const line of payloadLines) {
                  const processLine = line;
                  const trimmedLine = processLine.trim();

                  if (trimmedLine.startsWith('```')) {
                    if (inCodeBlock) {
                      currentBlockBuffer += processLine + '\n';
                      const completedChunkId = placeholderChunkIdsRef.current.code;
                      if (completedChunkId) {
                        const placeholderIndex = chunkQueueRef.current.findIndex(chunk => chunk.id === completedChunkId);
                        if (placeholderIndex !== -1) {
                          const updatedChunk = { ...chunkQueueRef.current[placeholderIndex], text: NormalizeSpaces(currentBlockBuffer), type: 'code' };
                          chunkQueueRef.current.splice(placeholderIndex, 1, updatedChunk);
                        } else {
                          setDisplayChunks(prevChunks => prevChunks.map(chunk =>
                            chunk.id === completedChunkId ? { ...chunk, text: NormalizeSpaces(currentBlockBuffer), type: 'code', shouldAnimate: true } : chunk
                          ));
                        }
                        delete placeholderChunkIdsRef.current.code;
                      } else {
                        chunkQueueRef.current.push({
                          id: `chunk_${currentChunkIdCounter.current++}`,
                          text: NormalizeSpaces(currentBlockBuffer),
                          shouldAnimate: true,
                          type: 'code',
                        });
                      }
                      currentBlockBuffer = '';
                      inCodeBlock = false;
                    } else {
                      if (currentBlockBuffer.length > 0) {
                        chunkQueueRef.current.push({
                          id: `chunk_${currentChunkIdCounter.current++}`,
                          text: NormalizeSpaces(currentBlockBuffer),
                          shouldAnimate: true,
                          type: 'text',
                        });
                        currentBlockBuffer = '';
                      }
                      inCodeBlock = true;
                      inTable = false;
                      currentBlockBuffer += processLine + '\n';
                      const placeholderId = `chunk_${currentChunkIdCounter.current++}`;
                      chunkQueueRef.current.push({
                        id: placeholderId,
                        type: 'code_placeholder',
                        shouldAnimate: true,
                      });
                      placeholderChunkIdsRef.current.code = placeholderId;
                    }
                  } else if (inCodeBlock) {
                    currentBlockBuffer += processLine + '\n';
                  } else {
                    const looksLikeTableRow = trimmedLine.startsWith('|') && trimmedLine.endsWith('|');
                    const looksLikeTableSeparator = trimmedLine.match(/^\|(?:[\s:\-]+?\|)+$/);
                    const isPotentiallyTableLine = looksLikeTableRow || looksLikeTableSeparator;
                    if (isPotentiallyTableLine) {
                      if (!inTable) {
                        if (currentBlockBuffer.length > 0) {
                          chunkQueueRef.current.push({
                            id: `chunk_${currentChunkIdCounter.current++}`,
                            text: NormalizeSpaces(currentBlockBuffer),
                            shouldAnimate: true,
                            type: 'text',
                          });
                          currentBlockBuffer = '';
                        }
                        inTable = true;
                        const placeholderId = `chunk_${currentChunkIdCounter.current++}`;
                        chunkQueueRef.current.push({
                          id: placeholderId,
                          type: 'table_placeholder',
                          shouldAnimate: true,
                        });
                        placeholderChunkIdsRef.current.table = placeholderId;
                      }
                      currentBlockBuffer += processLine + '\n';
                    } else {
                      if (inTable) {
                        if (currentBlockBuffer.length > 0) {
                          const completedChunkId = placeholderChunkIdsRef.current.table;
                          if (completedChunkId) {
                            const finalTableMarkdown = NormalizeSpaces(currentBlockBuffer);
                            const placeholderIndex = chunkQueueRef.current.findIndex(chunk => chunk.id === completedChunkId);
                            if (placeholderIndex !== -1) {
                              const updatedChunk = { ...chunkQueueRef.current[placeholderIndex], text: finalTableMarkdown, type: 'table' };
                              chunkQueueRef.current.splice(placeholderIndex, 1, updatedChunk);
                            } else {
                              setDisplayChunks(prevChunks => prevChunks.map(chunk =>
                                chunk.id === completedChunkId ? { ...chunk, text: finalTableMarkdown, type: 'table', shouldAnimate: true } : chunk
                              ));
                            }
                            delete placeholderChunkIdsRef.current.table;
                          } else {
                            chunkQueueRef.current.push({
                              id: `chunk_${currentChunkIdCounter.current++}`,
                              text: NormalizeSpaces(currentBlockBuffer),
                              shouldAnimate: true,
                              type: 'table',
                            });
                          }
                          currentBlockBuffer = '';
                        }
                        inTable = false;
                      }
                      if (processLine.trim().length > 0) {
                        chunkQueueRef.current.push({
                          id: `chunk_${currentChunkIdCounter.current++}`,
                          text: NormalizeSpaces(processLine) + '\n',
                          shouldAnimate: true,
                          type: 'text',
                        });
                      }
                    }
                  }

                  if (chunkQueueRef.current.length > 0 && !processingChunkRef.current) {
                    processNextChunk();
                  }
                }
              }
            }
          }
        }
        if (inCodeBlock) {
          const completedChunkId = placeholderChunkIdsRef.current.code;
          if (completedChunkId) {
            const placeholderIndex = chunkQueueRef.current.findIndex(chunk => chunk.id === completedChunkId);
            if (placeholderIndex !== -1) {
              const updatedChunk = { ...chunkQueueRef.current[placeholderIndex], text: NormalizeSpaces(currentBlockBuffer), type: 'code' };
              chunkQueueRef.current.splice(placeholderIndex, 1, updatedChunk);
            } else {
              setDisplayChunks(prevChunks => prevChunks.map(chunk =>
                chunk.id === completedChunkId ? { ...chunk, text: NormalizeSpaces(currentBlockBuffer), type: 'code', shouldAnimate: true } : chunk
              ));
            }
            delete placeholderChunkIdsRef.current.code;
          } else {
            chunkQueueRef.current.push({
              id: `chunk_${currentChunkIdCounter.current++}`,
              text: NormalizeSpaces(currentBlockBuffer),
              shouldAnimate: true,
              type: 'code',
            });
          }
        } else if (inTable) {
          const completedChunkId = placeholderChunkIdsRef.current.table;
          if (completedChunkId) {
            const finalTableMarkdown = NormalizeSpaces(currentBlockBuffer);
            const placeholderIndex = chunkQueueRef.current.findIndex(chunk => chunk.id === completedChunkId);
            if (placeholderIndex !== -1) {
              const updatedChunk = { ...chunkQueueRef.current[placeholderIndex], text: finalTableMarkdown, type: 'table' };
              chunkQueueRef.current.splice(placeholderIndex, 1, updatedChunk);
            } else {
              setDisplayChunks(prevChunks => prevChunks.map(chunk =>
                chunk.id === completedChunkId ? { ...chunk, text: finalTableMarkdown, type: 'table', shouldAnimate: true } : chunk
              ));
            }
            delete placeholderChunkIdsRef.current.table;
          } else {
            chunkQueueRef.current.push({
              id: `chunk_${currentChunkIdCounter.current++}`,
              text: NormalizeSpaces(currentBlockBuffer),
              shouldAnimate: true,
              type: 'table',
            });
          }
        } else if (currentBlockBuffer.length > 0) {
          chunkQueueRef.current.push({
            id: `chunk_${currentChunkIdCounter.current++}`,
            text: NormalizeSpaces(currentBlockBuffer),
            shouldAnimate: true,
            type: 'text',
          });
        }
        // Mark that the server stream ended and trigger final processing/finish if needed
        hasSeenStreamEndRef.current = true;
        processNextChunk();

        setDisplaySystemLoader(false);

      } else {
        handleRetryOrError(retryCount);
      }
    } catch (error) {
      console.error("Error fetching summary:", error);
      handleRetryOrError(retryCount);
    }
  }, [conversationId, informationMessageId, handleRetryOrError, setDisplaySystemLoader, processNextChunk]);

  useEffect(() => {
    if (fullMessageInfo) {
      setFullMessageInfoFetched(fullMessageInfo);
    }
  }, [fullMessageInfo, setFullMessageInfoFetched]);

  const onChunkComplete = useCallback((completedChunkId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    setDisplayChunks(prevChunks =>
      prevChunks.map(chunk =>
        chunk.id === completedChunkId ? { ...chunk, shouldAnimate: false } : chunk
      )
    );

    if (currentProcessingChunkIdRef.current === completedChunkId) {
      processingChunkRef.current = false;
      currentProcessingChunkIdRef.current = null;
      processNextChunk();
    }
  }, [processNextChunk]);

  useEffect(() => {
    if (isStreamingFinished && hasAttemptedGetInfoFetch) {
      if (chunkQueueRef.current.length === 0) {
        if (!processingChunkRef.current) {
          setSummaryStreamedText(accumulatedTextRef.current);
          setStreamingEnded(true);
          setDisplaySystemLoader(false);
        }
      }
    }
  }, [
    isStreamingFinished,
    processingChunkRef.current,
    chunkQueueRef.current.length,
    hasAttemptedGetInfoFetch,
    fullMessageInfo,
    setStreamingEnded,
    setSummaryStreamedText,
    setDisplaySystemLoader
  ]);

  if (streamingError) {
    return null;
  }

  const shouldFlexStart = currentTextLength >= 100;
  const showInitialLoadingState = !hasReceivedText && !hasAttemptedGetInfoFetch && !streamingError;
  return (
    <Box sx={{
      display: 'flex',
      alignItems: showInitialLoadingState ? 'flex-start' : (shouldFlexStart ? 'flex-start' : 'center')
    }}>
      <Box
        sx={{
          width: 32,
          height: 32,
          marginRight: 2,
          flexShrink: 0,
          backgroundImage: showInitialLoadingState ? `url('/ImpactAI.svg')` : `url('/ImpactAI_Static_Logo.svg')`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          display: showInitialLoadingState || displayChunks.length > 0 ? 'block' : 'none',
        }}
      />
      <Box sx={{ flexGrow: 1 }} className="markdown-container">
        {showInitialLoadingState ? (
          <SystemLoadingIndicator isLoading={true} data={systemLoaderData} />
        ) : (
          displayChunks.map((chunk) => {
            if (chunk.type === 'code_placeholder') {
              return <PlaceholderComponent key={chunk.id} type="code" />;
            }
            if (chunk.type === 'table_placeholder') {
              return <PlaceholderComponent key={chunk.id} type="table" />;
            }
            return (
              <MemoizedMarkdown
                key={chunk.id}
                text={chunk.text}
                onComplete={() => onChunkComplete(chunk.id)}
                sources={fullMessageInfo?.has_sources ? (fullMessageInfo.sources || []) : []}
                plotData={fullMessageInfo?.has_plot ? (fullMessageInfo.plot || null) : null}
                onViewOnPlotClicked={onViewOnPlotClicked}
                onViewOnSourceClicked={onViewOnSourceClicked}
                messageId={messageId}
              />
            );
          })
        )}
      </Box>
    </Box>
  );
};

export default StreamingSummary;