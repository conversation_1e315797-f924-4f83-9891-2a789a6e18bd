"""Test script to analyze agent intent classification across multiple runs."""

import asyncio
import logging
import sys
import uuid
from collections import Counter, defaultdict
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.agent.main import Agent

# Minimal config for testing
DEFAULT_CONFIG = {
    "model_name": "gemini-2.0-flash-001",
    "max_iterations": 5,
    "temperature": 0.3,
    "max_tokens": 65535,
    "verbose": False,
    "debug": False,
}


class IntentTester:
    """Test agent intent classification across multiple runs."""

    def __init__(self, config: Optional[Dict] = None):
        """Initialize the intent tester."""
        self.config = config or DEFAULT_CONFIG.copy()
        self.agent: Optional[Agent] = None

        # Suppress verbose logging during testing
        logging.getLogger().setLevel(logging.WARNING)

    async def initialize_agent(self) -> None:
        """Initialize the agent for testing."""
        if self.agent is None:
            conversation_id = f"intent_test_{uuid.uuid4().hex[:8]}"
            self.agent = Agent(config=self.config, conversation_id=conversation_id)

    async def get_intent_only(self, question: str) -> Tuple[Optional[str], bool]:
        """
        Execute agent to get only the intent, stopping after first response.

        Returns:
            Tuple of (intent, success_flag)
        """
        try:
            # Ensure agent is initialized
            await self.initialize_agent()

            # Reset agent state for fresh question
            conversation_id = f"intent_test_{uuid.uuid4().hex[:8]}"
            self.agent.conversation_id = conversation_id
            self.agent.state.conversation_id = conversation_id
            self.agent.state.query = question
            self.agent.state.current_query = question
            self.agent.state.history.clear()
            self.agent.state.conversation_history.clear()
            self.agent.state.llm_outputs.clear()

            # Add conversation entry
            self.agent.state.add_conversation_entry(question)
            self.agent._sync_state_with_cache()

            # Get agent response for intent (iteration 1)
            agent_response = await self.agent._get_agent_response(question, iteration=1)

            if agent_response and agent_response.intent:
                return agent_response.intent, True
            else:
                return None, False

        except Exception as e:
            print(f"Error getting intent for question '{question}': {e}")
            return None, False

    async def test_questions_multiple_times(
        self, questions: List[str], n_runs: int = 100
    ) -> Dict[str, List[str]]:
        """
        Test each question n times and collect intents.

        Args:
            questions: List of questions to test
            n_runs: Number of times to run each question (default 100)

        Returns:
            Dictionary mapping questions to lists of intents
        """
        results = defaultdict(list)

        total_tests = len(questions) * n_runs
        completed = 0

        print(
            f"Testing {len(questions)} questions x {n_runs} runs = {total_tests} total tests"
        )
        print("=" * 60)

        for question_idx, question in enumerate(questions):
            print(f"\nQuestion {question_idx + 1}/{len(questions)}: {question[:60]}...")

            successful_runs = 0
            for run in range(n_runs):
                intent, success = await self.get_intent_only(question)

                if success and intent:
                    results[question].append(intent)
                    successful_runs += 1
                else:
                    results[question].append("ERROR")

                completed += 1

                # Progress indicator
                if (run + 1) % 10 == 0 or run == n_runs - 1:
                    print(
                        f"  Run {run + 1}/{n_runs} - Success rate: {successful_runs}/{run + 1} ({100*successful_runs/(run+1):.1f}%)"
                    )

        print(f"\n✅ Completed all {total_tests} tests!")
        return dict(results)

    def compute_intent_statistics(
        self, results: Dict[str, List[str]]
    ) -> Dict[str, Dict]:
        """
        Compute statistics for each question's intent distribution.

        Args:
            results: Dictionary mapping questions to lists of intents

        Returns:
            Dictionary with statistics for each question
        """
        statistics = {}

        for question, intents in results.items():
            # Count intents
            intent_counts = Counter(intents)
            total_runs = len(intents)
            successful_runs = total_runs - intent_counts.get("ERROR", 0)

            # Calculate percentages
            intent_percentages = {
                intent: (count / total_runs) * 100
                for intent, count in intent_counts.items()
            }

            # Find most common intent (excluding errors)
            valid_intents = {k: v for k, v in intent_counts.items() if k != "ERROR"}
            most_common_intent = (
                max(valid_intents.items(), key=lambda x: x[1])[0]
                if valid_intents
                else None
            )

            statistics[question] = {
                "total_runs": total_runs,
                "successful_runs": successful_runs,
                "success_rate": (successful_runs / total_runs) * 100,
                "intent_counts": dict(intent_counts),
                "intent_percentages": intent_percentages,
                "most_common_intent": most_common_intent,
                "unique_intents": len(valid_intents),
            }

        return statistics

    def print_statistics_report(self, statistics: Dict[str, Dict]) -> None:
        """Print a formatted statistics report."""
        print("\n" + "=" * 80)
        print("INTENT CLASSIFICATION STATISTICS REPORT")
        print("=" * 80)

        for question_idx, (question, stats) in enumerate(statistics.items(), 1):
            print(f"\n📋 Question {question_idx}: {question}")
            print("-" * 60)

            # Summary stats
            print(f"Total runs: {stats['total_runs']}")
            print(
                f"Success rate: {stats['success_rate']:.1f}% ({stats['successful_runs']}/{stats['total_runs']})"
            )
            print(f"Unique intents: {stats['unique_intents']}")
            print(f"Most common intent: {stats['most_common_intent']}")

            # Intent distribution
            print("\n🎯 Intent Distribution:")
            sorted_intents = sorted(
                stats["intent_percentages"].items(), key=lambda x: x[1], reverse=True
            )

            for intent, percentage in sorted_intents:
                count = stats["intent_counts"][intent]
                if intent == "ERROR":
                    print(f"  ❌ {intent:<20} {count:>3} ({percentage:>5.1f}%)")
                else:
                    print(f"  ✅ {intent:<20} {count:>3} ({percentage:>5.1f}%)")

        # Overall summary
        print("\n" + "=" * 80)
        print("OVERALL SUMMARY")
        print("=" * 80)

        total_questions = len(statistics)
        avg_success_rate = (
            sum(s["success_rate"] for s in statistics.values()) / total_questions
        )
        all_intents = set()
        for stats in statistics.values():
            all_intents.update(
                intent for intent in stats["intent_counts"].keys() if intent != "ERROR"
            )

        print(f"Questions tested: {total_questions}")
        print(f"Average success rate: {avg_success_rate:.1f}%")
        print(f"Total unique intents observed: {len(all_intents)}")
        print(f"All intents: {', '.join(sorted(all_intents))}")

    async def cleanup(self) -> None:
        """Clean up resources."""
        if self.agent:
            await self.agent.cleanup()


async def main():
    """Main function to run intent classification tests."""
    # Example questions for testing
    test_questions = [
        "which interventions target profit most effectively? ",
        "Which inteventions improve program participant retention the most?",
        "which programs influence crop yield?",
    ]

    # Configuration
    n_runs = 100

    # Initialize tester
    tester = IntentTester()

    try:
        # Run tests
        print("🚀 Starting intent classification testing...")
        results = await tester.test_questions_multiple_times(test_questions, n_runs)

        # Compute and print statistics
        statistics = tester.compute_intent_statistics(results)
        tester.print_statistics_report(statistics)

    except KeyboardInterrupt:
        print("\n❌ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        raise
    finally:
        # Cleanup
        await tester.cleanup()


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
