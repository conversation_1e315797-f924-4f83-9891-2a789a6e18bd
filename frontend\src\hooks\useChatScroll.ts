import { useRef, useEffect, useContext } from 'react';
import { LayoutContext } from '../components/Layout/LayoutContext';

interface UseChatScrollProps {
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  displaySystemLoader: boolean;
  forceScrollToEnd: boolean;
  onForceScrollComplete: () => void;
  manualScroll?: boolean;
  onBelowChange?: (hasBelow: boolean) => void;
}

const useChatScroll = ({ scrollContainerRef, displaySystemLoader, forceScrollToEnd, onForceScrollComplete, manualScroll = false, onBelowChange }: UseChatScrollProps) => {
  const userScrolled = useRef(false);
  const observerRef = useRef<MutationObserver | null>(null);
  const ignoreNextScroll = useRef(false);
  const { isStreamingContent, updateIsStreamingContent } = useContext(LayoutContext);

  useEffect(() => {
    const el = scrollContainerRef.current;
    if (!el) {
      return;
    }

    const emitBelow = () => {
      if (!onBelowChange) return;
      const delta = el.scrollHeight - (el.scrollTop + el.clientHeight);
      onBelowChange(delta > 24);
    };

    const resetUserScrolled = () => {
      userScrolled.current = false;
    };

    const handleScroll = () => {
      if (ignoreNextScroll.current) {
        ignoreNextScroll.current = false;
        return;
      }

      const wasUserScrolled = userScrolled.current;
      userScrolled.current = true;

      if (observerRef.current && !(isStreamingContent || displaySystemLoader || forceScrollToEnd)) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }

      if (isStreamingContent && !wasUserScrolled && !displaySystemLoader) {
        updateIsStreamingContent(false);
      }

      if (manualScroll) emitBelow();
    };

    el.addEventListener('scroll', handleScroll, { passive: true });

    if (manualScroll) {
      emitBelow();
      const observer = new MutationObserver(() => {
        requestAnimationFrame(() => emitBelow());
      });
      observer.observe(el, { childList: true, subtree: true, characterData: true, attributes: true });
      observerRef.current = observer;

      if (forceScrollToEnd) {
        requestAnimationFrame(() => {
          el.scrollTop = el.scrollHeight;
          onForceScrollComplete();
          emitBelow();
        });
      }
    } else if (isStreamingContent || displaySystemLoader || forceScrollToEnd) {
      resetUserScrolled();

      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      const observer = new MutationObserver((mutationsList) => {
        if (!userScrolled.current && (isStreamingContent || displaySystemLoader || forceScrollToEnd)) {
          for (const mutation of mutationsList) {
            if (mutation.type === 'childList' || mutation.type === 'characterData' || mutation.type === 'attributes') {
              requestAnimationFrame(() => {
                ignoreNextScroll.current = true;
                el.scrollTop = el.scrollHeight;
                if (forceScrollToEnd) {
                  onForceScrollComplete();
                }
              });
              break;
            }
          }
        }
      });

      observer.observe(el, { childList: true, subtree: true, characterData: true, attributes: true });
      observerRef.current = observer;
    } else if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }

    return () => {
      el.removeEventListener('scroll', handleScroll as any);
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [isStreamingContent, scrollContainerRef, displaySystemLoader, forceScrollToEnd, onForceScrollComplete, manualScroll, onBelowChange, updateIsStreamingContent]);
};

export default useChatScroll;