"""
Refactor of **EntityExtractor** to align with the new `Tool` base‑class contract.

Highlights
~~~~~~~~~~
* Attaches a logger via `tool.<name>` namespace.
* `_extract_metadata` guarantees the three token‑count keys exist, defaulting to 0.
* Leverages the base `Tool` wrapper for automatic metadata normalisation – no other public behaviour changes.
"""

from __future__ import annotations

import json
import logging
from typing import Any, Dict, List

import aiohttp
from pydantic import BaseModel

from src.tools.base import Tool
from src.utils.url_management import get_el_tool_url

logger = logging.getLogger(__name__)

# API Configuration
ENTITY_API_URL = get_el_tool_url()


class EntityMention(BaseModel):
    id: str | int
    label: str
    mention: str = ""
    short_label: str = ""
    definition: str = ""


class ExtractedEntities(BaseModel):
    # --- existing fields unchanged -----------------------------------
    user_query: str
    country_codes: List[str] = []
    interventions: List[EntityMention] = []
    outcomes: List[EntityMention] = []
    intervention_sectors: List[EntityMention] = []
    intervention_target_populations: List[EntityMention] = []
    outcome_sectors: List[EntityMention] = []
    outcome_target_populations: List[EntityMention] = []
    metadata: Dict[str, Any] = {}

    extended_interventions: List[EntityMention] = []
    extended_outcomes: List[EntityMention] = []
    extended_intervention_sectors: List[EntityMention] = []
    extended_intervention_target_populations: List[EntityMention] = []
    extended_outcome_sectors: List[EntityMention] = []
    extended_outcome_target_populations: List[EntityMention] = []

    # -----------------------------------------------------------------
    # Existing helpers omitted for brevity (unchanged from original)
    # -----------------------------------------------------------------

    # Define entity fields for iteration
    _ENTITY_FIELDS = [
        "interventions",
        "outcomes",
        "intervention_sectors",
        "intervention_target_populations",
        "outcome_sectors",
        "outcome_target_populations",
        "extended_interventions",
        "extended_outcomes",
        "extended_intervention_sectors",
        "extended_intervention_target_populations",
        "extended_outcome_sectors",
        "extended_outcome_target_populations",
    ]

    # Field display names for better readability
    _FIELD_LABELS = {
        "interventions": "Interventions",
        "outcomes": "Outcomes",
        "intervention_sectors": "Intervention Sectors",
        "intervention_target_populations": "Intervention Target Populations",
        "outcome_sectors": "Outcome Sectors",
        "outcome_target_populations": "Outcome Target Populations",
        "extended_interventions": "Extended Interventions",
        "extended_outcomes": "Extended Outcomes",
        "extended_intervention_sectors": "Extended Intervention Sectors",
        "extended_intervention_target_populations": "Extended Intervention Target Populations",
        "extended_outcome_sectors": "Extended Outcome Sectors",
        "extended_outcome_target_populations": "Extended Outcome Target Populations",
    }


    def _entity_to_dict(self, entity: EntityMention) -> Dict[str, Any]:
        """Convert an EntityMention to dictionary format."""
        return {
            "id": entity.id,
            "label": entity.label,
            "mention": entity.mention,
            "short_label": entity.short_label,
            "definition": entity.definition,
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert the payload to the API-expected dictionary format."""
        result = {
            "user_query": self.user_query,
            "country_codes": self.country_codes,
        }

        # Add all entity fields
        for field in self._ENTITY_FIELDS:
            entities = getattr(self, field)
            if field in ["interventions", "outcomes"]:
                # Special format for interventions and outcomes
                result[field] = [self._entity_to_dict(entity) for entity in entities]
            else:
                # Standard format for other fields
                result[field] = [vars(entity) for entity in entities]

        return result

    def _format_entities(self, entities: List[EntityMention]) -> str:
        """Format a list of entities for string representation."""
        return ", ".join([str(self._entity_to_dict(entity)) for entity in entities])

    def __str__(self) -> str:
        """String representation of the user query and the extracted entities."""
        parts = [f"\nuser_query: {self.user_query}"]

        # Add countries if present
        if self.country_codes:
            parts.append(f"\nCountries: {', '.join(self.country_codes)}")

        # Add all entity fields that have content
        for field in self._ENTITY_FIELDS:
            entities = getattr(self, field)
            if entities:
                label = self._FIELD_LABELS[field]
                formatted_entities = self._format_entities(entities)
                parts.append(f"\n{label}: {formatted_entities}")

        return "\n".join(
            [
                "We have just executed the EntityExtractor tool and retrieved the following entities:",
                "Output: " + "; ".join(parts),
            ]
        )

class EntityExtractor(Tool):
    """Extract key research entities from a user query."""

    _ENTITY_FIELDS = [
        "interventions",
        "outcomes",
        "intervention_sectors",
        "intervention_target_populations",
        "outcome_sectors",
        "outcome_target_populations",
    ]

    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            name="entity_extractor",
            description="Extract countries, interventions, outcomes, sectors, target populations from user query to narrow search.",
            func=self.extract,
            arguments=[("user_query", "str")],
            outputs=[
                (
                    "entities",
                    "ExtractedEntities(...)",  # shortened for readability
                )
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.session = config.get("session")
        self.owns_session = False
        self.logger = logging.getLogger(f"tool.{self.name}")

    # ------------------------------------------------------------------
    # HTTP helpers (same as before)
    # ------------------------------------------------------------------

    async def _get_session(self) -> aiohttp.ClientSession:
        if self.session is not None and not self.session.closed:
            return self.session
        self.session = aiohttp.ClientSession()
        self.owns_session = True
        return self.session

    async def _call_entity_api(self, user_query: str) -> Dict[str, Any]:
        try:
            session = await self._get_session()
            async with session.post(
                ENTITY_API_URL,
                json={"user_text": user_query},
                headers={"Content-Type": "application/json"},
            ) as response:
                response.raise_for_status()
                return await response.json()
        except Exception as exc:  # noqa: BLE001
            self.logger.error("Entity API request failed: %s", exc)
            raise ValueError(f"Entity API request failed: {exc}")

    # ------------------------------------------------------------------
    # Conversion helpers
    # ------------------------------------------------------------------

    def _create_entity_mention(self, item: Dict[str, Any]) -> EntityMention:  # noqa: D401
        return EntityMention(
            id=item["id"],
            label=item["label"],
            mention=item.get("mention", ""),
            short_label=item.get("short_label", ""),
            definition=item.get("definition", ""),
        )

    def _expand_merged_entities(self, item: Dict[str, Any]) -> List[EntityMention]:
        entities = [self._create_entity_mention(item)]
        for mid in item.get("merged_ids", []):
            entities.append(
                EntityMention(
                    id=mid,
                    label=item["label"],
                    mention=item.get("mention", ""),
                    short_label=item.get("short_label", ""),
                    definition=item.get("definition", ""),
                )
            )
        return entities

    def _extract_regular_entities(self, api_resp: Dict[str, Any]) -> Dict[str, List[EntityMention]]:
        regular: Dict[str, List[EntityMention]] = {f: [] for f in self._ENTITY_FIELDS}
        for fld in self._ENTITY_FIELDS:
            for item in api_resp.get(fld, []):
                regular[fld].extend(self._expand_merged_entities(item))
        return regular

    def _extract_extended_entities(self, api_resp: Dict[str, Any]) -> Dict[str, List[EntityMention]]:
        extended: Dict[str, List[EntityMention]] = {}
        for fld in self._ENTITY_FIELDS:
            ext_fld = f"extended_{fld}"
            extended[ext_fld] = []
            for item in api_resp.get(fld, []):
                for grp in item.get("closest_group", []):
                    extended[ext_fld].append(
                        EntityMention(
                            id=grp["id"],
                            label=grp["label"],
                            short_label=grp.get("short_label", ""),
                            definition=grp.get("definition", ""),
                        )
                    )
        return extended

    def _extract_metadata(self, api_resp: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure token‑count keys exist even if backend omits them."""
        md = api_resp.get("metadata", {}) or {}
        for key in ("prompt_tokens", "completion_tokens", "thoughts_token_count"):
            md.setdefault(key, 0)
        return md

    def _convert_api_response(self, api_resp: Dict[str, Any], user_query: str) -> ExtractedEntities:
        try:
            ents = {
                **self._extract_regular_entities(api_resp),
                **self._extract_extended_entities(api_resp),
            }
            return ExtractedEntities(
                user_query=user_query,
                country_codes=api_resp.get("country_codes", []),
                metadata=self._extract_metadata(api_resp),
                **ents,
            )
        except Exception as exc:  # noqa: BLE001
            self.logger.error("Failed to convert API response: %s", exc)
            raise ValueError(f"Failed to convert API response: {exc}")

    # ------------------------------------------------------------------
    # Public run method
    # ------------------------------------------------------------------

    async def extract(self, user_query: str) -> ExtractedEntities:  # noqa: D401
        try:
            if self.verbose:
                self.logger.info("Extracting entities for: %s", user_query)
            api_resp = await self._call_entity_api(user_query)
            if self.verbose:
                self.logger.debug("API response: %s", json.dumps(api_resp)[:500])
            return self._convert_api_response(api_resp, user_query)
        except Exception as exc:  # noqa: BLE001
            self.logger.error("Error extracting entities: %s", exc)
            # Return empty container but attach metadata w/ zeros so downstream still works
            return ExtractedEntities(user_query=user_query, metadata={})

    # ------------------------------------------------------------------
    # Cleanup
    # ------------------------------------------------------------------

    async def cleanup(self):
        if self.owns_session and self.session and not self.session.closed:
            await self.session.close()
            self.session = None
