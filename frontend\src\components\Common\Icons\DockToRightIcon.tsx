import React from 'react';
import { SvgIcon, SvgIconProps } from '@mui/material';

interface DockToRightIconProps extends Omit<SvgIconProps, 'children'> {}

const DockToRightIcon: React.FC<DockToRightIconProps> = (props) => {
  return (
    <SvgIcon {...props} viewBox="0 -960 960 960">
      {/* Official Google Material Symbols "Dock To Right" icon */}
      <path d="M200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm120-80v-560H200v560h120Zm80 0h360v-560H400v560Zm-80 0H200h120Z"/>
    </SvgIcon>
  );
};

export default DockToRightIcon;
