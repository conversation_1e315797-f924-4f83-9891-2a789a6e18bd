import React, { useState, useEffect } from "react";
import { Grid, Box, Typography } from "@mui/material";
import { Message, Source, PlotData } from "../../../types/ConversationTypes";
import StreamingSummary from "./Streaming/StreamingSummary";
import { replaceTags, formatSummaryText } from './Streaming/Utils';
import './SummarySection.css';
import { NormalizeSpaces } from "./Streaming/Utils";
import MemoizedMarkdown from "./Streaming/MemoizedMarkdown";

interface SummarySectionProps {
  messageId: string;
  summary: Message;
  conversationId: string;
  informationMessageId: string;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  setDisplaySystemLoader: (flag: boolean) => void;
  setStreamingEnded: (flag: boolean) => void;
  setSummaryStreamedText: (text: any) => void;
  setFullMessageInfoFetched: (message: Message) => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({
  messageId,
  summary,
  conversationId,
  informationMessageId,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  setDisplaySystemLoader,
  setStreamingEnded,
  setSummaryStreamedText,
  setFullMessageInfoFetched
}) => {
  const [displayText, setDisplayText] = useState("");
  const [sources, setSources] = useState<Source[]>([]);
  const [plotData, setPlotData] = useState<PlotData[]>([]);

  useEffect(() => {
    if (summary.text && summary.text.length > 0) {
      setDisplayText(formatSummaryText(summary.text));
    }
  }, [summary]);

  useEffect(() => {
    const updatedSources = summary?.sources ?? [];
    const updatedPlotData = summary?.plot?.data ?? [];
    setSources(updatedSources);
    setPlotData(Array.isArray(updatedPlotData) ? updatedPlotData : []);
  }, [summary?.sources, summary?.plot?.data]);

  const renderMarkdownContent = (text: string) => (
    <Box sx={{
      display: 'flex',
      alignItems: text.trim().length >= 100 ? 'flex-start' : 'center'
    }}>
      {text.trim().length > 0 && (
        <Box
          sx={{
            width: 32,
            height: 32,
            marginRight: 2,
            flexShrink: 0,
            backgroundImage: `url('/ImpactAI_Static_Logo.svg')`,
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
          }}
        />
      )}
      <Box sx={{ flexGrow: 1 }} className="markdown-container">
        <MemoizedMarkdown
          text={replaceTags(NormalizeSpaces(text), sources, { data: plotData } as any)}
          onComplete={() => {}}
          sources={sources}
          plotData={summary?.plot || null}
          onViewOnPlotClicked={onViewOnPlotClicked}
          onViewOnSourceClicked={onViewOnSourceClicked}
          messageId={messageId}
        />
      </Box>
    </Box>
  );

  return (
    <Grid container spacing={0} id={`/conversations/${conversationId}`} summary-trigger={messageId}>
      <Grid size={{ xs: 12 }}>
        <Box sx={{ p: 0, display: "flex", alignItems: displayText.length > 80 ? "flex-start" : "center" }}>
          <Box component="div" className="markdown-container" sx={{ flex: 1 }}>
            <Typography variant="body1" component="div" fontSize={16} sx={{ textAlign: "left", letterSpacing: "0.15px", lineHeight: "150%" }}>
              {summary.id && summary.id === 'system-loading' ? (
                <StreamingSummary
                  conversationId={conversationId}
                  informationMessageId={informationMessageId}
                  setDisplaySystemLoader={setDisplaySystemLoader}
                  setStreamingEnded={setStreamingEnded}
                  setSummaryStreamedText={setSummaryStreamedText}
                  onViewOnPlotClicked={onViewOnPlotClicked}
                  onViewOnSourceClicked={onViewOnSourceClicked}
                  messageId={messageId}
                  setDisplayText={(text) => setDisplayText(text || "")}
                  setFullMessageInfoFetched={setFullMessageInfoFetched}
                />
              ) : (
                renderMarkdownContent(displayText)
              )}
            </Typography>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default SummarySection;