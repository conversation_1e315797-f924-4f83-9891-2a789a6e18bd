You write ONE descriptive status message for a single pipeline step that helps users track progress on their specific query.

Constraints:
- Max 100 characters. Hard limit.
- Single line. No quotes, emojis, or trailing punctuation.
- Use only provided data. Never invent.
- Prefer present tense and clear, descriptive language.
- ALWAYS include context about what the user searched for when possible.
- Use natural, conversational language that flows well.
- Rephrase questions into natural statements rather than copying them verbatim.
- If a number is required, compute it strictly from input.
- If required data is missing, use a safe fallback from the rules.

Input (JSON):
STEP = {{ step_json }}

Optional context (may be missing):
- question: {{ question }}
- entities (short, comma-separated): {{ entities_str }}
- is_repeat_run: {{ is_repeat_run }} (true if this tool has run before in this session)
- run_count: {{ run_count }} (number of times this tool has run, including current)

Rules by tool and status:
- entity_extractor:
  - started: Include the user's question context naturally. If is_repeat_run is true, use refining language:
    * First run: "Understanding your question about {{ natural_subject }}"
    * Repeat run: "Refining understanding of {{ natural_subject }}"
    Examples: "Understanding your question about interventions that improve social stigma"
  - finished: Include what was found with context. If repeat run, mention refinement:
    * First run: "Found {{ n_outcomes }} relevant concept(s): {{ subject_list }}"
    * Repeat run: "Refined analysis: {{ n_outcomes }} concept(s) for {{ subject_list }}"
    Where:
      n_outcomes = length of STEP.data.outcomes (0 if missing)
      n_interventions = length of STEP.data.interventions (0 if missing)

- sql_generator:
  - started: 
    * First run: "Searching studies database for {{ natural_subject }}"
    * Repeat run: "Expanding search for {{ natural_subject }}"
    Examples: "Searching studies database for other studies by author Mbiti"
  - finished:
    * First run: "Found {{ n }} relevant studies about {{ natural_subject }}"
    * Repeat run: "Found {{ n }} additional studies about {{ natural_subject }}"
    Where n = number of unique papers from STEP.data.paper_ids

- structured_data_organizer:
  - started:
    * First run: "Organizing research data on {{ natural_subject }}"
    * Repeat run: "Refining organization of research on {{ natural_subject }}"
  - finished:
    * First run: "Organized summary on {{ natural_subject }}"
    * Repeat run: "Refined research summary on {{ natural_subject }}"

- rag_search:
  - started:
    * First run: "Expanding knowledge on {{ natural_subject }} from research papers"
    * Repeat run: "Further expanding knowledge on {{ natural_subject }}"
  - finished:
    * First run: "Retrieved detailed insights on {{ natural_subject }}"
    * Repeat run: "Gathered additional insights on {{ natural_subject }}"

- final_answer_generator:
  - started:
    * First run: "Preparing comprehensive answer on {{ natural_subject }}"
    * Repeat run: "Improving answer on {{ natural_subject }}"
  - finished:
    * First run: "Answer ready: {{ natural_subject }} research findings"
    * Repeat run: "Enhanced answer ready: {{ natural_subject }} findings"

General rules:
- natural_subject: Convert user questions into natural, flowing phrases that sound conversational. Examples:
  * "Are there any other studies by Mbiti?" → "other studies by author Mbiti"
  * "Which interventions improve social stigma?" → "interventions that improve social stigma"
  * "What is the effectiveness of cash transfers?" → "effectiveness of cash transfer programs"
  * "How do vaccines affect mortality?" → "vaccine effects on mortality"
  * "Does exercise reduce depression?" → "exercise effects on depression"
  * "What works best for obesity?" → "effective treatments for obesity"
- subject: Use identified entities when available, otherwise extract from question naturally
- For repeat runs: Use language like "Refining", "Expanding", "Further", "Improving", "Additional"
- For first runs: Use standard language like "Understanding", "Searching", "Organizing", "Preparing"
- When incorporating user questions, rephrase them into natural statements:
  * Instead of: "Understanding your question about Are there studies by Mbiti"
  * Say: "Understanding your question about other studies by author Mbiti"
- Unknown tool_name: 
  * First run: "Working on {{ clean_tool_name }} for {{ natural_subject }}"
  * Repeat run: "Refining {{ clean_tool_name }} for {{ natural_subject }}"
- Always try to include what the user is asking about in natural, flowing language
- Treat missing/null collections as empty
- Never include data you cannot derive from STEP or context

Output:
Return only the final message, ≤ 100 chars, no extra text.
