from unittest.mock import Mock, patch

import pytest

from src.agent.main import Agent


@pytest.fixture
def mock_config():
    """Create a mock configuration for Agent."""
    return {
        "model_name": "gemini-2.0-flash-001",
        "temperature": 0.1,
        "max_tokens": 8192,
        "max_iterations": 3,
        "verbose": True,
    }


@pytest.fixture
def agent_instance(mock_config):
    """Create an Agent instance with mocked dependencies."""
    with (
        patch("src.agent.main.ToolManager"),
        patch("src.agent.main.LLMClient"),
        patch("src.agent.main.PipelineManager"),
        patch("src.agent.main.AgentConfig") as mock_agent_config,
        patch("src.agent.main.AgentLogger"),
        patch("src.agent.main.StatsManager"),
        patch("aiohttp.ClientSession"),
    ):

        # Setup mock config
        mock_config_instance = Mock()
        mock_config_instance.project_id = "test-project"
        mock_config_instance.location = "us-central1"
        mock_config_instance.model_name = "gemini-2.0-flash-001"
        mock_config_instance.verbose = True
        mock_config_instance.max_iterations = 3
        mock_agent_config.from_dict.return_value = mock_config_instance

        return Agent(config=mock_config, conversation_id="test_conversation")


def test_agent_initialization(agent_instance):
    """Test that Agent initializes correctly."""
    assert agent_instance.conversation_id == "test_conversation"
    assert agent_instance.tool_manager is not None
    assert agent_instance.llm is not None


def test_agent_has_required_methods(agent_instance):
    """Test that Agent has required public methods."""
    assert hasattr(agent_instance, "execute")
    assert hasattr(agent_instance, "cleanup")
    assert callable(getattr(agent_instance, "execute"))
    assert callable(getattr(agent_instance, "cleanup"))
