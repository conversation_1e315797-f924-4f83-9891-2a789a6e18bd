import React from 'react';
import { SvgIcon, SvgIconProps } from '@mui/material';

interface ArrowCircleDownIconProps extends Omit<SvgIconProps, 'children'> {}

const ArrowCircleDownIcon: React.FC<ArrowCircleDownIconProps> = (props) => (
  <SvgIcon {...props} viewBox="0 0 33 32">
    {/* Arrow circle down icon; inherits currentColor from context */}
    <path
      d="M16.5 22.4L22.9 16L20.66 13.76L18.1 16.32V9.6H14.9V16.32L12.34 13.76L10.1 16L16.5 22.4ZM16.5 32C14.2867 32 12.2067 31.58 10.26 30.74C8.31333 29.9 6.62 28.76 5.18 27.32C3.74 25.88 2.6 24.1867 1.76 22.24C0.92 20.2933 0.5 18.2133 0.5 16C0.5 13.7867 0.92 11.7067 1.76 9.76C2.6 7.81333 3.74 6.12 5.18 4.68C6.62 3.24 8.31333 2.1 10.26 1.26C12.2067 0.42 14.2867 0 16.5 0C18.7133 0 20.7933 0.42 22.74 1.26C24.6867 2.1 26.38 3.24 27.82 4.68C29.26 6.12 30.4 7.81333 31.24 9.76C32.08 11.7067 32.5 13.7867 32.5 16C32.5 18.2133 32.08 20.2933 31.24 22.24C30.4 24.1867 29.26 25.88 27.82 27.32C26.38 28.76 24.6867 29.9 22.74 30.74C20.7933 31.58 18.7133 32 16.5 32ZM16.5 28.8C20.0733 28.8 23.1 27.56 25.58 25.08C28.06 22.6 29.3 19.5733 29.3 16C29.3 12.4267 28.06 9.4 25.58 6.92C23.1 4.44 20.0733 3.2 16.5 3.2C12.9267 3.2 9.9 4.44 7.42 6.92C4.94 9.4 3.7 12.4267 3.7 16C3.7 19.5733 4.94 22.6 7.42 25.08C9.9 27.56 12.9267 28.8 16.5 28.8Z"
      fill="currentColor"
    />
  </SvgIcon>
);

export default ArrowCircleDownIcon;
