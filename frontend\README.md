# Frontend Application

The frontend application is a React-based single-page application (SPA) that provides the main user interface for ImpactAI. It is built with TypeScript, Material-UI, and modern React practices.

## Architecture

### Core Components

1. **Authentication**
   - Login/Register forms
   - Password recovery
   - Protected routes
   - JWT-based auth management

2. **Layout**
   - Responsive design with mobile support
   - Sidebar navigation
   - Dynamic header
   - Theme customization

3. **Conversation Interface**
   - Real-time chat functionality
   - Message streaming
   - Interactive prompts
   - Research visualization
   - Source attribution

4. **Data Visualization**
   - Interactive graphs
   - Forest plots
   - Region-based plots
   - Sector analysis
   - Data export capabilities

### Key Features

1. **Chat Interface**
   - Real-time message updates
   - Streaming responses
   - Message history
   - Feedback system

2. **Research Tools**
   - Source references
   - Interactive data visualization
   - Citation management
   - Quality indicators

3. **User Experience**
   - Responsive design
   - Mobile optimization
   - Dark/light theme support
   - Error handling
   - Loading states

## Technical Stack

- **Framework**: React
- **Language**: TypeScript
- **UI Library**: Material-UI
- **State Management**: React Context
- **Routing**: React Router
- **Styling**: CSS-in-JS with Material-UI
- **Data Visualization**: Custom plotting components
- **Build Tool**: Vite

## Project Structure

```
frontend/
├── src/
│   ├── components/
│   │   ├── Auth/
│   │   ├── Common/
│   │   ├── Conversation/
│   │   ├── Graph/
│   │   ├── Layout/
│   │   └── Sidebar/
│   ├── services/
│   ├── hooks/
│   ├── theme/
│   ├── types/
│   └── utils/
├── public/
└── config files
```

## Features

### Authentication
- Secure login/signup
- Password recovery
- Session management
- Protected routes

### Chat Interface
- Real-time messaging
- Message history
- Source attribution
- Feedback system

### Visualization
- Interactive charts
- Data export
- Geographic visualization
- Research metrics

### Navigation
- Sidebar menu
- Mobile-responsive drawer
- Breadcrumb navigation
- History management
