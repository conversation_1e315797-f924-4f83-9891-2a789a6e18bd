"use client";
import { Box, Grid } from "@mui/material";
import NewsAndEventsFrame from "./NewsAndEventsFrame";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import DynamicPageTitle from '../components/DynamicPageTitle';

const Newsevents = () => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  return (
    <>
      <DynamicPageTitle
        title="ImpactAI News & Events"
        description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
      />
      <Box
        sx={{
          width: "100%",
          background: "none",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          padding: 0,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "30px",
            padding: 0,
            width: "100%",
            alignItems: "center",
          }}
        >
          <Grid container sx={{ mb: 0, p: 0 }}>
            <Grid item xs={12} sx={{ p: 0, my: isMobile ? "48px" : isTablet ? "90px" : "100px", }}>
              <NewsAndEventsFrame />
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default Newsevents;
