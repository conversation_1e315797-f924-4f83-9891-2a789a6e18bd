import os

def get_env() -> str:
    env = os.getenv("TARGET", "development")
    if env not in ["local", "development", "testing", "production"]:
        raise ValueError(f"Invalid environment: {env}")
    return env

def get_agent_url() -> str:
    env = get_env()
    if env == "local":
        return "http://localhost:8001"
    if env == "development":
        return "https://agent.impact-ai-dev.app"
    if env == "testing":
        return "https://agent.impact-ai-test.app"
    if env == "production":
        return "https://agent.impact-ai.app"
    raise ValueError(f"Invalid environment: {env}")
