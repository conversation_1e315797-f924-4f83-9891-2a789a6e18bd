import pytest, json
from unittest.mock import patch, mock_open
from services.files import FileLoader, FilesService


def test_files_dir():
    loader = FileLoader()
    # Test the private method through a public method
    with patch("os.path.dirname") as mock_dirname, patch(
        "os.path.abspath"
    ) as mock_abspath, patch("os.path.join") as mock_join:

        mock_dirname.side_effect = ["/backend/services", "/backend"]
        mock_abspath.return_value = "/backend/services/files.py"
        mock_join.return_value = "/backend/files/test.txt"

        # Access private method through a public method with mocked file
        with patch("builtins.open", mock_open(read_data="test content")):
            loader.load_text("test.txt")

        # Verify the path construction
        mock_join.assert_called_once_with("/backend", "files", "test.txt")


def test_load_text():
    loader = FileLoader()
    test_content = "Hello, World!"

    with patch("builtins.open", mock_open(read_data=test_content)) as mock_file:
        content = loader.load_text("test.txt")

        assert content == test_content
        mock_file.assert_called_once()


def test_load_json():
    loader = FileLoader()
    test_json = '{"key": "value"}'
    expected_dict = {"key": "value"}

    with patch("builtins.open", mock_open(read_data=test_json)) as mock_file:
        content = loader.load_json("test.json")

        assert content == expected_dict
        mock_file.assert_called_once()


def test_load_forgot_password_email_template():
    service = FilesService()
    test_content = "<html>Password Reset Template</html>"

    with patch.object(service, "load_text") as mock_load:
        mock_load.return_value = test_content
        content = service.load_forgot_passsword_email_template()

        assert content == test_content
        mock_load.assert_called_once_with(
            file_name="email-template-password-reset.html"
        )


def test_load_tags_request():
    service = FilesService()
    test_content = "SELECT * FROM tags;"

    with patch.object(service, "load_text") as mock_load:
        mock_load.return_value = test_content
        content = service.load_tags_request()

        assert content == test_content
        mock_load.assert_called_once_with(file_name="fetch-tags-request.sql")


def test_load_agent_responses():
    service = FilesService()
    test_content = {"responses": ["test"]}

    with patch.object(service, "load_json") as mock_load:
        mock_load.return_value = test_content
        content = service.load_agent_responses()

        assert content == test_content
        mock_load.assert_called_once_with(file_name="mock-agent-responses.json")


def test_load_text_file_not_found():
    loader = FileLoader()

    with pytest.raises(FileNotFoundError):
        with patch("builtins.open") as mock_file:
            mock_file.side_effect = FileNotFoundError()
            loader.load_text("nonexistent.txt")


def test_load_json_invalid_json():
    loader = FileLoader()
    invalid_json = "{invalid json"

    with pytest.raises(json.JSONDecodeError):
        with patch("builtins.open", mock_open(read_data=invalid_json)):
            loader.load_json("invalid.json")


def test_load_abstract_request():
    service = FilesService()
    test_content = "SELECT id, abstract FROM papers WHERE id IN (:paper_ids);"

    with patch.object(service, "load_text") as mock_load:
        mock_load.return_value = test_content
        content = service.load_abstract_request()

        assert content == test_content
        mock_load.assert_called_once_with(file_name="fetch-abstract-request.sql")


def test_load_abstract_request_file_not_found():
    service = FilesService()

    with pytest.raises(FileNotFoundError):
        with patch.object(service, "load_text") as mock_load:
            mock_load.side_effect = FileNotFoundError()
            service.load_abstract_request()


def test_load_abstract_request_content_validation():
    service = FilesService()
    test_content = "select id, abstract from papers where id in (:paper_ids);"

    with patch.object(service, "load_text") as mock_load:
        mock_load.return_value = test_content
        content = service.load_abstract_request()

        assert isinstance(content, str)
        assert "select" in content.lower()
        assert "papers" in content.lower()
        assert ":paper_ids" in content
