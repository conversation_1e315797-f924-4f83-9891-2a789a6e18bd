import { useRef, useEffect, useState } from "react";
import {
  useTheme,
  Slider,
  Select,
  MenuItem,
  ToggleButtonGroup,
  Chip,
  Stack,
  OutlinedInput,
  ListItemText,
  Checkbox,
} from "@mui/material";
import { FilterAlt, Clear } from "@mui/icons-material";
import * as d3 from "d3";

import "./StudiesRegionPlot.css";

const StudiesCountryPlot = ({ plotData }) => {
  const theme = useTheme();

  const containerRef = useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(0);
  const yearExtent = d3.extent(plotData.studies, (d) => d.pulication_year);
  const [publicationYear, setPublicationYear] = useState<
    number[] | undefined[] | string[]
  >(yearExtent);
  const [qualityScoreGroup, setQualityScoreGroup] = useState();

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setChartWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  // -----

  const studiesMap = d3.group(plotData.studies, (d) => +d.id);

  const studiesList = d3
    .groups(plotData.flat_effect_sizes, (d) => d.paper_id)
    .map((d) => {
      return {
        paper_id: +d[0],
        outcome_sectors: Array.from(
          new Set(d[1].map((d) => d.outcome_sectors))
        ),
        intervention_sectors: Array.from(
          new Set(d[1].map((d) => d.intervention_sectors))
        ),
        all_sectors: Array.from(
          new Set([
            ...Array.from(new Set(d[1].map((d) => d.outcome_sectors).flat())),
            ...Array.from(
              new Set(d[1].map((d) => d.intervention_sectors).flat())
            ),
          ])
        ),
        quality_score_category: d[1][0].quality_score_category,
        region: d[1][0].region,
        income_group:
          d[1][0].income_group !== null
            ? d[1][0].income_group
                ?.replace(/ economies/g, " ")
                .replace(/-/g, " ")
            : "N/A",
        publication_year: studiesMap.get(+d[0])[0].pulication_year,
        data: d[1],
      };
    });

  console.log("STUDIESLIST", studiesList);

  // ----- SECTORS -----

  const allSectors2 = Array.from(
    new Set([...studiesList.map((d) => d.all_sectors).flat()])
  ).sort((a, b) => a.localeCompare(b));

  const [sectorFilterEnabled, setSectorFilterEnabled] = useState(false);

  const handleSectorChipClick = () => {
    if (sectorFilterEnabled) {
      setSelectedSectors([]);
    }
    setSectorFilterEnabled(!sectorFilterEnabled);
  };

  const [selectedSectors, setSelectedSectors] = useState<string[]>([]);
  const handleSectorChange2 = (value: string | string[]) => {
    setSelectedSectors(typeof value === "string" ? value.split(",") : value);
  };

  // ----- INCOME GROUPS -----

  const incomeGroupOrder = [
    "High income",
    "Upper middle income",
    "Lower middle income",
    "Low income",
  ];

  const allIncomeGroups = Array.from(
    new Set([...studiesList.map((d) => d.income_group.trim())])
  ).sort((a, b) => incomeGroupOrder.indexOf(a) - incomeGroupOrder.indexOf(b));

  const [incomeGroupFilterEnabled, setIncomeGroupFilterEnabled] =
    useState(false);

  const handleIncomeGroupChipClick = () => {
    if (incomeGroupFilterEnabled) {
      setSelectedIncomeGroups([]);
    }
    setIncomeGroupFilterEnabled(!incomeGroupFilterEnabled);
  };

  const [selectedIncomeGroups, setSelectedIncomeGroups] = useState<string[]>(
    []
  );
  const handleIncomeGroupChange = (value: string | string[]) => {
    setSelectedIncomeGroups(
      typeof value === "string" ? value.split(",") : value
    );
  };

  // ----- PUBLICATION YEAR -----

  const [publicationYearFilterEnabled, setPublicationYearFilterEnabled] =
    useState(false);

  const [publicationYear2, setPublicationYear2] = useState<
    number[] | undefined[] | string[]
  >(yearExtent);
  const handlePublicationYearChange2 = (
    event: Event,
    newValue: number | number[]
  ) => {
    setPublicationYear2(newValue as number[]);
    setPublicationYearFilterEnabled(true);
  };
  const handlePublicationYearChipClick = () => {
    if (publicationYearFilterEnabled) {
      setPublicationYear2(yearExtent);
    }
    setPublicationYearFilterEnabled(!publicationYearFilterEnabled);
  };

  // ----- QUALITY SCORE -----
  const qualityScoreOrder = ["Low", "Average", "High"];

  const allQualityScoreGroups = Array.from(
    new Set(plotData.studies.map((d) => d.quality_score_group))
  ).sort((a, b) => qualityScoreOrder.indexOf(a) - qualityScoreOrder.indexOf(b));

  const [qualityScoreFilterEnabled, setQualityScoreFilterEnabled] =
    useState(false);
  const handleQualityScoreChipClick = () => {
    if (qualityScoreFilterEnabled) {
      setSelectedQualityScore([]);
    }
    setQualityScoreFilterEnabled(!qualityScoreFilterEnabled);
  };
  const [selectedQualityScore, setSelectedQualityScore] = useState<string[]>(
    []
  );
  const handleQualityScoreChange = (value: string) => {
    console.log("VALUE", value);
    if (selectedQualityScore.includes(value)) {
      setSelectedQualityScore(
        selectedQualityScore.filter((item) => item !== value)
      );
    } else {
      setSelectedQualityScore((prev) => [...prev, value]);
    }
  };

  // -----

  const filteredStudies2 = studiesList.filter(
    (d) =>
      (selectedSectors.length === 0
        ? true
        : d.all_sectors.some((s) => selectedSectors.includes(s))) &&
      (selectedIncomeGroups.length === 0
        ? true
        : selectedIncomeGroups.includes(d.income_group)) &&
      d.publication_year >= publicationYear2[0] &&
      d.publication_year <= publicationYear2[1] &&
      (selectedQualityScore.length === 0
        ? true
        : selectedQualityScore.includes(d.quality_score_category))
  );

  const groupedByStudy = d3
    .groups(filteredStudies2, (d) => d.region)
    .sort((a, b) => b[1].length - a[1].length);

  const xScale = d3
    .scaleLinear()
    .domain([
      0,
      d3.max(
        d3.groups(studiesList, (d) => d.region),
        (d) => d[1].length
      ),
    ])
  .range([0, 174])

  return (
    <div className="studies-plot container">
      <h4 style={{ marginTop: 0, fontWeight: "normal", textAlign: "center" }}>
        Studies by Region
      </h4>
      {groupedByStudy.map((obj, i) => (
        <div
          className="bar"
          style={{ height: 38, gridTemplateColumns: "180px 1fr" }}
        >
          <div className="key" style={{ textAlign: "end" }}>
            {obj[0] || "N/A"}
          </div>
          <div style={{ width: "100%", height: "100%" }} id={`barchart-bar-${i}`}>
            <div
              style={{ position: "relative", width: "100%", height: "100%" }}
            >
              <div
                style={{
                  position: "absolute",
                  background: `#478FFC`,
                  height: "100%",
                  borderRadius: 4,
                  width: xScale(obj[1].length),
                }}
              ></div>
              <div
                style={{
                  left: xScale(obj[1].length),
                }}
                className={
                  xScale(obj[1].length) > 20 ? "value-number-large" : "value-number"
                }
              >
                {obj[1].length}
              </div>
              {/* <div
                className="value"
                style={{
                  background: "rgba(232, 240, 252, 1)",
                  width: xScale(obj[1].length),
                }}
              ></div> */}
            </div>
          </div>
        </div>
      ))}
      <div>
        <div className="filter-chips">
          <div
            style={{
              fontSize: 15,
              display: "flex",
              alignItems: "center",
              paddingBottom: 10,
            }}
          >
            <FilterAlt style={{ fontSize: 20 }} /> Filter By:
          </div>
          <Stack direction="row" spacing={1} useFlexGap={true} flexWrap="wrap">
            <Chip
              onClick={handleSectorChipClick}
              label="Sector"
              disabled={false}
              variant="outlined"
              size="small"
              color="primary"
              icon={sectorFilterEnabled && <Clear />}
            />
            <Chip
              onClick={handleIncomeGroupChipClick}
              label="Income Group"
              disabled={false}
              variant="outlined"
              size="small"
              color="primary"
              icon={incomeGroupFilterEnabled && <Clear />}
            />
            <Chip
              onClick={handlePublicationYearChipClick}
              label="Publication Year"
              disabled={false}
              variant="outlined"
              size="small"
              color="primary"
              icon={publicationYearFilterEnabled && <Clear />}
            />
            <Chip
              onClick={handleQualityScoreChipClick}
              label="Quality Score"
              disabled={false}
              variant="outlined"
              size="small"
              color="primary"
              icon={qualityScoreFilterEnabled && <Clear />}
            />
          </Stack>
        </div>
        {sectorFilterEnabled && (
          <div style={{ marginTop: 10 }}>
            {allSectors2.length === 1 && (
              <div style={{ fontSize: 15 }}>Sector {allSectors2[0]}:</div>
            )}
            {allSectors2.length > 1 && (
              <div>
                <div style={{ fontSize: 15 }}>Sector:</div>
                <Select
                  size="small"
                  labelId="dropdown-label"
                  id="dropdown"
                  value={selectedSectors}
                  multiple
                  style={{ maxWidth: "100%", width: "100%" }}
                  onChange={(e) => {
                    handleSectorChange2(e.target.value);
                  }}
                  label="Select an Option"
                  input={
                    <OutlinedInput id="select-multiple-chip" label="Chip" />
                  }
                  renderValue={(selected) => selected.join(", ")}
                >
                  {allSectors2.map((s) => (
                    <MenuItem key={s} value={s}>
                      <Checkbox checked={selectedSectors.includes(s)} />
                      <ListItemText primary={s} />
                    </MenuItem>
                  ))}
                </Select>
              </div>
            )}
          </div>
        )}
        {incomeGroupFilterEnabled && (
          <div style={{ marginTop: 10 }}>
            {allIncomeGroups.length === 1 && (
              <div style={{ fontSize: 15 }}>
                Income Group {allIncomeGroups[0]}:
              </div>
            )}
            {allIncomeGroups.length > 1 && (
              <div>
                <div style={{ fontSize: 15 }}>Income Group:</div>
                <Select
                  size="small"
                  labelId="dropdown-label"
                  id="dropdown"
                  value={selectedIncomeGroups}
                  multiple
                  style={{ maxWidth: "100%", width: "100%" }}
                  onChange={(e) => {
                    // setSector(e.target.value);
                    handleIncomeGroupChange(e.target.value);
                  }}
                  label="Select an Option"
                  input={
                    <OutlinedInput id="select-multiple-chip" label="Chip" />
                  }
                  renderValue={(selected) => selected.join(", ")}
                >
                  {allIncomeGroups.map((s) => (
                    <MenuItem key={s} value={s}>
                      <Checkbox checked={selectedIncomeGroups.includes(s)} />
                      <ListItemText primary={s} />
                    </MenuItem>
                  ))}
                </Select>
              </div>
            )}
          </div>
        )}
        {publicationYearFilterEnabled && (
          <div style={{ marginTop: 10 }}>
            {yearExtent[0] === yearExtent[1] && (
              <div style={{ fontSize: 15 }}>
                Publication Year {yearExtent[0]}:
              </div>
            )}
            {yearExtent[0] !== yearExtent[1] && (
              <div>
                <div style={{ fontSize: 15 }}>Publication Year:</div>
                <Slider
                  aria-label="PublicationYear"
                  value={publicationYear2}
                  onChange={handlePublicationYearChange2}
                  min={yearExtent[0]}
                  max={yearExtent[1]}
                  marks={true}
                  step={1}
                  size="small"
                />
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <div style={{ fontSize: "12px" }}>{publicationYear2[0]}</div>
                  <div style={{ fontSize: "12px" }}>{publicationYear2[1]}</div>
                </div>
              </div>
            )}
          </div>
        )}
        {qualityScoreFilterEnabled && (
          <div style={{ marginTop: 10 }}>
            {allQualityScoreGroups.length === 1 && (
              <div style={{ fontSize: 15 }}>
                Quality Score {allQualityScoreGroups[0]}:
              </div>
            )}
            {allQualityScoreGroups.length > 1 && (
              <div>
                <div style={{ fontSize: 15 }}>Quality Score:</div>
                <ToggleButtonGroup size="small" value={qualityScoreGroup}>
                  <Stack
                    direction="row"
                    spacing={1}
                    useFlexGap={true}
                    flexWrap="wrap"
                  >
                    {allQualityScoreGroups.map((q) => (
                      <Chip
                        onClick={() => handleQualityScoreChange(q)}
                        size="small"
                        label={q}
                        disabled={false}
                        style={{
                          backgroundColor: selectedQualityScore.includes(q)
                            ? "#D4E4FC"
                            : null,
                        }}
                      />
                    ))}
                  </Stack>
                </ToggleButtonGroup>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default StudiesCountryPlot;
