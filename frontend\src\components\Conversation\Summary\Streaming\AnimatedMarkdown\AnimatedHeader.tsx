import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import AccessibleHeader from './AccessibleHeader';

const AnimatedHeader = forwardRef(({
  as,
  onComplete,
  children,
  currentHeaderLevelRef,
  ...props
}: any, ref) => {

  const [displayText, setDisplayText] = useState("");
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);

  const textToAnimate = React.Children.toArray(children)
    .map(child => typeof child === 'string' ? child : '')
    .join('');

  useEffect(() => {
    if (textToAnimate && !isAnimationComplete) {
      let i = 0;
      const intervalId = setInterval(() => {
        setDisplayText(textToAnimate.substring(0, i + 1));
        i++;
        if (i >= textToAnimate.length) {
          clearInterval(intervalId);
          setIsAnimationComplete(true);
          onComplete();
        }
      }, 15);
      return () => clearInterval(intervalId);
    } else {
      setDisplayText(textToAnimate);
      if (textToAnimate && !isAnimationComplete) {
        setIsAnimationComplete(true);
        onComplete();
      }
    }
  }, [onComplete, isAnimationComplete, textToAnimate]);

  useImperativeHandle(ref, () => ({
    getIsAnimationComplete: () => isAnimationComplete
  }));

  return (
    <AccessibleHeader
      as={as}
      currentHeaderLevelRef={currentHeaderLevelRef}
      {...props}
    >
      {displayText}
    </AccessibleHeader>
  );
});

export default AnimatedHeader;