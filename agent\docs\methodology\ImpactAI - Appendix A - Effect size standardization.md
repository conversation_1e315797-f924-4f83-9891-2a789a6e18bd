**Appendix A: Effect size standardization**

This appendix summarizes our approach to harmonizing effect sizes collected from varied impact evaluation studies. Due to variations in methodologies, metrics, measurements, and taxonomies, harmonizing effect sizes across systematic reviews and individual studies is challenging. This research aims to inform foundational elements of how our database is constructed.

# **Introduction**

Following the World Bank’s [IDEAL](https://www.worldbank.org/en/research/brief/ideal) schema, ImpactAI prioritizes extracting raw outcome metrics such as intention-to-treat (ITT) and local average treatment effect (LATE). We only extract effect sizes for estimates from the full sample. We use a decision tree that depends on the standardization of the effect size to determine whether to use the author's effect size or compute it ourselves.

Among the various methods for calculating the standardized mean difference, we use Hedges’ g in our product as it is among the most used, particularly in small-sample studies, as it incorporates a correction factor to adjust for bias that can arise in them.

1) **When the effect size in the paper is standardized**
   1) If the effect size comes from a cluster randomized control trial (RCT) and the author adjusted for the cluster size, or if it doesn’t come from a cluster RCT, we use the author’s effect size and convert it into Hedges’ g.
   2) If the effect size comes from a cluster RCT and the author didn’t adjust for the cluster size, we adjust for it and then convert it into Hedges’ g.

2) **When the effect size in the paper is not standardized**

Depending on whether the effect size is a standardized mean difference or not, we standardize or compute it and convert it to Hedges’ g.

The following sections will describe the steps we use to compute and combine the effect sizes into Hedge’s g.

When we convert between different measures we make certain assumptions about the nature of the underlying traits or effects. Even if these assumptions do not hold exactly, the decision to use these conversions is often better than the alternative, which is to simply omit the studies that happened to use an alternate metric. This would involve loss of information, and possibly the systematic loss of information, resulting in a biased sample of studies. A sensitivity analysis to compare the meta-analysis results with and without the converted studies would be important.

# **Standardized mean difference**

## **Cohen’s d**

Cohen’s standardized mean difference (d) is computed as the mean difference between the treatment and control group divided by the pooled standard deviation of the two groups.

`d = (X1 - X2) / Sd_pooled`

The pooled standard deviation can be derived as follows:

*`Sd_pooled = sqrt( ((n1 - 1)*s1^2 + (n2 - 1)*s2^2) / (n1 + n2 - 2) )`*

***X1** and **X2** are the means of the two groups (can be ITT, LATE)*
***n1** and **n2** are sample sizes from both groups*
***s1** and **s2** are the standard deviations of both groups.*

In cases where the standard deviation of both groups is not reported we can follow (Borenstein, 2009\)*:*

`S_within = SE * sqrt((n1 * n2) / (n1 + n2))`

*Where **SE** is the regression coefficient standard error.*

However, this is just an approximation and should be considered a last option. Alternatively, we could contact authors for the actual figures or use the replication packages to perform the calculations ourselves.

If the standard error is not reported, we can use z-statistic and follow [Higgins et al., Chapter 7, 7.7.7.2](https://handbook-5-1.cochrane.org/chapter_7/7_7_7_2_obtaining_standard_errors_from_confidence_intervals_and.htm)):

`SE = intervention_effect_estimate / z-score`

If the authors only reported confidence intervals and no standard error, we calculated the standard error from the confidence intervals.

`SE = (upper_limit - lower_limit) / alpha`

Where **alpha** is the critical value of the confidence interval.

If the study does not report the standard error but reports t, we extract and use this as reported by the authors. In cases where only significance levels are reported, then t was imputed as follows:

* Prob \> 0.1: t \= 0.5
* 0.1 ≥ Prob \> 0.05: t \= 1.8
* 0.05 ≥ Prob \> 0.01: t \= 2.4
* 0.01 ≥ Prob: t \= 2.8

* **Assumptions**

We assume independence of observations.
We assume that each arm in the study follows a normal distribution.
The variance of the underlying measurements is similar across the groups being compared.
Our preferred estimand is the Intention To Treat (ITT), but in the Local Average Treatment Effect (LATE) case, we assume the compliance rate is close to 100%.

* **Limitation**

Cohen’s Standardized Mean Differences (SMD) has a bias in small samples (N\<30) and overestimates the true value of the estimator. Hedges’s SMD considers the sample size and is thus more frequently used.

Cohen’s d is sensitive to outliers.

## **Hedge’s g**

Hedge’s standardized mean difference can be approximated with Cohen’s d and the degree of freedom.
`g ~= d * (1 - (3 / (4*df - 1)))`

***d** is Cohen’s standardized mean difference.*
***df** is the degree of freedom which is n1 \+ n2 – 2 for two independent groups.*

* **Assumptions**

We assume independence of observations.
We assume that each arm in the study follows a normal distribution.
We assume that the variance is similar across the groups being compared.

* **Limitation**

Hedges ‘g is sensitive to outliers.

When sample sizes are highly unbalanced between groups, the pooled standard deviation used in calculating g may not accurately reflect the true variability, leading to biased effect sizes.

# **Adjustment by Intraclass correlation (ICC)**

In the case where the effect size comes from a cluster RCT and the author didn’t adjust for the size of the cluster, we use the following adjustment:

`SE(d)_adjusted = SE(d) * sqrt(1 + (m - 1) * c)`

Where ***m*** is the average cluster size.

If cluster size is not reported, we calculate it by dividing the total number of participants (or those analyzed) by the number of clusters.

If the ICC is not reported, we default to an ICC of 0.05, which aligns with estimated ICC values derived from published data for similar outcomes.

# **Risk ratio (RR)**

The risk ratio measures the risk of an event in the treatment group compared to the risk of the same event in the control group.

* **Conversion**

The risk ratio is computed as the following:

`RR = CIt / CIc`
***CIt** and **CIc** denote the cumulative incidence in both groups.*

*We convert the risk ratio into Cohen’s SMD as follows:*

*`d ~= ln(RR) * (sqrt(3) / pi) ~= ln(RR) * 0.551`*

*From Cohen’s SMD (d), we then compute Hedge’s SMD (g).*

* **Assumptions**

We assume independence of observations.
The binary outcome is assumed to follow a logistic distribution that has a fixed variance.
The relationship between the risk ratio (RR) and SMD assumes a uniform effect size.
The approach presumes a log-linear relationship between the binary outcome (captured by RR) and the continuous effect (captured by SMD).

* **Limitation**

For rare events, the risk ratio may exaggerate the apparent magnitude of an effect.
The risk ratio doesn’t account for time, so it assumes the follow-up duration is consistent across groups.

# **Risk difference**

It is the difference between the risk of an outcome in the treatment group and the control group.

* **Conversion**

If cumulative incidence (risk) in both groups is available, we compute the risk ratio and transform the risk ratio into Cohen’s d.

Otherwise, we convert from risk difference to odds ratio and follow [(Higgins et al. Chapter 6,  6,4,1)](https://training.cochrane.org/handbook/current/chapter-06#:~:text=The%20interpretation%20of%20odds%20is,in%20risk%20\(risk%20difference\).)

`odds = risk / (1 - risk)`

Then, we convert the odds ratio to Cohen’s d.

* **Assumptions**

We assume independence of observations.

* **Limitations**

Risk difference estimates can become unstable in small samples or when event rates are extremely low or high.
It provides no information about the relative magnitude of differences concerning baseline risks

# **Odds ratio**

The odds ratio is the ratio of the odds of an event occurring in one group to the odds in another.

* **Conversion**

*`d ~= ln(OR) * (sqrt(3) / pi) ~= ln(OR) * 0.551`*

*From Cohen’s SMD (d), we then compute Hedge’s SMD (g).*

* **Assumptions**

We assume independence of observations.
The data for the two groups compared by the odds ratio are assumed to follow a logistic or standard normal distribution.

One can place the treated group or the control group in the numerator, it just has to be consistent across the database .If the treatment group is in the denominator, ln(OR) would change signs and the OR would change to its inverse.

* **Limitation**

When the outcome of interest is common (e.g., prevalence \>10%), the OR tends to overstate the strength of the association compared to the relative risk.

# **Hazard ratio**

A hazard ratio (HR) is a relative measure that compares the rate at which an event occurs in one group to the rate at which it occurs in another group over time.

* **Conversion**

For the conversion of the hazard ratio, there are two possibilities:

* The hazard ratio is interpretable as an odds or a probability under the assumption of proportional hazards ([David M. Thompson](https://arxiv.org/search/stat?searchtype=author&query=Thompson,+D+M), [Julia E. Reid](https://arxiv.org/search/stat?searchtype=author&query=Reid,+J+E), 2021\)
  * With this assumption, we would then convert the hazard ratio to SMD using the odds ratio formula.

*`d ~= ln(OR) * (sqrt(3) / pi) ~= ln(OR) * 0.551`*

* From [(Tyler J. VanderWeele, 2019\)](https://academic.oup.com/biometrics/article/76/3/746/7429123?login=false), we can convert the hazard ratio to a risk ratio. We would then convert the risk ratio into Cohen’s d.

`RR = (1 - 0.5 * sqrt(HR) / (1 - 0.5 * sqrt(1/HR))`
Then,

*`d ~= ln(RR) * (sqrt(3) / pi) ~= ln(RR) * 0.551`*

* **Assumptions**

We assume independence of observations.
We assume proportional hazards across the entire study duration.

* **Limitation**

Requires Time-to-Event Data: the hazard ratio does not explicitly describe absolute risks or when events occur, only the relative risk over time.

# **Proportions**

* **Conversion**

There are two options to convert proportions, the first one is to convert the proportion to an Odds Ratio and then to Cohen’s d.

`OR = (pt / (1 - pt)) / (pc / (1 - pc))`

`d ~= ln(OR) * (sqrt(3) / pi) ~= ln(OR) * 0.551`

For outcomes reported based on the proportion of days, events, we can also use the following:

`d = (pt - pc) / sqrt( ((pt * (1 - pt)) / nt) + ((pc * (1 - pc)) / nc) )`

*Where **pt** and **pc** are the proportions of the treatment and control group.*
*And **nc** and **nt** are the sample sizes in the control and treatment groups.*

* **Assumptions**

The conversion from proportions to SMD assumes an underlying normal distribution of a continuous latent variable.
The binary variable must have a clear dichotomy.
We assume that the variance is similar across the groups being compared.

* **Limitation**

Small sample sizes may lead to unstable or biased estimates.

# **References**

Angrist, J. D., & Pischke, J.-S. (2009). *Mostly harmless econometrics : an empiricist's companion*. Princeton University Press.
Borenstein, M. (2009). *Introduction to Meta-Analysis*. Wiley. 10.1002/9780470743386
Boateng, G. O., Neilands, T. B., Frongillo, E. A., Melgar-Quiñonez, H. R., & Young, S. L. (2018). Best practices for developing and validating scales for health, social, and behavioral research: A primer. Frontiers in Public Health, 6, 149\. [https://doi.org/10.3389/fpubh.2018.00149](https://doi.org/10.3389/fpubh.2018.00149)
Brody, T. (2012, . .). *Hazard Ratio*. Science direct. [https://www.sciencedirect.com/topics/medicine-and-dentistry/hazard-ratio](https://www.sciencedirect.com/topics/medicine-and-dentistry/hazard-ratio)
Chinn, S. (2000, Nov 30). A simple method for converting an odds ratio to effect size for use in meta-analysis. *Statistics in medecine*, *19*(22), 3127-3131. 10.1002/1097-0258(20001130)19:22\<3127::aid-sim784\>3.0.co;2-m
Christensen, G., Freese, J., & Miguel, E. (2019). *Transparent and Reproducible Social Science Research: How to Do Open Science*. University of California Press. [https://www.ucpress.edu/books/transparent-and-reproducible-social-science-research/paper](https://www.ucpress.edu/books/transparent-and-reproducible-social-science-research/paper)
Cohen, J. (2013). *Statistical Power Analysis for the Behavioral Sciences*. Taylor & Francis Group. 10.4324/9780203771587
Deaton, A., & Cartwright, N. (2018, August). Understanding and misunderstanding randomized controlled trials. *Social Science & Medicine*, *210*(.), 2-21. 10.1016/j.socscimed.2017.12.005
DerSimonian, R., & Laird, N. (1986). Meta-analysis in clinical trials. In *Controlled Clinical Trials* (Vol. 7, pp. 177-188). Elsevier. 10.1016/0197-2456(86)90046-2
Hae-Young, K. (2017). Statistical notes for clinical researchers: Risk difference, risk ratio, and odds ratio. *Restor Dent Endod.*, *42*(1), 72-76. 10.5395/rde.2017.42.1.72
Higgins, J., Thomas, J., Chandler, J., Cumpston, M., Li, T., Page, M., & Welch, V. (n.d.). *Cochrane Handbook for Systematic Reviews of Interventions*. Cochrane Training. Retrieved November 28, 2024, from [https://training.cochrane.org/handbook](https://training.cochrane.org/handbook)
Higgins, J. P., Thompson, S. G., Deeks, J. J., & Altman, D. G. (2003). Measuring inconsistency in meta-analyses. *BMJ*, *327*(7414), 557-60. 10.1136/bmj.327.7414.557
Jain, S., Sharma, S. K., & Jain, K. (2019). Meta-analysis of fixed, random and mixed effects models. International Journal of Mathematical, Engineering and Management Sciences, 4(1), 199–218.
Jing, Y., Murad, M. H., & Lin, L. (2023). A Bayesian model for combining standardized mean differences and odds ratios in the same meta-analysis. *Journal of Biopharmaceutical Statistics*, *33*(2), 167-190. 10.1080/10543406.2022.2105345
Loannidis, J. P., Patsopoulos, N. A., & Rothstein, H. R. (2008, June 21). Reasons or excuses for avoiding meta-analysis in forest plots. *BMJ*, *336*(7658), 1413-1415. 10.1136/bmj.a117
Nuño, M. M., & Gillen, D. L. (2017). Alternative Sampling Designs for Time-to-Event Data With Applications to Biomarker Discovery in Alzheimer's Disease. In *Handbook of Statistics* (Vol. 36, pp. 105-166). Elsevier. 10.1016/bs.host.2017.08.001
Riley, R. D., Higgins, J. P., & Deeks, J. J. (2011). Interpretation of random effects meta-analyses. *BMJ*, *342*(549), \-. [https://doi.org/10.1136/bmj.d549](https://doi.org/10.1136/bmj.d549)
Schwarzer, G., Carpenter, J. R., & Rücker, G. (2015). *Meta-Analysis with R*. Springer International Publishing. 10.1007/978-3-319-21416-0
Spruance, S. L., Reid, J. E., Grace, M., & Samore, M. (2004). Hazard Ratio in Clinical Trials. *Antimicrob Agents Chemother*, *48*(8), 2787-92. 10.1128/AAC.48.8.2787-2792.2004
Tufanaru, C., Munn, Z., Stephenson, M., & Aromataris, E. (2015). Fixed or random effects meta-analysis? Common methodological issues in systematic reviews of effectiveness. *International Journal of Evidence-Based Healthcare*, *13*(3), 196-207. 10.1097/XEB.0000000000000065
