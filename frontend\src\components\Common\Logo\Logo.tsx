import { Box, SxProps, Theme } from '@mui/material';
import React from 'react';
import whiteLogo from './logo_white.svg';
import blueLogo from './logo_blue.svg';
import iconNoText from './logo_icon.svg';

interface LogoProps {
  onClick?: () => void;
  isCollapsed?: boolean;
  logoSrc?: string;
  altText?: string;
  color?: 'white' | 'blue';
  width?: string | number;
  height?: string | number;
  sx?: SxProps<Theme>;
}

const Logo: React.FC<LogoProps> = ({
  onClick,
  isCollapsed = false,
  logoSrc,
  altText = "WorldBank ImpactAI",
  color = 'blue',
  width: propWidth,
  height: propHeight,
  sx,
}) => {
  const currentLogoSrc = isCollapsed ? iconNoText : (logoSrc || (color === 'blue' ? blueLogo : whiteLogo));
  const calculatedWidth = isCollapsed ? 38 : (propWidth || 150);
  const calculatedHeight = isCollapsed ? 38 : (propHeight || 38);

  return (
    <Box
      id='impactai-logo'
      sx={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        cursor: 'pointer',
        px: isCollapsed ? 0 : 2,
        ...sx,
      }}
      onClick={onClick}
    >
      <img
        src={currentLogoSrc}
        alt={altText}
        loading="lazy"
        style={{
          width: `${calculatedWidth}px`,
          height: `${calculatedHeight}px`,
          marginRight: '0px',
        }}
      />
    </Box>
  );
}

export default Logo;