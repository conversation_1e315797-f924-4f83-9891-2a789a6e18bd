import React, { RefObject } from "react";
import { Box, Grid } from "@mui/material";
import { Message, FilterState } from "../../../types/ConversationTypes";
import { RightSidePanel } from './RightSidePanel';
import { MessageContent } from './MessageContent';
import PromptSection from "../../PromptControl/PromptSection";
import useScrollbarWidth from "../../../hooks/useScrollbarWidth";
import { PROMPT_LABEL } from "../../../utils/labels";

interface TwoColumnLayoutProps {
    openRightSection: "sources" | "charts" | null;
    isSidebarCollapsed: boolean;
    isMobileOrTablet: boolean;
    initialConversationMessages: Message[];
    handleUserPrompt: (e: any) => void;
    displaySystemLoader: boolean;
    loadingMessages: boolean;
    conversationWrapperScrollRef: RefObject<HTMLDivElement>;
    leftColumnScrollRef: RefObject<HTMLDivElement>;
    lastSystemMessageId: string | null;
    hoveredMessageId: string | null;
    setHoveredMessageId: (id: string | null) => void;
    informationMessageId: string | null;
    streamingEnded: boolean;
    summaryStreamedText: string;
    activeInfoMessageIdForPanel: string | null;
    isTwoColumnLayoutWithPanelOpen: boolean;
    localConversationId: string;
    handleViewOnPlotClickedInternal: (payload: any) => void;
    handleViewOnSourceClickedInternal: (payload: any) => void;
    handleOpenSources: (id: string, fromSourcesButton: boolean) => void;
    handleOpenCharts: (id: string, payload: any) => void;
    setDisplaySystemLoader: (flag: boolean) => void;
    setStreamingEnded: (flag: boolean) => void;
    setSummaryStreamedText: (text: string) => void;
    setFullMessageInfoFetched: (msg: Message | null) => void;
    activeQuestionId: string | null;
    lastMessageRef: RefObject<HTMLDivElement>;
    buttonStyle: any;
    theme: any;
    handleCloseRightSection: () => void;
    panelSources: any[];
    selectedStudy: string;
    activeSourcePaperIds: string[];
    activeSourceMessageId: string | null;
    setActiveSourcePaperIds: (ids: string[]) => void;
    setActiveSourceMessageId: (id: string | null) => void;
    handleFiltersChange: (messageId: string, filters: FilterState, type: 'sources' | 'plot') => void;
    handleResetFilters: () => void;
    activeFiltersCount: number;
    activePlotDetails: any;
    setSelectedStudy: (id: string) => void;
}

export const TwoColumnLayout: React.FC<TwoColumnLayoutProps> = ({
    openRightSection,
    isSidebarCollapsed,
    isMobileOrTablet,
    initialConversationMessages,
    handleUserPrompt,
    displaySystemLoader,
    loadingMessages,
    conversationWrapperScrollRef,
    leftColumnScrollRef,
    lastSystemMessageId,
    hoveredMessageId,
    setHoveredMessageId,
    informationMessageId,
    streamingEnded,
    summaryStreamedText,
    activeInfoMessageIdForPanel,
    isTwoColumnLayoutWithPanelOpen,
    localConversationId,
    handleViewOnPlotClickedInternal,
    handleViewOnSourceClickedInternal,
    handleOpenSources,
    handleOpenCharts,
    setDisplaySystemLoader,
    setStreamingEnded,
    setSummaryStreamedText,
    setFullMessageInfoFetched,
    activeQuestionId,
    lastMessageRef,
    buttonStyle,
    theme,
    handleCloseRightSection,
    panelSources,
    selectedStudy,
    activeSourcePaperIds,
    activeSourceMessageId,
    setActiveSourcePaperIds,
    setActiveSourceMessageId,
    handleFiltersChange,
    handleResetFilters,
    activeFiltersCount,
    activePlotDetails,
    setSelectedStudy,
}) => {
    const scrollbarWidth = useScrollbarWidth();
    const rightPanelOpenOffset = '40px';
    const gapBetweenColumns = '2vw';

    let leftColumnCalculatedWidth;
    let stickyPanelCalculatedWidth;
    if (openRightSection) {
        if (openRightSection === 'charts') {
            if (isSidebarCollapsed) {
                leftColumnCalculatedWidth = `45vw`;
                stickyPanelCalculatedWidth = `45vw`;
            } else {
                leftColumnCalculatedWidth = `45vw`;
                stickyPanelCalculatedWidth = `35vw`;
            }
        } else if (openRightSection === 'sources') {
            if (isSidebarCollapsed) {
                leftColumnCalculatedWidth = `45vw`;
                stickyPanelCalculatedWidth = `45vw`;
            } else {
                leftColumnCalculatedWidth = `45vw`;
                stickyPanelCalculatedWidth = `35vw`;
            }
        } else {
            leftColumnCalculatedWidth = '45vw';
            stickyPanelCalculatedWidth = '35vw';
        }
    } else {
        leftColumnCalculatedWidth = '45vw';
        stickyPanelCalculatedWidth = '35vw';
    }

    return (
        <Box
            sx={{
                display: 'flex',
                width: '100%',
                height: 'calc(100vh - 104px)',
                overflow: 'hidden',
                position: 'relative',
                justifyContent: 'center',
                alignItems: 'flex-start',
                maxWidth: '100%',
            }}
        >
            <Box
                ref={conversationWrapperScrollRef}
                className='conversation-wrapper'
                sx={{
                    display: 'flex',
                    width: '100%',
                    maxWidth: '100%',
                    height: `calc(100% - 104px)`,
                    overflowY: openRightSection ? 'hidden' : 'auto',
                    paddingRight: openRightSection ? 0 : (scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0),
                    px: "0px",
                    pt: "0px",
                    pb: "0px",
                    overflowX: 'hidden',
                    transition: 'width 0.3s ease-in-out, max-width 0.3s ease-in-out, flex 0.3s ease-in-out',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    flexShrink: 0,
                    scrollbarGutter: 'stable',
                }}
            >
                <Box
                    ref={leftColumnScrollRef}
                    id='left-column'
                    flexGrow={0}
                    flexShrink={0}
                    width={leftColumnCalculatedWidth}
                    minWidth={'350px'}
                    className='conversation-wrapper-inner'
                    sx={{
                        transition: 'width 0.3s ease-in-out, margin 0.3s ease-in-out',
                        height: '100%',
                        overflowY: openRightSection ? 'auto' : 'visible',
                        paddingRight: openRightSection && scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0,
                        px: (isMobileOrTablet
                            ? "0px"
                            : {
                                xs: "0px", sm: "0px", md: "40px", lg: "40px", xl: "40px", xxl: "40px", uhd: "40px", '4k': "40px"
                            }),
                        ml: openRightSection ? 0 : 'auto',
                        mr: 'auto',
                        scrollbarGutter: 'stable',
                    }}
                >
                    <Grid
                        container
                        direction="column"
                        className='messages-wrapper'
                    >
                        <Box id="chat-history" px={3}>
                            {initialConversationMessages.map((msg, index) => (
                                <Grid key={msg.id}
                                    size={{ xs: 12 }}
                                    sx={{
                                        mb: index === initialConversationMessages.length - 1 ? 4 : 1
                                    }}
                                >
                                    <MessageContent
                                        msg={msg}
                                        index={index}
                                        lastSystemMessageId={lastSystemMessageId}
                                        hoveredMessageId={hoveredMessageId}
                                        setHoveredMessageId={setHoveredMessageId}
                                        informationMessageId={informationMessageId}
                                        displaySystemLoader={displaySystemLoader}
                                        streamingEnded={streamingEnded}
                                        summaryStreamedText={summaryStreamedText}
                                        openRightSection={openRightSection}
                                        activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                                        isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                                        localConversationId={localConversationId}
                                        handleViewOnPlotClickedInternal={handleViewOnPlotClickedInternal}
                                        handleViewOnSourceClickedInternal={handleViewOnSourceClickedInternal}
                                        handleOpenSources={handleOpenSources}
                                        handleOpenCharts={handleOpenCharts}
                                        setDisplaySystemLoader={setDisplaySystemLoader}
                                        setStreamingEnded={setStreamingEnded}
                                        setSummaryStreamedText={setSummaryStreamedText}
                                        setFullMessageInfoFetched={setFullMessageInfoFetched}
                                        activeQuestionId={activeQuestionId}
                                        lastMessageRef={lastMessageRef}
                                        buttonStyle={buttonStyle}
                                        theme={theme}
                                        initialConversationMessages={initialConversationMessages}
                                    />
                                </Grid>
                            ))}
                        </Box>
                        {!window.location.href.includes('impact-ai-prod.app') && (
                            <Grid
                                size={{ xs: 12 }}
                                className="prompt-wrapper">
                                <PromptSection
                                    centeredContent={false}
                                    handleChange={handleUserPrompt}
                                    queryLabelText={PROMPT_LABEL}
                                    isLoading={displaySystemLoader || loadingMessages}
                                    dataTags={{}}
                                    selectedTag={''}
                                    onCloseDropdown={() => { }}
                                    chipClickCount={() => { }}
                                />
                            </Grid>
                        )}
                    </Grid>
                </Box>
            </Box>
            <Box
                className="sticky-panel"
                sx={{
                    width: stickyPanelCalculatedWidth,
                    maxWidth: stickyPanelCalculatedWidth,
                    minWidth: openRightSection ? { xs: '0px', md: '250px' } : '0px',
                    transition: 'width 0.3s ease-in-out, opacity 0.3s ease-in-out, max-width 0.3s ease-in-out, min-width 0.3s ease-in-out, visibility 0.3s ease-in-out, right 0.3s ease-in-out',
                    position: 'fixed',
                    top: '82px',
                    right: openRightSection
                        ? rightPanelOpenOffset
                        : `calc(-${stickyPanelCalculatedWidth} - ${gapBetweenColumns})`,
                    height: 'calc(100vh - 120px)',
                    overflowY: 'auto',
                    visibility: openRightSection ? 'visible' : 'hidden',
                    opacity: openRightSection ? 1 : 0,
                    flexShrink: 0,
                    flexGrow: 0,
                    zIndex: 100,
                    scrollbarGutter: 'stable',
                }}
            >
                {openRightSection && (
                    <RightSidePanel
                        openRightSection={openRightSection}
                        activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                        handleCloseRightSection={handleCloseRightSection}
                        panelSources={panelSources}
                        selectedStudy={selectedStudy}
                        activeSourcePaperIds={activeSourcePaperIds}
                        activeSourceMessageId={activeSourceMessageId}
                        setActiveSourcePaperIds={setActiveSourcePaperIds}
                        setActiveSourceMessageId={setActiveSourceMessageId}
                        handleFiltersChange={handleFiltersChange}
                        handleResetFilters={handleResetFilters}
                        activeFiltersCount={activeFiltersCount}
                        activePlotDetails={activePlotDetails}
                        onSelectStudy={setSelectedStudy}
                        initialConversationMessages={initialConversationMessages}
                    />
                )}
            </Box>
        </Box>
    );
};