import re
import math
import numpy as np
from dataclasses_json import dataclass_json
from dataclasses import dataclass, field

from typing import Optional


@dataclass_json
@dataclass
class StudyDataPoint:
    paper_id: int
    paper_title: str
    paper_citation: str
    country: str
    author_name: str
    year: int

    intervention_tag_id: int
    intervention_tag_label: str

    intervention_group_id: int
    intervention_group_label: str

    intervention_sector_id: int
    intervention_sector_label: str

    outcome_tag_id: int
    outcome_tag_label: str

    outcome_group_id: int
    outcome_group_label: str

    outcome_sector_id: int
    outcome_sector_label: str

    direction: str
    treatment_effect: str
    precision_value: str

    paper_doi_url: Optional[str] = field(default="")

    intervention_tag_definition: Optional[str] = field(default="")
    intervention_group_definition: Optional[str] = field(default="")
    intervention_sector_definition: Optional[str] = field(default="")
    outcome_tag_definition: Optional[str] = field(default="")
    outcome_group_definition: Optional[str] = field(default="")
    outcome_sector_definition: Optional[str] = field(default="")

    def get_score(self):
        return {"value": self.value, "lower": self.lower, "upper": self.upper}

    @property
    def paper_label(self) -> str:
        name = self.author_name if self.author_name else ""

        if self.year is None or (
            isinstance(self.year, float) and math.isnan(self.year)
        ):
            year_str = ""
        else:
            year_str = str(int(self.year))

        complete_source = f"{name} ({year_str})" if year_str else name

        return complete_source

    @property
    def value(self) -> float:
        numeric_values = re.findall(r"(?:-|)\d+(?:\.\d+|)", self.treatment_effect)
        value = float(numeric_values[0]) if len(numeric_values) > 0 else None
        return value

    @property
    def lower(self) -> float:
        numeric_values = re.findall(r"(?:-|)\d+(?:\.\d+|)", self.treatment_effect)
        value = float(numeric_values[0]) if len(numeric_values) > 0 else None
        lower = float(numeric_values[1]) if len(numeric_values) > 1 else value

        return lower

    @property
    def upper(self) -> float:
        numeric_values = re.findall(r"(?:-|)\d+(?:\.\d+|)", self.treatment_effect)

        value = float(numeric_values[0]) if len(numeric_values) > 0 else None
        upper = float(numeric_values[2]) if len(numeric_values) > 2 else value

        return upper

    def to_json(self) -> str:
        return self.to_json()


@dataclass_json
@dataclass
class TagData:
    id: int
    label: str
    type: str
    level: str
    details: Optional[str] = field(default="")
    definition: Optional[str] = field(default="")

    def to_json(self) -> str:
        return self.to_json()


@dataclass_json
@dataclass
class KnowledgeResults:
    tag: TagData
    study_data_points: list[StudyDataPoint]

    def get_plot_data(self) -> list[dict]:
        groups = {}
        for study in self.study_data_points:
            key = (study.intervention_tag_id, study.outcome_tag_id)
            if key not in groups:
                groups[key] = {
                    "outcome": study.outcome_tag_label,
                    "outcome_id": study.outcome_tag_id,
                    "outcome_sector": study.outcome_sector_label,
                    "intervention": study.intervention_tag_label,
                    "intervention_id": study.intervention_tag_id,
                    "intervention_sector": study.intervention_sector_label,
                    "data": [],
                }
            groups[key]["data"].append(study)

        plot_data = []
        for key, studies in groups.items():
            aggregate = {"lower": 0, "upper": 0, "value": 0}
            data = []
            lower_values = []
            value_values = []
            upper_values = []
            for study in studies["data"]:
                data.append(
                    {
                        "label": study.paper_label,
                        "score": study.get_score(),
                        "paper_id": study.paper_id,
                        "paper_title": study.paper_title,
                        "country": study.country,
                        "paper_citation": study.paper_citation,
                    }
                )
                lower_values.append(study.lower)
                value_values.append(study.value)
                upper_values.append(study.upper)

            aggregate["lower"] = float(np.median(lower_values))
            aggregate["upper"] = float(np.median(upper_values))
            aggregate["value"] = float(np.median(value_values))

            if len(data) > 0:
                plot_data.append(
                    {
                        "outcome": studies["outcome"],
                        "outcome_id": studies["outcome_id"],
                        "outcome_sector": studies["outcome_sector"],
                        "intervention": studies["intervention"],
                        "intervention_id": studies["intervention_id"],
                        "intervention_sector": studies["intervention_sector"],
                        "aggregate": aggregate,
                        "data": data,
                    }
                )
        return plot_data

    def get_summary_sources(self) -> list[dict]:
        sources = {}
        for study in self.study_data_points:
            if study.paper_id not in sources:
                sources[study.paper_id] = {
                    "id": str(study.paper_id),
                    "paper_id": str(study.paper_id),
                    "short_paper_id": str(study.paper_id),
                    "title": study.paper_title,
                    "doi_url": study.paper_doi_url,
                    "citation": study.paper_citation,
                }
        return list(sources.values())

    def has_enough_db_hits(self) -> bool:
        return len(self.study_data_points) > 0

    def to_json(self) -> str:
        return self.to_json()
