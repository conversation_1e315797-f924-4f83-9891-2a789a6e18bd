from dataclasses import dataclass

@dataclass
class LLMCallStat:
    """
    This module defines data structures for tracking statistics related to LLM (Large Language Model) calls within the agent system.
    It provides the `LLMCallStat` dataclass, which records detailed information about each LLM invocation, including its source,
    name, iteration, token usage, and latency. These records are used for monitoring, analysis, and optimization of agent and tool interactions.
    """
    # ‘source’ lets us distinguish “agent” vs “tool”
    source: str                     # "agent" or "tool"
    name: str                       # e.g. "agent_loop" or "sql_generator"
    iteration: int | None           # None for tool-calls
    prompt_tokens: int = 0
    completion_tokens: int = 0
    thought_tokens: int = 0
    latency: float = 0.0            # seconds

@dataclass
class ToolCallStat:
    tool_name: str
    duration: float                # s
    success: bool = True
    error: str | None = None
    prompt_tokens: int = 0
    completion_tokens: int = 0
    thought_tokens: int = 0