import React, { useState, useRef, useEffect } from "react";
import { Box } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import StudiesRegionPlot from "./StudiesRegionPlot";
import StudiesYearPlot from "./StudiesYearPlot";
import QualityScorePlot from "./QualityScorePlot"
import { Tabs, Tab } from "@mui/material";
import "./ForestPlot.css";

interface DescriptivePlotProps {
  plotData: any;
}

const DescriptivePlot: React.FC<DescriptivePlotProps> = ({
  plotData,
}: DescriptivePlotProps) => {
  const theme = useTheme();

  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
    
    const containerRef = useRef<HTMLDivElement>(null);
      const [chartWidth, setChartWidth] = useState(0);
    
      useEffect(() => {
        if (containerRef.current) {
          const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
              setChartWidth(entry.contentRect.width);
            }
          });
          resizeObserver.observe(containerRef.current);
    
          return () => resizeObserver.disconnect();
        }
      }, []);

  if (!plotData) {
    return null;
  }

  interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        {...other}
      >
        {value === index && (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              backgroundColor: `${theme.palette.background.default}`,
              overflow: "hidden",
              width: "100%",
              maxWidth: "100%",
              [theme.breakpoints.down("sm")]: {
                padding: "8px",
              },
            }}
          >
            {children}
          </Box>
        )}
      </div>
    );
  }

  return (
      <Box
          ref={containerRef}
      sx={{
        display: "flex",
        flexDirection: "column",
        borderRadius: "8px",
        border: `1px solid ${theme.elevation.outlined}`,
        backgroundColor: `${theme.palette.background.default}`,
        overflow: "hidden",
              width: "100%",
              height: "100%",
        maxWidth: "100%",
        [theme.breakpoints.down("sm")]: {
          padding: "8px",
        },
      }}
    >
      <Tabs value={value} onChange={handleChange}>
        <Tab label="By region" value={0} />
        <Tab label="Per year" value={1} />
        <Tab label="By quality score" value={2} />
      </Tabs>
      <CustomTabPanel value={value} index={0}>
        <StudiesRegionPlot plotData={plotData} />
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        <StudiesYearPlot plotData={plotData} chartWidth={chartWidth} />
      </CustomTabPanel>
      <CustomTabPanel value={value} index={2}>
        <QualityScorePlot plotData={plotData} chartWidth={chartWidth} />
      </CustomTabPanel>
    </Box>
  );
};

export default DescriptivePlot;
