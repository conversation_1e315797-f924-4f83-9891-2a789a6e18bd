// src/utils/test/ConversationUtils.test.ts

import { replaceTags } from '../../components/Conversation/Summary/Streaming/Utils';
import { Source } from '../../types/ConversationTypes';

const mockSources: Source[] = [
    { short_paper_id: 'A1', paper_id: 'paperIdA1' } as Source,
    { short_paper_id: 'B2', paper_id: 'paperIdB2' } as Source,
    { short_paper_id: 'D865', paper_id: '4368' } as Source,
    { short_paper_id: 'EF4', paper_id: 'paperIdEF4' } as Source,
    { short_paper_id: 'G123', paper_id: 'paperIdG123' } as Source,
    { short_paper_id: 'S869', paper_id: 'paperIdS869' } as Source,
    { short_paper_id: 'L364', paper_id: 'paperIdL364' } as Source,
    { short_paper_id: 'J96', paper_id: 'paperIdJ96' } as Source,
    { short_paper_id: 'X99', paper_id: 'paperIdX99' } as Source,
    { short_paper_id: 'XY100', paper_id: 'paperIdXY100' } as Source,
];

const mockPlotData: any[] = [];

describe('replaceTags - Source Tag Processing', () => {

    const testRegexMatch = (regex: RegExp, text: string) => {
        const match = regex.exec(text);
        if (match) {
            regex.lastIndex = 0;
            return match[1];
        }
        return null;
    };

    const regexForSourcesTester = /\[\s*([A-Za-z]+\d+(?:\s*,\s*[A-Za-z]+\d+)*)\s*\]/g;

    it('should match single-letter-digit codes', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A1]')).toBe('A1');
        expect(testRegexMatch(regexForSourcesTester, '[B23]')).toBe('B23');
    });

    it('should match multi-letter-digit codes', () => {
        expect(testRegexMatch(regexForSourcesTester, '[EF4]')).toBe('EF4');
        expect(testRegexMatch(regexForSourcesTester, '[GHI123]')).toBe('GHI123');
        expect(testRegexMatch(regexForSourcesTester, '[S869]')).toBe('S869');
    });

    it('should match multiple codes separated by commas', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A1,B2]')).toBe('A1,B2');
        expect(testRegexMatch(regexForSourcesTester, '[EF4,G123]')).toBe('EF4,G123');
    });

    it('should handle spaces around commas', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A1 , B2]')).toBe('A1 , B2');
        expect(testRegexMatch(regexForSourcesTester, '[EF4 , G123 , H456]')).toBe('EF4 , G123 , H456');
    });

    it('should handle optional leading/trailing spaces inside brackets', () => {
        expect(testRegexMatch(regexForSourcesTester, '[ A1]')).toBe('A1');
        expect(testRegexMatch(regexForSourcesTester, '[A1 ]')).toBe('A1');
        expect(testRegexMatch(regexForSourcesTester, '[  A1  ]')).toBe('A1');
        expect(testRegexMatch(regexForSourcesTester, '[  EF4 , G123  ]')).toBe('EF4 , G123');
    });

    it('should NOT match codes starting with digits', () => {
        expect(testRegexMatch(regexForSourcesTester, '[123A]')).toBeNull();
    });

    it('should NOT match codes with no digits', () => {
        expect(testRegexMatch(regexForSourcesTester, '[ABC]')).toBeNull();
    });

    it('should NOT match codes with spaces inside the ID', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A 1]')).toBeNull();
    });

    it('should NOT match codes with hyphens or other special chars', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A-1]')).toBeNull();
    });

    it('should NOT match if missing brackets', () => {
        expect(testRegexMatch(regexForSourcesTester, 'A1')).toBeNull();
    });

    it('should NOT match if comma separator is missing', () => {
        expect(testRegexMatch(regexForSourcesTester, '[A1 B2]')).toBeNull();
    });

    const replaceTagsTestCases = [
        {
            name: "text with source tag, including surrounding text",
            inputText: "notebooks, and other supplies [D865]",
            expectedOutput: "notebooks, and other supplies [](?paper_id=4368)",
        },
        {
            name: "single-letter-digit source tag",
            inputText: "Some text with a source [A1].",
            expectedOutput: "Some text with a source [](?paper_id=paperIdA1).",
        },
        {
            name: "multi-letter-digit source tag",
            inputText: "Findings from [EF4].",
            expectedOutput: "Findings from [](?paper_id=paperIdEF4).",
        },
        {
            name: "another multi-letter-digit source tag",
            inputText: "Result is based on [XY100].",
            expectedOutput: "Result is based on [](?paper_id=paperIdXY100).",
        },
        {
            name: "multiple mixed source tags with combined URLs",
            inputText: "See references [G123, S869, X99].",
            expectedOutput: "See references [](?paper_id=paperIdG123&paper_id=paperIdS869&paper_id=paperIdX99).",
        },
        {
            name: "source with leading space inside bracket",
            inputText: "Details in [ A1].",
            expectedOutput: "Details in [](?paper_id=paperIdA1).",
        },
        {
            name: "source with trailing space inside bracket",
            inputText: "Further reading [B2 ].",
            expectedOutput: "Further reading [](?paper_id=paperIdB2).",
        },
        {
            name: "source with both leading and trailing spaces inside bracket",
            inputText: "Look here [  EF4  ].",
            expectedOutput: "Look here [](?paper_id=paperIdEF4).",
        },
        {
            name: "multiple sources with spaces around commas",
            inputText: "More info in [  L364 , J96  ].",
            expectedOutput: "More info in [](?paper_id=paperIdL364&paper_id=paperIdJ96).",
        },
        {
            name: "mixed valid and invalid source tags, replacing only valid ones",
            inputText: "Mixed sources [A1, INVALID2, EF4].",
            expectedOutput: "Mixed sources [](?paper_id=paperIdA1&paper_id=paperIdEF4).",
        },
        {
            name: "source tag that matches regex but is not in mockSources",
            inputText: "This source is missing [Z1].",
            expectedOutput: "This source is missing .",
        },
        {
            name: "text with no source tags",
            inputText: "Just some plain text.",
            expectedOutput: "Just some plain text.",
        },
        {
            name: "empty string",
            inputText: "",
            expectedOutput: "",
        },
        {
            name: "text with only invalid bracket formats (regex won't match)",
            inputText: "Bad formats: [123A] [ABC] [A 1] [A-1].",
            expectedOutput: "Bad formats: [123A] [ABC] [A 1] [A-1].",
        },
    ];

    test.each(replaceTagsTestCases)(
        'should correctly process "$name" - Input: "$inputText"',
        ({ inputText, expectedOutput }) => {
            expect(replaceTags(inputText, mockSources, mockPlotData)).toBe(expectedOutput);
        }
    );
});

describe('replaceTags - Plot Icon Processing', () => {

    const testRegexMatch = (regex: RegExp, text: string) => {
        const match = regex.exec(text);
        if (match) {
            regex.lastIndex = 0;
            return match[1];
        }
        return null;
    };

    const regexForPlotsTester = /\[\s*(intervention\s*=\s*\d+\s*,\s*outcome\s*=\s*\d+|outcome\s*=\s*\d+\s*,\s*intervention\s*=\s*\d+)\s*\]/g;

    it('should match intervention and outcome parameters in correct order', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=123,outcome=456]')).toBe('intervention=123,outcome=456');
    });

    it('should match outcome and intervention parameters in reverse order', () => {
        expect(testRegexMatch(regexForPlotsTester, '[outcome=456,intervention=123]')).toBe('outcome=456,intervention=123');
    });

    it('should handle spaces around parameters and comma', () => {
        expect(testRegexMatch(regexForPlotsTester, '[ intervention = 123 , outcome = 456 ]')).toBe('intervention = 123 , outcome = 456');
        expect(testRegexMatch(regexForPlotsTester, '[ outcome = 456 , intervention = 123 ]')).toBe('outcome = 456 , intervention = 123');
    });

    it('should handle optional leading/trailing spaces inside brackets', () => {
        expect(testRegexMatch(regexForPlotsTester, '[  intervention=1,outcome=2  ]')).toBe('intervention=1,outcome=2');
    });

    it('should NOT match if intervention is missing', () => {
        expect(testRegexMatch(regexForPlotsTester, '[outcome=456]')).toBeNull();
    });

    it('should NOT match if outcome is missing', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=123]')).toBeNull();
    });

    it('should NOT match if parameter values are not digits', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=abc,outcome=def]')).toBeNull();
    });

    it('should NOT match if only one parameter is present', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=123]')).toBeNull();
        expect(testRegexMatch(regexForPlotsTester, '[outcome=456]')).toBeNull();
    });

    it('should NOT match if additional parameters are present', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=1,outcome=2,extra=3]')).toBeNull();
    });

    it('should NOT match if comma is missing', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=1 outcome=2]')).toBeNull();
    });

    it('should NOT match if parameters are duplicated', () => {
        expect(testRegexMatch(regexForPlotsTester, '[intervention=1,intervention=2,outcome=3]')).toBeNull();
    });

    const plotTagTestCases = [
        {
            name: "text with plot tag, standard order",
            inputText: "Here is a plot [intervention=123,outcome=456] related to results.",
            expectedOutput: "Here is a plot [](?intervention=123&outcome=456) related to results.",
        },
        {
            name: "text with plot tag, reversed order",
            inputText: "Another plot [outcome=789,intervention=000] for analysis.",
            expectedOutput: "Another plot [](?outcome=789&intervention=000) for analysis.",
        },
        {
            name: "plot tag with extra spaces",
            inputText: "Chart data [  intervention = 1 ,  outcome = 2  ].",
            expectedOutput: "Chart data [](?intervention=1&outcome=2).",
        },
        {
            name: "multiple plot tags in text",
            inputText: "Plots: [intervention=1,outcome=2] and [outcome=3,intervention=4].",
            expectedOutput: "Plots: [](?intervention=1&outcome=2) and [](?outcome=3&intervention=4).",
        },
        {
            name: "mixed source and plot tags",
            inputText: "See [A1] for details and plot [intervention=5,outcome=6].",
            expectedOutput: "See [](?paper_id=paperIdA1) for details and plot [](?intervention=5&outcome=6).",
        },
        {
            name: "text with only invalid plot tag format (regex won't match)",
            inputText: "Invalid plot: [intervention=abc,outcome=def].",
            expectedOutput: "Invalid plot: [intervention=abc,outcome=def].",
        },
        {
            name: "text with only partial plot tag (regex won't match)",
            inputText: "Partial plot: [intervention=123].",
            expectedOutput: "Partial plot: [intervention=123].",
        },
        {
            name: "empty string with plot tags",
            inputText: "",
            expectedOutput: "",
        },
        {
            name: "text with no plot tags",
            inputText: "Just some plain text.",
            expectedOutput: "Just some plain text.",
        }
    ];

    test.each(plotTagTestCases)(
        'should correctly process "$name" - Input: "$inputText"',
        ({ inputText, expectedOutput }) => {
            expect(replaceTags(inputText, mockSources, mockPlotData)).toBe(expectedOutput);
        }
    );
});