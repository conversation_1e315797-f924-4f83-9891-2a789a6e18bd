/**
 * Text formatting utility functions
 */

/**
 * Converts a name from "First Last" format to "Last, First" format
 * @param name Name to invert
 * @returns Inverted name in "Last, First" format
 */
export const invertName = (name: string): string => {
  if (!name) return '';
  const parts = name.trim().split(/\s+/);
  if (parts.length === 1) return parts[0];
  const lastName = parts.pop() || '';
  return `${lastName}, ${parts.join(' ')}`;
};

/**
 * Converts a string to title case, capitalizing the first letter of each word
 * @param text Text to convert to title case
 * @returns Text in title case
 */
export const toTitleCase = (text: string): string => {
  if (!text) return '';
  
  // List of words that should not be capitalized in titles (unless they're the first word)
  const lowercaseWords = [
    'a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'in', 
    'nor', 'of', 'on', 'or', 'so', 'the', 'to', 'up', 'yet'
  ];
  
  return text.toLowerCase().split(/\s+/).map((word, index) => {
    // Always capitalize the first word or if the word shouldn't be lowercase
    if (index === 0 || !lowercaseWords.includes(word)) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
    return word;
  }).join(' ');
};

/**
 * Capitalizes just the first letter of a string
 * @param text Text to capitalize
 * @returns Text with first letter capitalized
 */
export const capitalizeFirstLetter = (text: string): string => {
  if (!text) return '';
  return text.charAt(0).toUpperCase() + text.slice(1);
};

/**
 * Converts a sentence to sentence case (only first letter capitalized)
 * @param text Text to convert to sentence case
 * @returns Text in sentence case
 */
export const sentenceCase = (str: string): string => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};
