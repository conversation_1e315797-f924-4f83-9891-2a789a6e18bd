import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime
from services.waitlist import add_to_waitlist
from sqlalchemy import Insert


@pytest.fixture
def mock_session():
    session = AsyncMock()
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock()
    return session


@pytest.mark.asyncio
async def test_add_to_waitlist(mock_session):
    test_email = "<EMAIL>"
    test_organization = "Test Org"

    # Mock datetime.now() to return a fixed time
    fixed_time = datetime(2025, 1, 1, 12, 0, 0)

    with patch(
        "services.waitlist.get_userdata_db_session", return_value=mock_session
    ), patch("services.waitlist.datetime") as mock_datetime:

        mock_datetime.now.return_value = fixed_time

        await add_to_waitlist(test_email, test_organization)

        # Verify the session methods were called
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

        # Get the insert query that was executed
        insert_query = mock_session.execute.call_args[0][0]

        # Verify it's an insert query

        assert isinstance(insert_query, Insert)

        # Convert the query parameters to a dict for easier assertion
        query_params = insert_query.compile().params

        # Verify the data being inserted
        assert query_params["email"] == test_email
        assert query_params["organization"] == test_organization
        assert query_params["created_at"] == fixed_time
        assert query_params["updated_at"] == fixed_time
