import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession

from .utils import (
    create_database_engine,
    create_async_session_maker,
    get_db_session,
)

logger = logging.getLogger(__name__)

# Create engine and session maker for userdata database
engine = create_database_engine("MYSQL_DATABASE")
AsyncSessionLocal = create_async_session_maker(engine)


@asynccontextmanager
async def get_userdata_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for userdata database sessions with error handling.
    """
    async with get_db_session(AsyncSessionLocal) as session:
        yield session
