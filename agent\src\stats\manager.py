from collections import defaultdict
from typing import Dict, List
from .records import LLMCallStat, ToolCallStat

class StatsManager:
    def __init__(self):
        # keep every LLM call and tool call in memory
        self.llm_calls:  List[LLMCallStat]  = []
        self.tool_calls: List[ToolCallStat] = []

        # your nine “flat” counters for the CLI
        self.general: Dict[str, float] = defaultdict(float)
        self.general.update({
            "total_thinking_time":    0.0,
            "total_action_time":      0.0,
            "total_iterations":       0,
            "successful_queries":     0,
            "failed_queries":         0,
            "pipeline_executions":    0,
            # new token counters
            "total_prompt_tokens":    0,
            "total_completion_tokens":0,
            "total_thought_tokens":   0,
        })

    # ——— LLM calls (agent or tool) ———————————————————————————————
    def record_llm_call(
        self,
        *,
        source: str,
        name: str,
        iteration: int | None,
        prompt: int = 0,
        completion: int = 0,
        thought: int = 0,
        latency: float = 0.0,
    ):
        self.llm_calls.append(
            LLMCallStat(source, name, iteration, prompt, completion, thought, latency)
        )
        self.general["total_prompt_tokens"]     += prompt
        self.general["total_completion_tokens"] += completion
        self.general["total_thought_tokens"]    += thought
        if source == "agent":
            self.general["total_thinking_time"] += latency

    # ——— tool calls (no pipeline count here) —————————————————————————
    def record_tool_call(
        self,
        *,
        tool_name: str,
        duration: float,
        success: bool = True,
        error: str | None = None,
        prompt: int = 0,
        completion: int = 0,
        thought: int = 0,
    ):
        self.tool_calls.append(
            ToolCallStat(tool_name, duration, success, error, prompt, completion, thought)
        )
        # we do NOT increment pipeline_executions here
        self.general["total_prompt_tokens"]     += prompt
        self.general["total_completion_tokens"] += completion
        self.general["total_thought_tokens"]    += thought

    # ——— your existing general helpers —————————————————————————————
    def add_thinking_time(self, t: float):
        self.general["total_thinking_time"] += t

    def add_action_time(self, t: float):
        self.general["total_action_time"] += t

    def increment_iterations(self):
        self.general["total_iterations"] += 1

    def increment_pipeline_executions(self):
        self.general["pipeline_executions"] += 1

    def mark_query_successful(self):
        self.general["successful_queries"] += 1

    def mark_query_failed(self):
        self.general["failed_queries"] += 1

    # ——— read APIs for CLI ————————————————————————————————————————
    def summary(self) -> Dict[str, float]:
        return dict(self.general)

    def breakdown_per_tool(self) -> Dict[str, Dict[str, float]]:
        from collections import defaultdict
        out = defaultdict(lambda: defaultdict(float))
        for c in self.tool_calls:
            d = out[c.tool_name]
            d["calls"]            += 1
            d["duration"]         += c.duration
            d["prompt_tokens"]    += c.prompt_tokens
            d["completion_tokens"]+= c.completion_tokens
            d["thought_tokens"]   += c.thought_tokens
        return out