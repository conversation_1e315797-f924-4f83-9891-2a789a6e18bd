"use client";
import React from "react";
import { useRouter, usePathname } from "next/navigation";
import { useTheme } from "@mui/material/styles";
import {
    Box,
    Typography,
    AppBar,
    Toolbar,
    IconButton,
} from "@mui/material";
import JoinWaitlistButton from "./JoinWaitlistButton";
import { useIsMobile, useIsTablet } from './MobileUtils';
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import Image from 'next/image';

const Navbar = () => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;
    const router = useRouter();
    const theme = useTheme();
    const navItems = [
        { label: "Home", pageName: "" },
        { label: "Mission", pageName: "mission" },
        { label: "FAQ", pageName: "faq" },
        { label: "News & Events", pageName: "newsevents" },
        { label: "Team", pageName: "team" }
    ];
    const [navOpen, setNavOpen] = React.useState(false);
    const pathname = usePathname();

    const MainNavHeader = () => {
        return (
            <AppBar
                sx={{
                    position: 'relative',
                    top: 0,
                    left: 0,
                    width: '100%',
                    zIndex: 1100,
                    background: '#FFF',
                    borderBottom: '1px solid #F1F6FA',
                    boxShadow: 'none',
                    padding: '24px 16px',
                    height: isMobile ? 'auto' : '122px'
                }}
            >
                <Toolbar
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: isMobile ? '100%' : isTablet ? '100%' : isMobileOrTablet ? '100%' : '85%',
                        margin: '0 auto',
                    }}
                >
                    {/* Logo Section (Left Aligned) */}
                    <Box sx={{ display: 'flex', gap: isMobileOrTablet ? '3px' : '24px', alignItems: 'center' }}>
                        <Image
                            src="/images/header_footer/logo-wb-header-en.svg"
                            alt="World Bank Logo"
                            width={isMobile ? 100 : isTablet ? 100 : 180}
                            height={isMobile ? 36 : isTablet ? 36 : 36}
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                                window.open('https://www.worldbank.org/ext/en/home', '_self');
                            }}
                        />
                    </Box>

                    {/* Centered Trending Data Section */}
                    {!isMobile && (
                        <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobileOrTablet ? '3px' : '20px' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobileOrTablet ? '3px' : '8px' }}>
                                    <IconButton
                                        sx={{
                                            padding: '6px',
                                            cursor: 'default',
                                            '&:hover': {
                                                backgroundColor: 'transparent',
                                            },
                                        }}
                                        disableRipple
                                    >
                                        <Image
                                            src="/images/header_footer/trending_data_icon.svg"
                                            alt="Trending Up"
                                            width={15}
                                            height={11}
                                        />
                                    </IconButton>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            color: '#012740',
                                            fontWeight: '700',
                                            fontSize: isMobile ? '12px' : isTablet ? '12px' : '16px',
                                            cursor: 'default',
                                        }}
                                    >
                                        Trending Data
                                    </Typography>
                                </Box>
                                <Typography
                                    variant="body2"
                                    sx={{
                                        color: '#004370',
                                        fontSize: isMobile ? '12px' : isTablet ? '12px' : '16px',
                                        textDecoration: 'none',
                                        '&:hover': {
                                            textDecoration: 'none',
                                        },
                                        cursor: 'pointer',
                                    }}
                                    component="a"
                                    href="https://www.worldbank.org/en/topic/poverty/overview?intcid=ecr_hp_trendingdata_en_ext"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    Around 700M live on less than $2.15 daily, the extreme poverty line
                                </Typography>
                            </Box>
                        </Box>
                    )}

                    {/* Mobile Menu Button (if mobile) */}
                    {isMobile && (
                        <IconButton
                            aria-label="toggle menu"
                            onClick={() => setNavOpen(!navOpen)}
                            sx={{
                                color: theme.palette.background.default,
                                backgroundColor: theme.palette.action.active,
                                width: "36px",
                                height: "36px",
                            }}
                        >
                            {navOpen ? <CloseIcon fontSize="medium" /> : <MenuIcon fontSize="medium" />}
                        </IconButton>
                    )}
                </Toolbar>
            </AppBar>
        );
    };

    const SubNavHeader = () => {
        return (
            <AppBar
                sx={{
                    top: 0,
                    left: 0,
                    width: "100%",
                    zIndex: 1099,
                    background: theme.palette.background.default,
                    boxShadow: "none",
                    position: 'relative',
                    height: isMobileOrTablet ? 'auto' : '82px'
                }}
            >
                <Toolbar
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        padding: "20px",
                        minWidth: isMobileOrTablet ? "95%" : "60%",
                        margin: "0 auto",
                    }}
                >
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            gap: isMobileOrTablet ? "32px" : "64px",
                            alignItems: "center",
                            width: "100%",
                            padding: "0px",
                            margin: "0px",
                            height: "74px",
                            backgroundColor: theme.palette.background.default,
                            transition: "all 0.3s ease",
                            // cursor: "pointer",
                            zIndex: 99,
                            "&:hover": {
                                backgroundColor: theme.palette.background.default,
                            },
                            justifyContent: 'center',
                        }}
                    >
                        {/* Center Section: Navigation */}
                        <Box>
                            <nav>
                                <Box
                                    component="ul"
                                    sx={{
                                        display: "flex",
                                        gap: "15px",
                                        listStyle: "none",
                                        padding: 0,
                                        margin: 0,
                                    }}
                                >
                                    {navItems.map((item, index) => {
                                        const itemPath = item.pageName ? `/${item.pageName}` : '/';
                                        const isSelected = pathname === itemPath;

                                        return (
                                            <Typography
                                                key={index}
                                                component="li"
                                                sx={{
                                                    cursor: "pointer",
                                                    fontSize: "13px",
                                                    fontStyle: "normal",
                                                    fontWeight: "600",
                                                    lineHeight: '22px',
                                                    letterSpacing: '1px',
                                                    textTransform: 'uppercase',
                                                    color: theme.palette.primary.main,
                                                    transition: "color 0.3s ease",
                                                    padding: '4px 5px',
                                                    borderRadius: isSelected ? '4px' : '0px',
                                                    background: isSelected ? theme.palette.action.selected : 'transparent',
                                                    "&:hover": {
                                                        color: theme.palette.primary.main,
                                                        borderRadius: '4px',
                                                        background: theme.palette.action.selected,
                                                    },
                                                }}
                                                onClick={() => {
                                                    router.push(itemPath);
                                                }}
                                            >
                                                {item.label}
                                            </Typography>
                                        );
                                    })}
                                </Box>
                            </nav>
                        </Box>

                        {/* Right Section: Button */}
                        <JoinWaitlistButton />
                    </Box >
                </Toolbar>
            </AppBar>
        );
    };
    const renderMobileAppBar = () => {
        console.log("Rendering Mobile App Bar");

        return (
            <>
                {/* Dropdown Menu - Positioned Below AppBar */}
                {navOpen && (
                    <Box
                        sx={{
                            position: "relative !important",
                            top: 0,
                            left: "50%",
                            transform: "translateX(-50%)",
                            width: "100%",
                            background: theme.palette.background.default,
                            borderRadius: "16px",
                            padding: "16px 8px",
                            zIndex: 1099,
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            alignSelf: "stretch",
                            flexDirection: "column",
                            margin: "auto",
                        }}
                    >
                        {navItems.map((item, index) => {
                            const itemPath = item.pageName ? `/${item.pageName}` : '/';
                            const isSelected = pathname === itemPath;

                            return (
                                <Typography
                                    key={index}
                                    sx={{
                                        padding: "8px",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        fontWeight: "600",
                                        color: theme.palette.primary.main,
                                        background: isSelected ? theme.palette.action.selected : 'transparent',
                                        "&:hover": { background: theme.palette.action.selected },
                                    }}
                                    onClick={() => {
                                        router.push(itemPath);
                                        setNavOpen(false);
                                    }}
                                >
                                    {item.label}
                                </Typography>
                            );
                        })}
                        <Box sx={{ marginTop: "16px", textAlign: "center" }}>
                            <JoinWaitlistButton />
                        </Box>
                    </Box>
                )
                }
            </>
        );
    };


    return (
        <>
            <MainNavHeader />
            {isMobile ? renderMobileAppBar() : SubNavHeader()}
        </>
    );
};

export default Navbar;