"""Tool management module for the research agent.

Refactored `ToolManager` to leverage the new `Tool` base-class wrapper.

Key changes
-----------
1. **Single entry-point** – all calls go through `await tool(*args, **kw)` so
   the metadata normalisation (added in the Tool base class) always runs.
2. **Internal helper `_invoke_tool`** – wraps execution, captures success
   flag, duration, error and returns the raw result.
3. **Simplified caching logic** – left intact for now, but all direct
   `tool.func` invocations were changed to `_invoke_tool`.
4. **Reduced duplicate `if verbose` logging** – uses `self.logger`.

This is a drop-in replacement; public methods and behaviour remain the same.
"""

import inspect
import logging
import time
from typing import Any, Dict, List, Optional

from src.agent.config import DatasetSettings
from src.agent.models import AgentCache
from src.stats import StatsManager
from src.tools.base import Tool
from src.tools.entity_extractor import EntityExtractor, ExtractedEntities
from src.tools.methodology_explainer import MethodologyExplainer
from src.tools.rag_searcher import <PERSON><PERSON><PERSON><PERSON>s, RAGSearcher
from src.tools.sql_generator import QueryR<PERSON>ult, SQLGenerator
from src.tools.structure_data_organizer import (
    StructuredData,
    StructuredDataOrganizer,
    StructuredDataOrganizerV2,
    StructuredDataV2,
)
from src.tools.summary_generator import FinalAnswer, FinalAnswerGenerator
from src.utils.file_management import DatasetManager
from src.utils.flags import get_use_new_da_flag

# TODO: remove after we completely remove the old DA
USE_NEW_DA_FLAG = get_use_new_da_flag()

class ToolCache:  # pylint: disable=too-many-instance-attributes
    """Cache for tool outputs."""

    def __init__(self):
        """Initialize empty cache."""
        self.entities: Optional[ExtractedEntities] = None
        self.query_result: Optional[QueryResult] = None
        self.rag_results: Optional[RAGResults] = None
        self.structured_data: Optional[StructuredData] = None
        self.structured_data_v2: Optional[List[StructuredDataV2]] = None
        self.final_answer: Optional[FinalAnswer] = None
        self.url: Optional[str] = None
        self.url_post_processed: Optional[str] = None
        self.data_used_url: Optional[str] = None
        dataset_settings = DatasetSettings()
        self.dataset_manager: Optional[DatasetManager] = DatasetManager(
            dataset_settings
        )

    def update_entities(self, entities: ExtractedEntities) -> None:
        """Update cached entities."""
        self.entities = entities

    def update_query_result(self, query_result: QueryResult) -> None:
        """Update cached query result."""
        self.query_result = query_result
        self.url = query_result.dataset

    def update_rag_results(self, rag_results: RAGResults) -> None:
        """Update cached RAG results."""
        self.rag_results = rag_results

    async def update_structured_data(self, structured_data: StructuredData) -> None:
        """Update cached structured data."""
        self.structured_data = structured_data
        self.url_post_processed = structured_data.url

    async def update_structured_data_v2(
        self, structured_data: List[StructuredDataV2]
    ) -> None:
        """Update cached structured data."""
        self.structured_data_v2 = structured_data

    def update_data_used_url(self, url: str) -> None:
        """Update cached data URL."""
        self.data_used_url = url

    def update_final_answer(self, final_answer: FinalAnswer) -> None:
        """Update cached final answer"""
        self.final_answer = final_answer

    def clear(self) -> None:
        """Clear all cached data."""
        self.entities = None
        self.query_result = None
        self.rag_results = None
        self.structured_data = None
        self.structured_data_v2 = None
        self.final_answer = None
        self.data_used_url = None
        self.url_post_processed = None


class ToolManager:  # pylint: disable=too-many-instance-attributes, unused-variable
    """Manages registration and execution of tools."""

    def __init__(
        self,
        config: Dict[str, Any],
        conversation_id: Optional[str],
        session: Optional[Any] = None,
        stats_manager: StatsManager | None = None,
        agent_cache: AgentCache | None = None,
    ) -> None:
        self.tools: dict[str, Tool] = {}
        self.config = config
        self.session = session
        self.stats = stats_manager
        self.conversation_id = conversation_id
        self.agent_cache = agent_cache
        self.cache = ToolCache()
        self.logger = logging.getLogger("tool_manager")
        self._tool_aliases = {
            "EntityExtractor": "entity_extractor",
            "SQLGenerator": "sql_generator",
            "RAGSearcher": "rag_search",
            "StructuredDataOrganizer": "structured_data_organizer",
            "StructuredDataOrganizerV2": "structured_data_organizer_v2",
            "FinalAnswerGenerator": "final_answer_generator",
            "MethodologyExplainer": "methodology_explainer",
        }
        self._register_default_tools()

    # ------------------------------------------------------------------
    # Registration helpers
    # ------------------------------------------------------------------

    def _register_default_tools(self) -> None:
        tool_cfg = dict(self.config)
        if self.session is not None:
            tool_cfg["session"] = self.session
        default: dict[str, Tool] = {
            "entity_extractor": EntityExtractor(tool_cfg),
            "sql_generator": SQLGenerator(tool_cfg),
            "rag_search": RAGSearcher(tool_cfg),
            "structured_data_organizer": StructuredDataOrganizer(tool_cfg),
            "structured_data_organizer_v2": StructuredDataOrganizerV2(tool_cfg),
            "final_answer_generator": FinalAnswerGenerator(tool_cfg),
            "methodology_explainer": MethodologyExplainer(tool_cfg),
        }
        for name, tool in default.items():
            self.register(name, tool)
            if tool_cfg.get("verbose"):
                self.logger.info("Registered tool: %s", name)

    def register(self, name: str, tool: Tool) -> None:
        if name in self.tools:
            self.logger.warning("Overwriting existing tool: %s", name)
        self.tools[name] = tool

    def get_tool(self, name: str) -> Tool:
        canonical = self._tool_aliases.get(name, name)
        if canonical not in self.tools:
            raise ValueError(
                f"Tool not found: {name}. Available: {list(self.tools.keys())}"
            )
        return self.tools[canonical]

    def list_tools(self) -> List[Dict[str, Any]]:
        """List all registered tools with their metadata."""
        return [
            {
                "name": name,
                "description": tool.description,
                "arguments": tool.arguments,
                "outputs": tool.outputs,
            }
            for name, tool in self.tools.items()
        ]

    async def _invoke_tool(self, tool_name: str, **kwargs) -> Any:
        """Unified execution wrapper – always calls the tool object."""
        tool = self.get_tool(tool_name)
        start = time.time()
        success = True
        err: str | None = None
        result: Any = None
        try:
            call_result = tool(**kwargs)
            if inspect.isawaitable(call_result):
                result = await call_result
            else:
                result = call_result
            return result
        except Exception as exc:
            success = False
            err = str(exc)
            raise
        finally:
            duration = time.time() - start
            md = {}
            if isinstance(result, dict):
                md = result.get("metadata", {})
            elif hasattr(result, "metadata"):
                # Handle cases where result is a dataclass with metadata
                md = getattr(result, "metadata", {})
            pr = md.get("prompt_tokens", md.get("prompt", 0)) or 0
            cm = md.get("completion_tokens", md.get("completion", 0)) or 0
            th = md.get("thought_tokens", md.get("thoughts_token_count", 0)) or 0
            if self.stats:
                self.stats.record_tool_call(
                    tool_name=tool_name,
                    duration=duration,
                    success=success,
                    error=err,
                    prompt=pr,
                    completion=cm,
                    thought=th,
                )

    # ------------------------------------------------------------------
    # Execution logging helpers
    # ------------------------------------------------------------------

    def _log_tool_start(self, tool_name: str, data: Dict[str, Any] = None) -> None:
        """Log the start of tool execution."""
        if self.agent_cache and self.conversation_id:
            self.agent_cache.log_tool_execution(
                self.conversation_id, tool_name, "started", data
            )

    def _log_tool_finish(
        self, tool_name: str, data: Dict[str, Any] = None
    ) -> None:
        """Log the completion of tool execution."""
        if self.agent_cache and self.conversation_id:
            self.agent_cache.log_tool_execution(
                self.conversation_id, tool_name, "finished", data
            )

    def _extract_tool_data(
        self, tool_name: str, result: Any = None, **kwargs
    ) -> Dict[str, Any] | None:
        """Extract relevant data for logging based on tool type and result."""
        data: Dict[str, Any] | None = None

        if tool_name == "entity_extractor" and result:
            data = {
                "interventions": getattr(result, "interventions", None),
                "outcomes": getattr(result, "outcomes", None),
            }

        elif tool_name == "sql_generator":
            if kwargs.get("entities") and not result:
                entities = kwargs["entities"]
                data = {
                    "interventions": entities.interventions if entities else [],
                    "outcomes": entities.outcomes if entities else [],
                }
            elif result:
                data = {"paper_ids": getattr(result, "paper_ids", None)}

        elif tool_name == "rag_search":
            if kwargs.get("query_result") and not result:
                query_result = kwargs["query_result"]
                data = {"user_query": getattr(query_result, "user_query", None)}
            elif result:
                data = {"documents": getattr(result, "documents", None)}

        elif tool_name == "structured_data_organizer" and result:
            data = {"text": getattr(result, "text", None)}
        elif tool_name == "structured_data_organizer_v2" and result:
            data = {"analysis": getattr(result, "analysis", None)}

        return data

    # ------------------------------------------------------------------
    # Public API used by the agent
    # ------------------------------------------------------------------

    async def execute(self, tool_name: str, **kwargs) -> Any:  # noqa: D401
        return await self._invoke_tool(tool_name, **kwargs)

    async def execute_with_cache(self, tool_name: str, **kwargs) -> Any:
        """Execute tool with caching and standardized logging."""
        start = time.time()
        tool_name = self._normalize_tool_name(tool_name)
        result: Any = None

        try:
            # Handle tool-specific pre-processing and dependencies
            if tool_name == "sql_generator":
                kwargs["entities"] = self.cache.entities

            elif tool_name == "rag_search":
                if not USE_NEW_DA_FLAG:
                    if not self.cache.query_result:
                        raise ValueError("Run sql_generator first")
                    kwargs["query_result"] = self.cache.query_result
                else:
                    # No cache needed for now
                    pass

            elif tool_name == "structured_data_organizer":
                if not self.cache.query_result:
                    raise ValueError(
                        "No SQL query results in cache. Please generate a SQL query first."
                    )
                kwargs["dataset"] = self.cache.query_result.dict_rows
                kwargs["entities"] = self.cache.entities

            elif tool_name == "structured_data_organizer_v2":
                kwargs["entities"] = self.cache.entities

            # Log tool start with relevant data
            start_data = self._extract_tool_data(tool_name, **kwargs)
            self._log_tool_start(tool_name, start_data)

            # Execute the tool
            result = await self._invoke_tool(tool_name, **kwargs)

            # Log tool completion with result data
            finish_data = self._extract_tool_data(tool_name, result)
            self._log_tool_finish(tool_name, data=finish_data)

            # Update cache based on result type
            await self._update_cache_for_result(result)

            return result
        finally:
            duration = time.time() - start
            self.logger.debug(
                "execute_with_cache(%s) finished in %.2fs", tool_name, duration
            )

    async def _update_cache_for_result(self, result: Any) -> None:
        """Update internal cache based on tool result."""
        if isinstance(result, ExtractedEntities):
            self.cache.update_entities(result)
        elif isinstance(result, QueryResult):
            self.cache.update_query_result(result)
            if result.dataset:
                self.cache.update_data_used_url(result.dataset)
        elif isinstance(result, RAGResults):
            self.cache.update_rag_results(result)
        elif isinstance(result, StructuredData):
            await self.cache.update_structured_data(result)
        elif isinstance(result, list) and all(
            isinstance(item, StructuredDataV2) for item in result
        ):
            await self.cache.update_structured_data_v2(result)
        elif isinstance(result, FinalAnswer):
            self.cache.update_final_answer(result)

    # ------------------------------------------------------------------
    # Utility getters
    # ------------------------------------------------------------------

    def _normalize_tool_name(self, tool_name: str) -> str:
        return {"rag_searcher": "rag_search"}.get(tool_name, tool_name)

    def clear_cache(self) -> None:
        self.cache.clear()
        self.logger.info("Tool cache cleared")

    def get_tool_data(self) -> Dict[str, Any]:
        return {
            "entities": (
                self.cache.entities.model_dump() if self.cache.entities else None
            ),
            "query_result": (
                self.cache.query_result.model_dump()
                if self.cache.query_result
                else None
            ),
            "rag_results": (
                self.cache.rag_results.model_dump() if self.cache.rag_results else None
            ),
            "structured_data": (
                self.cache.structured_data.model_dump()
                if self.cache.structured_data
                else None
            ),
            "data_used_url": self.cache.data_used_url,
            "url_post_processed": self.cache.url_post_processed,
            "final_answer": (
                self.cache.final_answer if self.cache.final_answer else None
            ),
            "structured_data_v2": (
                [sd.model_dump() for sd in self.cache.structured_data_v2]
                if self.cache.structured_data_v2
                else None
            ),
        }
