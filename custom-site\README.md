# Marketing Website

The website component serves as the public-facing landing page and marketing site for ImpactAI. Built with Next.js and Material-UI, it provides information about the product, team, and mission.

## Architecture

### Core Components

1. **Pages**
   - Home
   - Mission
   - FAQ
   - News & Events
   - Team
   - Waitlist

2. **Layout**
   - Responsive navigation
   - Dynamic content sections
   - Footer
   - Mobile optimization

3. **Components**
   - Dynamic page titles
   - Interactive sections
   - Video integration
   - Contact forms

### Key Features

1. **Content Sections**
   - Hero section
   - Product features
   - Mission statement
   - Team profiles
   - News and updates

2. **Interactive Elements**
   - Waitlist signup
   - Navigation menu
   - Video player
   - Social links

3. **User Experience**
   - Responsive design
   - Mobile-first approach
   - Fast page loads
   - SEO optimization

## Technical Stack

- **Framework**: Next.js
- **Language**: TypeScript
- **UI Library**: Material-UI
- **Styling**: Tailwind CSS
- **Build**: Next.js build system

## Project Structure

```
website/
├── src/
│   ├── app/
│   │   ├── components/
│   │   ├── home/
│   │   ├── mission/
│   │   ├── faq/
│   │   ├── newsevents/
│   │   └── team/
│   ├── styles/
│   └── utils/
├── public/
│   └── images/
└── config files
```

## Features

### Home Page
- Hero section with key messaging
- Feature highlights
- Product demo video
- News and events section
- Insights counter
- Waitlist signup

### Mission Page
- Problem statement
- Solution overview
- Impact metrics
- Call to action

### Team Section
- Team member profiles
- Professional backgrounds
- Contact information
- Social links

### News & Events
- Latest updates
- Media coverage
- Public engagements
- Press materials

### FAQ Section
- Categorized questions
- Detailed answers
- Topic filtering
- Contact options

### Technical Features
- SEO optimization
- Fast page loading
- Mobile responsiveness
- Analytics integration
