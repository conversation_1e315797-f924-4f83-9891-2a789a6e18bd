#!/bin/bash

# Load environment variables from .env file
ENV_FILE="$(dirname "$0")/.env"

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env file not found at $ENV_FILE"
    echo "Please create a .env file with the required environment variables:"
    echo "PROJECT_ID=your_gcp_project_id"
    echo "INSTANCE_NAME=argilla-db"
    echo "REGION=us-central1"
    echo "DB_NAME=argilla"
    echo "DB_USER=argilla"
    echo "DB_PASSWORD=your_secure_password"
    exit 1
fi

# Source the .env file
source "$ENV_FILE"

# Validate required environment variables
required_vars=(
    "PROJECT_ID" "INSTANCE_NAME" "REGION"
    "DB_NAME" "DB_USER" "DB_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set in .env file"
        exit 1
    fi
done

echo "✅ All required environment variables loaded from .env file"

# === AUTH & PROJECT ===
gcloud config set project $PROJECT_ID

# === 1. Create Cloud SQL PostgreSQL Instance ===
gcloud sql instances create $INSTANCE_NAME \
  --database-version=POSTGRES_14 \
  --cpu=2 \
  --memory=4GiB \
  --region=$REGION \
  --root-password=$DB_PASSWORD

# === 2. Create the Argilla database ===
gcloud sql databases create $DB_NAME \
  --instance=$INSTANCE_NAME

# === 3. Set the password for the default user ===
gcloud sql users set-password $DB_USER \
  --instance=$INSTANCE_NAME \
  --password=$DB_PASSWORD

# === 4. Get the connection name (used by Cloud Run) ===
INSTANCE_CONNECTION_NAME=$(gcloud sql instances describe $INSTANCE_NAME \
  --format="value(connectionName)")

echo "✅ Cloud SQL instance ready."
echo "Instance connection name: $INSTANCE_CONNECTION_NAME"

# === 5. Print out the environment variable for Cloud Run ===
ARGILLA_DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@/${DB_NAME}?host=/cloudsql/${INSTANCE_CONNECTION_NAME}"
echo ""
echo "✅ Use this in your Cloud Run deploy command:"
echo "ARGILLA_DATABASE_URL=$ARGILLA_DATABASE_URL"
