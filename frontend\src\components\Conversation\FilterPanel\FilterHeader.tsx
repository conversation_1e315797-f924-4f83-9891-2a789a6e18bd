import React from 'react';
import { Box, Chip, IconButton, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import FilterListAltIcon from '@mui/icons-material/FilterListAlt';

interface FilterHeaderProps {
    numActiveFilters?: number;
    onClearFilters?: () => void;
    onTogglePanel?: () => void;
}

const FilterHeader: React.FC<FilterHeaderProps> = ({
    numActiveFilters = 0,
    onClearFilters = () => { },
    onTogglePanel = () => { },
}) => {
    const theme = useTheme();
    const hasActiveFilters = numActiveFilters > 0;

    return (
        <Box
            id="filters-panel-header"
            sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                p: 2,
                flexShrink: 0,
                height: '68px',
                borderBottom: `1px solid ${theme.palette.divider}`
            }}
        >
            <IconButton
                size="small"
                sx={{ color: theme.palette.text.secondary }}
                onClick={onTogglePanel}
            >
                <FilterListAltIcon />
            </IconButton>
            <Box sx={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
                {hasActiveFilters && (
                    <Chip
                        label={`${numActiveFilters} Filter${numActiveFilters > 1 ? 's' : ''}`}
                        onDelete={onClearFilters}
                        deleteIcon={<CloseIcon />}
                        size="small"
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            padding: '3px 4px',
                            fontFamily: 'Roboto',
                            fontSize: '13px',
                            fontWeight: 400,
                            lineHeight: '18px',
                            letterSpacing: '0.16px',
                            backgroundColor: 'transparent',
                            color: theme.palette.primary.main,
                            border: `1px solid ${theme.palette.divider}`,
                            '& .MuiChip-deleteIcon': {
                                color: theme.palette.primary.main,
                                fontSize: '18px',
                                '&:hover': {
                                    color: theme.palette.primary.dark,
                                },
                            },
                        }}
                    />
                )}
            </Box>
        </Box>
    );
};

export default FilterHeader;