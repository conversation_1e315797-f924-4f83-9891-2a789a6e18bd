import uuid
from unittest.mock import AsyncMock, patch

import pytest
from models.agent import AgentR<PERSON>ponse
from services.agent import AgentService, get_gemini_completion
from tests.test_helpers.mocks import load_json_mocks

mock_agent_response = load_json_mocks("mock-agent-response.json")
mock_data_used = load_json_mocks("mock-data-used.json")


@pytest.fixture
def sample_conversation_id():
    return uuid.UUID("12345678-1234-5678-1234-************")


@pytest.fixture
def sample_query():
    return mock_agent_response["response"]["context"]["query"]


@pytest.fixture
def mock_response_data():
    return mock_agent_response


@pytest.fixture
def agent_service(sample_conversation_id, sample_query):
    return AgentService(sample_conversation_id, sample_query)


def mock_hashed_responses(query, response_data):
    """Mock the hashed_responses dictionary with a specific query-response pair."""
    return patch("services.agent.hashed_responses", {query: response_data})


def mock_get_json(return_value=None, side_effect=None):
    """Mock the get_json function with a specified return value."""
    return patch(
        "services.agent.get_json",
        new_callable=AsyncMock,
        return_value=return_value,
        side_effect=side_effect,
    )


def mock_post_json(return_value=None, side_effect=None):
    """Mock the post_json function with a specified return value."""
    return patch(
        "services.agent.post_json",
        new_callable=AsyncMock,
        return_value=return_value,
        side_effect=side_effect,
    )


@pytest.mark.asyncio
async def test_execute_cached_response(agent_service, mock_response_data):
    with mock_hashed_responses(agent_service.query, mock_response_data), mock_get_json(
        mock_data_used
    ):
        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query


@pytest.mark.asyncio
async def test_execute_api_call(agent_service, mock_response_data):
    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        mock_response_data
    ), mock_get_json(mock_data_used):
        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)

        assert response.context.query == agent_service.query
        assert len(response.context.tool_data.data_used) > 0


@pytest.mark.asyncio
async def test_execute_api_error(agent_service):
    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        None, side_effect=Exception("API Error")
    ):

        with pytest.raises(Exception) as exc_info:
            await agent_service.execute()

        assert str(exc_info.value) == "API Error"


@pytest.mark.asyncio
async def test_execute_invalid_response(agent_service):
    invalid_response = {"response": {"invalid": "data"}}

    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        invalid_response
    ):

        with pytest.raises(Exception):
            await agent_service.execute()


def test_agent_service_initialization(sample_conversation_id, sample_query):
    service = AgentService(sample_conversation_id, sample_query)

    assert service.conversation_id == sample_conversation_id
    assert service.query == sample_query
    assert service.agent_api_url == "https://agent.impact-ai-dev.app"


@pytest.mark.asyncio
async def test_execute_correct_api_call(agent_service, mock_response_data):
    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        mock_response_data
    ) as mock_post, mock_get_json(mock_data_used) as mock_get:

        await agent_service.execute()

        expected_url = f"{agent_service.agent_api_url}/execute"
        expected_data = {
            "conversation_id": str(agent_service.conversation_id),
            "query": agent_service.query,
        }

        mock_post.assert_called_once_with(expected_url, body=expected_data, timeout=300)
        mock_get.assert_called_once_with(
            mock_response_data["response"]["context"]["tool_data"]["data_url"]
        )


@pytest.mark.asyncio
async def test_execute_get_json_failure_defaults_to_empty_list(
    agent_service, mock_response_data
):
    """Test that when get_json fails, data_used defaults to empty list."""
    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        mock_response_data
    ), mock_get_json(None, side_effect=Exception("Network error")):

        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query
        # Verify that data_used is set to empty list when get_json fails
        assert response.context.tool_data.data_used == []


@pytest.mark.asyncio
async def test_execute_get_json_failure_with_cached_response(
    agent_service, mock_response_data
):
    """Test that when get_json fails with cached response, data_used defaults to empty list."""
    with mock_hashed_responses(agent_service.query, mock_response_data), mock_get_json(
        None, side_effect=Exception("Network error")
    ):

        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query
        # Verify that data_used is set to empty list when get_json fails
        assert response.context.tool_data.data_used == []


@pytest.mark.asyncio
async def test_execute_no_data_url_in_tool_data(agent_service, mock_response_data):
    """Test that when tool_data doesn't contain data_url, no get_json is called."""
    # Create a response without data_url
    response_without_data_url = mock_response_data.copy()
    del response_without_data_url["response"]["context"]["tool_data"]["data_url"]

    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        response_without_data_url
    ), mock_get_json(mock_data_used) as mock_get:

        response = await agent_service.execute()

        assert isinstance(response, AgentResponse)
        assert response.context.query == agent_service.query
        # Verify that get_json was not called since there's no data_url
        mock_get.assert_not_called()


@pytest.mark.asyncio
async def test_get_summary_success(agent_service, mock_response_data):
    """Test successful summary retrieval."""
    with mock_hashed_responses(agent_service.query, mock_response_data), \
         mock_get_json(mock_data_used):
        summary = await agent_service.get_summary()
        assert isinstance(summary, str)
        assert len(summary) > 0


@pytest.mark.asyncio
async def test_get_summary_execute_error(agent_service):
    """Test get_summary when execute() fails."""
    with mock_hashed_responses(agent_service.query, None), mock_post_json(
        None, side_effect=Exception("API Error")
    ):
        summary = await agent_service.get_summary()
        assert summary == ""


@pytest.mark.asyncio
async def test_get_agent_response_data_success(agent_service):
    """Test successful agent response data retrieval."""
    mock_summary_data = {
        "summary_text": "Test summary",
        "query": "test query",
        "data_url": "http://example.com/data"
    }
    mock_data = ["test data"]
    with mock_get_json(mock_summary_data) as mock_summary_get, \
         mock_get_json(mock_data) as mock_data_get:
        response = await agent_service.get_agent_response_data()
        assert isinstance(response, AgentResponse)
        assert response.response == "Test summary"
        assert response.context.query == "test query"
        assert response.context.tool_data.data_url == "http://example.com/data"
        assert response.context.tool_data.data_used == ["test data"]
        mock_summary_get.assert_called_once()
        mock_data_get.assert_called_once_with("http://example.com/data")


@pytest.mark.asyncio
async def test_get_agent_response_data_error(agent_service):
    """Test agent response data retrieval when API call fails."""
    with mock_get_json(None, side_effect=Exception("API Error")):
        response = await agent_service.get_agent_response_data()
        assert response is None


@pytest.mark.asyncio
async def test_generate_summary(agent_service):
    """Test summary generation with streaming."""
    with patch("services.agent.get_stream_events", new_callable=AsyncMock) as mock_stream:
        mock_stream.return_value = ["event1", "event2"]
        stream = await agent_service.generate_summary()
        assert stream == ["event1", "event2"]
        mock_stream.assert_called_once_with(
            f"{agent_service.agent_api_url}/conversations/"
            f"{agent_service.conversation_id}/messages/{agent_service.message_id}/summary",
            params={"query": agent_service.query}
        )


@pytest.mark.asyncio
async def test_get_gemini_completion():
    """Test Gemini model completion."""
    test_prompt = "Test prompt"
    expected_response = "Test response"
    with patch("google.generativeai.GenerativeModel") as mock_model:
        mock_instance = mock_model.return_value
        mock_instance.generate_content_async.return_value.text = expected_response
        response = await get_gemini_completion(test_prompt)
        assert response == expected_response
        mock_model.assert_called_once_with("gemini-1.5-flash")
        mock_instance.generate_content_async.assert_called_once_with(test_prompt)


@pytest.mark.asyncio
async def test_get_gemini_completion_error():
    """Test Gemini model completion when API call fails."""
    with patch("google.generativeai.GenerativeModel") as mock_model:
        mock_instance = mock_model.return_value
        mock_instance.generate_content_async.side_effect = Exception("API Error")
        response = await get_gemini_completion("Test prompt")
        assert response == ""
