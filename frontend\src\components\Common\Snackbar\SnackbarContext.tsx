import React, { createContext, useState, useCallback, useContext } from 'react';
import CustomSnackbar from './CustomSnackbar';

interface SnackbarContextValue {
    openSnackbar: (message: string) => void;
    closeSnackbar: () => void;
    snackbarOpen: boolean;
    snackbarMessage: string;
}

const SnackbarContext = createContext<SnackbarContextValue | undefined>(undefined);

export const useSnackbar = () => {
    const context = useContext(SnackbarContext);
    if (!context) {
        throw new Error('useSnackbar must be used within a SnackbarProvider');
    }
    return context;
};

interface SnackbarProviderProps {
    children: React.ReactNode;
}

export const SnackbarProvider: React.FC<SnackbarProviderProps> = ({ children }) => {
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');

    const openSnackbar = useCallback((message: string) => {
        setSnackbarMessage(message);
        setSnackbarOpen(true);
    }, []);

    const closeSnackbar = useCallback(() => {
        setSnackbarOpen(false);
        setSnackbarMessage('');
    }, []);

    const value: SnackbarContextValue = {
        openSnackbar,
        closeSnackbar,
        snackbarOpen,
        snackbarMessage,
    };

    return (
        <SnackbarContext.Provider value={value}>
            {children}
            <CustomSnackbar
                open={snackbarOpen}
                message={snackbarMessage}
                onClose={closeSnackbar}
            />
        </SnackbarContext.Provider>
    );
};