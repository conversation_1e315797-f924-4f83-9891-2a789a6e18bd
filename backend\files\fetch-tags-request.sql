WITH RankedTags AS (
    SELECT
        id, tag_full_name, tag_label, taxonomy_type, taxonomy_level, request, tag_count,
        ROW_NUMBER() OVER (PARTITION BY tag_label ORDER BY tag_count DESC) AS rn
    FROM Taxonomy
    WHERE taxonomy_type = ':tag_type'
)
SELECT
    id, tag_full_name, tag_label, taxonomy_type, taxonomy_level, request, tag_count
FROM RankedTags
WHERE rn = 1
ORDER BY tag_count DESC
LIMIT 25;
