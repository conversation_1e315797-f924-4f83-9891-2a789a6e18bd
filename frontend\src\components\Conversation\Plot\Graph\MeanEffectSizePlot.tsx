import React, { useState, useEffect, useRef } from "react";
import * as d3 from "d3";
import { Box } from "@mui/material";

interface MeanEffectSizePlotProps {
  data: any;
  xDomain: any;
}

const MeanEffectSizePlot: React.FC<MeanEffectSizePlotProps> = ({
  data,
  xDomain,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [svgWidth, setSvgWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  const margin = {
    top: 0,
    right: 20,
    bottom: 0,
    left: 20,
  };

  const height = 40;
  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;
  const h: number = height - margin.top - margin.bottom;

  const meanRange = [
    d3.mean(data[1], (d) => d.standardized_ci_lower),
    d3.mean(data[1], (d) => d.standardized_ci_upper),
  ];
  const meanEffectSize = meanRange[0] + (meanRange[1] - meanRange[0]) / 2;
  const xScale = d3
    .scaleLinear()
    .domain(xDomain)
    .range([0, w])
    .nice();

  const maxUpperLower = d3.max(data[1], (d) =>
    Math.max(Math.abs(d.hedges_d), Math.abs(d.hedges_d))
  );

  const cScale = d3
    .scaleQuantize()
    .domain([-maxUpperLower, maxUpperLower])
    .range([
      "#67001f",
      "#b2182b",
      "#d6604d",
      "#f4a582",
      "#fddbc7",
      "#f7f7f7",
      "#d1e5f0",
      "#92c5de",
      "#4393c3",
      "#2166ac",
      "#053061",
    ]);

  return (
    <Box className="funnel-plot-box" ref={containerRef}>
      <svg
        width={svgWidth}
        height={height + margin.bottom + margin.top}
        style={{ background: "rgba(245, 249, 254, 1)" }}
      >
        {w > 0 && (
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            {data[1].length > 1 && (
              <g
                className="mean-effect-size"
                transform={`translate(0, ${height / 2})`}
              >
                <line
                  x1={xScale(meanRange[0])}
                  y1={-4}
                  x2={xScale(meanRange[0])}
                  y2={4}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(meanRange[1])}
                  y1={-4}
                  x2={xScale(meanRange[1])}
                  y2={4}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(meanRange[0])}
                  x2={xScale(meanRange[1])}
                  stroke="rgb(22 54 97)"
                />
                <rect
                  x={xScale(meanEffectSize) - 6}
                  y={-6}
                  width={12}
                  height={12}
                  fill={`${cScale(meanEffectSize)}`}
                  stroke="rgb(22 54 97)"
                  strokeWidth={2}
                  transform={`rotate(-45 ${xScale(meanEffectSize)} 0)`}
                />
                <text
                  x={xScale(meanRange[1])}
                  dx={8}
                  dy={0}
                  style={{
                    fontSize: 12,
                    textAnchor: "start",
                    fontWeight: "normal",
                    dominantBaseline: "central",
                    fill: "rgb(22 54 97)",
                    stroke: "rgb(245, 249, 254)",
                    strokeWidth: 4,
                    paintOrder: "stroke fill",
                  }}
                >
                  Average Effect
                </text>
              </g>
            )}
            <line
              x1={xScale(0)}
              x2={xScale(0)}
              y2={h + 200}
              stroke="black"
              strokeDasharray="2 2"
            />
          </g>
        )}
      </svg>
    </Box>
  );
};

export default MeanEffectSizePlot;