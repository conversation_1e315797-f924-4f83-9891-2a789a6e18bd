import { Box, Skeleton } from '@mui/material';

interface PlaceholderProps {
  type: 'code' | 'table' | 'text';
  sx?: object;
}

const PlaceholderComponent = ({ type, sx }: PlaceholderProps) => {
  const renderTextSkeleton = () => (
    <>
      <Skeleton animation="wave" width="80%" />
      <Skeleton animation="wave" />
      <Skeleton animation="wave" width="60%" />
    </>
  );

  const renderCodeSkeleton = () => (
    <Box
      sx={{
        bgcolor: 'grey.900',
        borderRadius: 1,
        p: 2,
        height: '150px',
        width: '100%',
        ...sx,
      }}
    >
      {renderTextSkeleton()}
    </Box>
  );

  const renderTableSkeleton = () => (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        ...sx,
      }}
    >
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Skeleton animation="wave" variant="text" width="20%" />
        <Skeleton animation="wave" variant="text" width="30%" />
        <Skeleton animation="wave" variant="text" width="40%" />
      </Box>
      <Skeleton animation="wave" height={25} />
      <Skeleton animation="wave" height={25} />
      <Skeleton animation="wave" height={25} />
    </Box>
  );

  switch (type) {
    case 'code':
      return renderCodeSkeleton();
    case 'table':
      return renderTableSkeleton();
    case 'text':
      return <Box sx={{ width: '100%', ...sx }}>{renderTextSkeleton()}</Box>;
    default:
      return null;
  }
};

export default PlaceholderComponent;