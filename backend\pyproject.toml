[tool.poetry]
name = "backend"
version = "0.1.0"
description = "FastAPI backend service for ImpactAI platform"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "0.110.0"
uvicorn = {extras = ["standard"], version = "0.27.1"}
redis = "5.0.2"
requests = "^2.32.4"
sqlalchemy = "2.0.28"
pymysql = "1.1.1"
cryptography = "44.0.1"
pandas = "2.2.1"
dataclasses-json = "0.6.4"
jinja2 = "3.1.6"
databases = "0.9.0"
aiomysql = "0.2.0"
structlog = "24.4.0"
openai = "1.55.3"
google-generativeai = "*"
pyjwt = "2.10.1"
httpx = "0.28.1"
bcrypt = "4.3.0"
python-dotenv = "^1.1.1"
alembic = "^1.16.4"

[tool.poetry.group.dev.dependencies]
pytest-asyncio = "0.21.0"
pytest = "^7.0"


[tool.pytest.ini_options]
pythonpath = ["backend"]
testpaths = ["backend"]
python_files = ["*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
dev = ["pytest", "pytest-asyncio"] 