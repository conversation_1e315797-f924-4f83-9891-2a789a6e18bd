# Backend Service

The backend service is built with FastAPI and provides the core API functionality for the ImpactAI platform.

## Architecture

### Main Components

- **FastAPI Application**: Core API server with middleware and routers
- **Database Models**: SQLAlchemy models for data persistence
- **Authentication**: JWT-based authentication system
- **WebSocket Support**: Real-time communication for chat features
- **Redis Cache**: Performance optimization through caching
- **OpenTelemetry**: Integrated monitoring and tracing

### Database Configuration

The backend uses optimized database connection pooling for high performance and reliability:

- **Connection Pool Size**: 20 concurrent connections
- **Max Overflow**: 20 additional connections during peak load
- **Pool Pre-ping**: Enabled for connection health validation
- **Connection Timeout**: 10 seconds for new connections
- **Pool Recycle**: 1800 seconds (30 minutes) for connection refresh
- **Compiled Cache**: Enabled for query performance optimization

#### Database Sessions

The application uses context-managed database sessions with improved error handling:

```python
async with get_core_db_session() as session:
    # Database operations with automatic cleanup
    result = await session.execute(query)
```

Features:
- Automatic session cleanup and connection pooling
- Robust error handling with rollback capability
- Null-safe session management
- Comprehensive logging for monitoring

### Text Processing and Sanitization

The backend includes a sophisticated text processing engine that ensures clean, contextual research summaries:

#### Core Sanitization Features
- **Number Formatting**: Removes parentheses around numerical values for cleaner presentation
- **Bracket Cleaning**: Removes incomplete or malformed reference brackets
- **Invalid Reference Removal**: Filters out references marked as "ALL" or other invalid markers

#### Plot Reference Management
- **Data-Driven Filtering**: Intervention-outcome pairs are validated against available research data
- **Contextual Removal**: When no supporting data exists, all plot references are automatically removed
- **Selective Filtering**: Only maintains plot references that have corresponding data points

#### Source Reference Filtering
- **Validated Source Citations**: Source references like `[A123]`, `[P456]` are validated against available paper data
- **Automatic Cleanup**: Non-matching source references are removed to maintain citation integrity
- **Data-Driven Validation**: Only sources with corresponding `paper_combined_id` in the dataset are retained
- **Context-Aware Processing**: When no source data is available, all source references are removed

#### Agent Response Integration
The `AgentResponse.sanitized_summary()` method applies all text processing layers:
1. **Plot text sanitization** - Cleans formatting and removes invalid markers
2. **Plot pair filtering** - Validates intervention-outcome references against data
3. **Source filtering** - Validates source citations against available papers
4. **Contextual processing** - Removes all invalid references when no data supports them

#### Research Integrity
- **Evidence-Based Citations**: Only citations with supporting data are maintained
- **Clean Presentation**: Automated removal of broken or unsupported references
- **Consistent Formatting**: Standardized text presentation across all responses
- **Data Validation**: Multi-layer validation ensures all references are backed by actual research data

### API Routes

Most uptodate list of endpoints can be found after you run the app `/docs` endpoint.

1. **Authentication**

   - `/auth/login` - User login
   - `/auth/register` - User registration
   - `/auth/forgot-password` - Password recovery
   - `/auth/change-password` - Password change

2. **Conversations**

   - `/conversations` - Manage chat conversations
   - `/conversations/{conversation_id}/messages/{message_id}` - Get message with enhanced source data
   - `/conversations/{conversation_id}/messages/{message_id}/sources` - Get detailed source information
   - WebSocket endpoint for real-time messaging

3. **Search**

   - `/search/chips` - Search chips based on database taxonomy (interventions and outcomes)

4. **Waitlist**
   - `/waitlist` - Manage user waitlist

#### API Response Format

**Search Chips**: The `/search/chips` endpoint returns intervention and outcome suggestions based on database taxonomy.

```json
{
  "success": true,
  "data": {
    "entries": [
      {
        "type": "intervention",
        "value": "Effectiveness of insurance programs",
        "label": "insurance programs"
      },
      {
        "type": "outcome",
        "value": "Improving air quality",
        "label": "air quality"
      }
    ]
  }
}
```

**Paper Sources with Abstracts**: Conversation endpoints return complete paper abstracts, providing rich context for research citations.

Response format for sources:
```json
{
  "sources": [
    {
      "id": "unique-source-id",
      "paper_id": "123",
      "title": "Paper Title",
      "citation": "Full citation text",
      "abstract": "Actual paper abstract text",
      "journal_name": "Journal Name",
      "country": "Country of study",
      "region": "Geographic region",
      "quality_score": 0.75
    }
  ]
}
```

### Middleware

- CORS Support
- Security Headers
- Authentication Token Handling
- Rate Limiting (configurable)

## Setup

### Requirements

```txt
fastapi==0.110.0
uvicorn[standard]==0.27.1
redis==5.0.2
sqlalchemy==2.0.28
pymysql==1.1.1
databases==0.9.0
aiomysql==0.2.0
structlog==24.4.0
```

### Environment Variables

Required environment variables:

- Database configuration
- Redis settings
- JWT secret
- API keys for external services

## Database Migration Management

The backend uses Alembic for database schema management and provides convenient Make commands for common database operations.

### Prerequisites

Ensure your environment variables are properly configured:

```bash
MYSQL_HOST=your_host
MYSQL_DATABASE=your_database_name
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
```

### Available Commands

#### Development Workflow

```bash
# Check current migration status
make db-current

# View migration history
make db-history

# Create a new migration after model changes
make db-create
# (Will prompt for migration message)

# Apply pending migrations
make db-migrate
```

#### Migration Commands Reference

| Command | Description | Usage |
|---------|-------------|--------|
| `make db-current` | Show current migration status | `make db-current` |
| `make db-history` | Show detailed migration history | `make db-history` |
| `make db-create` | Create new migration from model changes | `make db-create` |
| `make db-migrate` | Apply all pending migrations | `make db-migrate` |
| `make db-downgrade` | Downgrade to previous migration | `make db-downgrade` |
| `make db-downgrade-to` | Downgrade to specific revision | `make db-downgrade-to REV=abc123` |
| `make db-reset` | Reset database (with confirmation) | `make db-reset` |

#### Typical Development Workflow

1. **Check current state**:
   ```bash
   make db-current
   ```

2. **Make changes to models** in `database/models.py`

3. **Create migration**:
   ```bash
   make db-create
   # Enter description: "Add user profile fields"
   ```

4. **Review generated migration** in `alembic/versions/`

5. **Apply migration**:
   ```bash
   make db-migrate
   ```

6. **Verify changes**:
   ```bash
   make db-current
   make db-history
   ```

#### Safety Features

- **Confirmation prompts**: Destructive operations like `db-reset` require confirmation
- **Error handling**: Commands will fail gracefully with clear error messages
- **Poetry integration**: All commands run through Poetry for consistent dependency management
- **Verbose output**: Migration history includes detailed information about each migration

#### Troubleshooting

**Common Issues:**

1. **Migration conflicts**: If you have conflicting migrations, use `make db-history` to identify issues
2. **Failed migrations**: Check database logs and use `make db-downgrade` to rollback if needed
3. **Model/DB sync issues**: Use `make db-current` to verify your current state matches expectations
4. **Foreign key constraint errors**: If Alembic can't drop indexes due to FK constraints, delete the problematic migration and create an empty sync migration instead

**Recovery Commands:**

```bash
# Rollback one migration
make db-downgrade

# Rollback to specific revision
make db-downgrade-to REV=72aa0c5ad725

# Nuclear option: Reset everything (BE CAREFUL!)
make db-reset
```

#### Database Schema

The application includes models for:
- **User Management**: `User`, `Conversation`, `Message`
- **Content**: `Plot`, `Source`, `Feedback`
- **Research Data**: `Paper`, `Intervention`, `Outcome`, `Estimate`
- **Administration**: `Waitlist`

All models include:
- Automatic UUID generation for primary keys
- Timestamp tracking (`created_at`, `updated_at`, `deleted_at`)
- Proper foreign key relationships
- Optimized indexing for performance

#### Additional Resources

- 📋 **[Database Migration Quick Reference](docs/database-migrations.md)** - Comprehensive cheat sheet for all database commands
- 🔧 **[Alembic Official Documentation](https://alembic.sqlalchemy.org/)** - Complete Alembic reference

## Features

### Security

- JWT authentication
- Password hashing with bcrypt
- Security headers middleware
- Rate limiting capability

### Search Functionality

- **Database-Driven Chips**: Search suggestions sourced from real taxonomy database
- **External Service Integration**: Connects to chips service for up-to-date taxonomy data
- **Smart Labeling**: Uses concise taxonomy labels with fallback to question text
- **Frontend Ready**: Structured response format optimized for UI consumption
- **Error Handling**: Robust error handling with graceful degradation

### Data Management

- **Database Performance**: Optimized connection pooling with 20 concurrent connections
- **Paper Abstract Retrieval**: Direct fetching of paper abstracts from database
- Redis caching for fast response times
- Structured logging with comprehensive error tracking
- Robust error handling with automatic rollback

### Real-time Features

- WebSocket support for live chat
- Conversation management with comprehensive source data
- Message feedback system with detailed paper citations

### Monitoring

- OpenTelemetry integration
- Health check endpoint
- Structured logging with session lifecycle tracking
- Performance metrics and connection pool monitoring

## Features Overview

### Database Capabilities

- **Connection Pool**: 20 concurrent connections for high concurrency
- **Connection Health Monitoring**: Automatic detection of stale connections
- **Query Performance**: Compiled cache for fast query execution
- **Connection Reliability**: Robust timeout and retry mechanisms

### Research Data Features

- **Paper Abstracts**: Complete abstracts retrieved and included in API responses
- **Rich Citations**: Comprehensive source data with detailed paper metadata
- **Research Context**: Informative research context for development practitioners

## API Documentation

The API documentation is available at `/docs` when running the server, providing interactive documentation for all endpoints.

### Testing

Test coverage includes:
- Database session lifecycle testing
- Connection pool configuration validation
- Paper abstract retrieval functionality
- Error handling and rollback scenarios
- Search chips endpoint functionality (14 comprehensive tests)
- External service integration testing
- Mock data validation and edge case handling
