import React from "react";
import { useInView } from "react-intersection-observer";
import { Box } from "@mui/material";
import Logo from "../Common/Logo/Logo";

interface LazyBackgroundBoxProps {
    image: string;
    altText: string;
}

const LazyBackgroundBox: React.FC<LazyBackgroundBoxProps> = ({ image, altText }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        rootMargin: "200px 0px",
    });

    return (
        <Box
            ref={ref}
            sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                width: "100%",
                backgroundImage: inView ? `url("${image}")` : "none",
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: 'no-repeat'
            }}
        >
            <Logo width={200} height={46} logoSrc="" altText={altText} color="white" />
        </Box>
    );
};

export default LazyBackgroundBox;