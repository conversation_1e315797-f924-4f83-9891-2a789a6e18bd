**Appendix C: Model choice**

In synthesizing evidence, the goal is to combine the effect sizes of various studies retrieved based on a user’s query into one single, average effect. The method the model uses to arrive at this pooled effect depends greatly on specific preconditions and model specifications. By default, ImpactAI will use fixed-effects and random-effects meta-analytic pooling.

**Key components for model choice**

The below components can be understood as the common ingredients needed to calculate various model choices for reconciling between-study differences:

* **Inverse-variance weighting:** Assigns weights to studies based on the inverse of their effect size variances, prioritizing studies with greater precision. Used for all three models.
* **Tau-squared:** Estimates the between-study variance in a random-effects and mixed-effects model, quantifying the heterogeneity in true effect size beyond sampling error
* **Moderator variable (Quality score):** A composite quality score that assigns greater weight to higher-quality studies as determined by indices assessing methodological and statistical robustness. Relevant to the mixed-effects model.

**Calculating Variance using Standard error and Hedges’ g**

Variance calculation plays a key role in determining the weights assigned to individual studies when pooling effect sizes. The formulas and methodologies for calculating the variance of Hedges’ g are well-documented in statistical literature.

***Calculating variance using standard error***
The variance for a given study is:

`Var(g) = SE^2`

***Calculating variance using sample sizes***
In cases where the standard error is not directly extracted, the variance can be calculated using the sample sizes of the treatment (n1) and control (n2) groups:

`Var(g) = (n1 + n2) / (n1 * n2) + g^2 / (2 * (n1 + n2))`

If only the total sample size (N) is reported, it is standard practice to assume equal allocation between groups (n1 \= n2 \= N/2).

# **Random-effects model**

The first model accessible by users will be random-effects which offers a basic standardization approach. Under the random-effects model, there are two sources of study error variance that are addressed, within- and between-study differences. This widely used approach addresses the extent to which effect sizes vary within a given set of interventions and measured outcomes. For example, it could be the case that the average effect of an intervention is small, even though there are studies that evaluate said intervention and report very high effect sizes. The magnitude of this heterogeneity can be quantified as Tau-squared, which is the variance of the “true” effect sizes across a set of studies. While there are multiple ways to calculate tau-squared, our team uses one of the most commonly used estimators researchers use: DerSimonian and Laird (DL) ([Wang, 2022](https://scholarworks.wmich.edu/dissertations/3926/?utm_source=scholarworks.wmich.edu%2Fdissertations%2F3926&utm_medium=PDF&utm_campaign=PDFCoverPages)). The formula is:

`T^2_DL = (Q - (k - 1)) / (sum(wi) - (sum(wi^2) / sum(wi)))`

Where Q is derived from Cochran’s test, k is the number of studies, wi is the weight for study i, which equals the inverse of the within study variance.

Cochran’s Q is a statistical test used in meta-analysis used to detect heterogeneity. It determines whether the observed variability in effect sizes across studies is greater than expected by chance using study weights ([Cochran, 1954](https://www.jstor.org/stable/3001666?origin=crossref)).

`Q = sum over k[ wi * (xi - xbar_w)^2 ]`

Where k is the number of studies, xi is the effect size for study i, wi is the inverse-variance weight, and xbar\_w is the pooled weighted effect size ([Wang, 2022](https://scholarworks.wmich.edu/dissertations/3926/?utm_source=scholarworks.wmich.edu%2Fdissertations%2F3926&utm_medium=PDF&utm_campaign=PDFCoverPages)).

Now that the between-study variance can be used as part of a study’s weight, the formula becomes:

`wi = 1 / (Var(gi) + T^2)`

Once we have assigned a weight for each study, the average effect (M) is given by the weighted mean across all the studies. This is calculated as:

`M = sum over k(wi * xi) / sum over k(wi)`

Where k is the number of studies, and xi is the observer effect size for the study.

# **Fixed-effects model**

Alongside the random-effects models which will be our main approach, the above methodology also enables a fixed-effects model which we can offer to users to select.

The major assumption under a fixed-effect model is that all included studies have a “true” effect size (Boresntein et al. 2011). This implies that variability in the effect sizes is driven solely by sampling error. However, this is not usually the case in practice, as interventions and samples often differ, making this assumption less realistic. Only in cases where the user can reasonably consider all the studies are similar enough and that there is a common effect should this model be employed. The goal of using this model is not to make generalizations, as one cannot ensure statistical or methodological homogeneity with other studies.

In order to calculate the overall effect of a given intervention on its outcome, we average the effect sizes, given a higher weight to studies with greater precision (smaller standard error). **Inverse-variance weighting** is a commonly used method for this purpose, where the weight of a study is inversely proportional to the variance of its effect size. This means that studies with greater precision (lower standard error or variance) have a higher contribution to the pooled effect size. *For example, if we ask Impact AI, "What is the role of Skills Training on Income?" and it returns three studies—A, B, and C—the weights must be calculated based on these three studies. If the query changes, a new set of weights will need to be calculated.*

To calculate the inverse variance:

`wi = 1 / Var(gi)`

Using this weight, computing the weighted average effect size M:

`M = sum over k(wi * xi) / sum over k(wi)`
