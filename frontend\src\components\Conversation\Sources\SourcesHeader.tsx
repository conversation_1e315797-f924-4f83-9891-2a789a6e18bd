import React from 'react';
import {
    Box,
    Typography,
    IconButton,
    useTheme,
    alpha,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import FilterListIcon from '@mui/icons-material/FilterList';

interface SourcesHeaderProps {
    isFiltered: boolean;
    filteredCount: number;
    totalCount: number;
    hideSourceCount: boolean;
    displayCloseButton: boolean;
    onShowAll: () => void;
    onClose: () => void;
    onOpenFilterPanel: () => void;
}

const SourcesHeader: React.FC<SourcesHeaderProps> = ({
    isFiltered,
    filteredCount,
    totalCount,
    hideSourceCount,
    displayCloseButton,
    onShowAll,
    onClose,
    onOpenFilterPanel,
}) => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                p: 1.5,
                background: theme.palette.common.white,
                zIndex: 1,
                flexShrink: 0,
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 0.5,
                }}
            >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconButton
                        size="small"
                        onClick={onOpenFilterPanel}
                        sx={{
                            color: theme.palette.text.secondary,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '36px',
                            height: '36px',
                            borderRadius: '50%',
                            padding: 0
                        }}
                    >
                        <FilterListIcon sx={{ width: 18, height: 18 }} />
                    </IconButton>
                    {!hideSourceCount && (
                        isFiltered ? (
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    borderRadius: '100px',
                                    background: theme.palette.common.white,
                                    border: `1px solid ${theme.palette.secondary.main}`,
                                    pl: 2,
                                    pr: 1,
                                    height: '24px',
                                    color: theme.palette.primary.main,
                                    boxShadow: 'none',
                                    gap: 0.5,
                                    width: 'max-content',
                                    maxWidth: { xs: '100%', md: '200px' },
                                }}
                            >
                                <Box
                                    component="span"
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        height: '100%',
                                        fontFamily: 'Roboto, sans-serif',
                                        fontSize: '13px',
                                        fontWeight: 400,
                                        lineHeight: '18px',
                                        letterSpacing: '0.16px',
                                    }}
                                >
                                    {`${filteredCount} of ${totalCount} sources`}
                                </Box>
                                <IconButton
                                    size="small"
                                    onClick={onShowAll}
                                    sx={{
                                        ml: 0,
                                        p: 0,
                                        color: alpha(theme.palette.primary.main, 0.7),
                                        height: '16px',
                                        width: '16px',
                                        minWidth: '16px',
                                        minHeight: '16px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}
                                    aria-label="Clear filter"
                                >
                                    <CloseIcon sx={{ fontSize: '16px' }} />
                                </IconButton>
                            </Box>
                        ) : (
                            totalCount > 0 && (
                                <Typography
                                    variant="body2"
                                    sx={{
                                        fontSize: 13,
                                        fontWeight: 400,
                                        color: theme.palette.text.secondary,
                                    }}
                                >
                                    {`${totalCount} sources`}
                                </Typography>
                            )
                        )
                    )}
                </Box>
                {displayCloseButton && (
                    <IconButton
                        id='test-kush'
                        onClick={onClose}
                        size="small"
                        sx={{
                            color: theme.palette.text.secondary,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '36px',
                            height: '36px',
                            borderRadius: '50%',
                            padding: 0
                        }}
                    >
                        <CloseIcon sx={{ width: 18, height: 18 }} />
                    </IconButton>
                )}
            </Box>
        </Box>
    );
};

export default SourcesHeader;