from sqlalchemy import Column, String, DateTime, Text, JSON, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_mixin, declarative_base
import sqlalchemy as sa
import uuid, json

Base = declarative_base()


@declarative_mixin
class TimestampMixin:
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime, nullable=True)


class User(TimestampMixin, Base):
    """Represents a user in the system."""

    __tablename__ = "users"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), nullable=True)
    password_hash = Column(String(255), nullable=True)


class Conversation(TimestampMixin, Base):
    """Represents a conversation between users."""

    __tablename__ = "conversations"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    title = Column(String(255), nullable=True)
    user_id = Column(String(255), ForeignKey("users.id"), nullable=True)

    def display_title(self):
        """Return the title of the conversation."""
        return self.title if self.title else f"Chat started on {self.created_at}."


class Message(TimestampMixin, Base):
    """Represents a message in a conversation."""

    __tablename__ = "messages"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String(255), ForeignKey("conversations.id"), nullable=True)
    text = Column(Text, nullable=True)
    author = Column(String(255), nullable=True)
    type = Column(String(255), nullable=True)
    choices = Column(JSON, nullable=True)
    query_values = Column(JSON, nullable=True)
    streaming_finished_at = Column(DateTime, nullable=True)
    summary_configs = Column(JSON, nullable=True)


class Plot(TimestampMixin, Base):
    """Represents a plot associated with a message."""

    __tablename__ = "plots"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    message_id = Column(String(255), ForeignKey("messages.id"))
    title = Column(String(255), nullable=True)
    data = Column(JSON, nullable=True)

    def data_as_json(self):
        """Convert the plot data to JSON format."""
        return self.data if isinstance(self.data, dict) else json.loads(self.data)


class Source(TimestampMixin, Base):
    """Represents a source document related to a message."""

    __tablename__ = "sources"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    message_id = Column(String(255), ForeignKey("messages.id"))
    paper_id = Column(String(255), nullable=True)
    position = Column(Integer, nullable=True)
    short_paper_id = Column(String(255), nullable=True)
    title = Column(String(255), nullable=True)
    doi_url = Column(String(255), nullable=True)
    journal_name = Column(String(255), nullable=True)
    citation = Column(JSON, nullable=True)


class Feedback(TimestampMixin, Base):
    """Represents feedback on a message."""

    __tablename__ = "feedbacks"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    message_id = Column(String(255), ForeignKey("messages.id"))
    reaction = Column(String(255), nullable=True)
    message = Column(Text, nullable=True)
    tags = Column(String(255), nullable=True)


class Waitlist(TimestampMixin, Base):
    """Represents users on waitlist."""

    __tablename__ = "waitlists"


    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), nullable=True)
    organization = Column(String(255), nullable=True)


class Estimate(TimestampMixin, Base):
    __tablename__ = "estimates"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    comparison_id = Column(Integer, nullable=True)
    outcome_id = Column(String(255), nullable=True)
    parameter = Column(String(255), nullable=True)
    cohen_d = Column(sa.Float, nullable=True)
    treatment_effect = Column(Text, nullable=True)
    hedges_d = Column(sa.Float, nullable=True)
    precision = Column(String(255), nullable=True)
    standardized_ci_lower = Column(sa.Float, nullable=True)
    standardized_ci_upper = Column(sa.Float, nullable=True)
    n_control = Column(sa.Float, nullable=True)
    n_treatment = Column(sa.Float, nullable=True)
    n_total = Column(sa.Float, nullable=True)
    effect_type = Column(Text, nullable=True)
    outcome_type = Column(Text, nullable=True)
    outcome_scale = Column(Text, nullable=True)
    regression_type = Column(Text, nullable=True)
    control_variables = Column(Text, nullable=True)

class Intervention(TimestampMixin, Base):
    __tablename__ = "interventions"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    arm_id = Column(Integer, nullable=True)
    paper_id = Column(String(255), nullable=True)
    name = Column(Text, nullable=True)
    objective = Column(Text, nullable=True)
    taxonomy_l1 = Column(String(255), nullable=True)
    scale = Column(Text, nullable=True)
    taxonomy_l2 = Column(String(255), nullable=True)
    intensity = Column(Text, nullable=True)
    taxonomy_l3 = Column(String(255), nullable=True)
    fidelity = Column(Text, nullable=True)
    taxonomy_l4 = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    taxonomy_l3_definition = Column(String(255), nullable=True)
    analysis_unit = Column(Text, nullable=True)
    taxonomy_l4_definition = Column(String(255), nullable=True)
    start_date = Column(DateTime, nullable=True)
    details = Column(Text, nullable=True)
    end_date = Column(DateTime, nullable=True)
    timeline = Column(Text, nullable=True)
    cost = Column(sa.Float, nullable=True)

class Outcome(TimestampMixin, Base):
    __tablename__ = "outcomes"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    outcome_name = Column(Text, nullable=True)
    intervention_id = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    name = Column(String(255), nullable=True)
    analysis_unit = Column(Text, nullable=True)
    taxonomy_l1 = Column(String(255), nullable=True)
    outcome_connotation = Column(Text, nullable=True)
    taxonomy_l2 = Column(String(255), nullable=True)
    outcome_type = Column(Text, nullable=True)
    taxonomy_l3 = Column(String(255), nullable=True)
    is_primary_period = Column(sa.Boolean, nullable=True)
    taxonomy_l4 = Column(String(255), nullable=True)
    data_collection_round = Column(Text, nullable=True)
    taxonomy_l3_definition = Column(String(255), nullable=True)
    taxonomy_l4_definition = Column(String(255), nullable=True)
    category = Column(String(255), nullable=True)
    statistical_type = Column(String(255), nullable=True)
    standardized = Column(String(255), nullable=True)
    standardization_description = Column(String(255), nullable=True)
    coding = Column(String(255), nullable=True)
    direction = Column(String(255), nullable=True)

class Paper(TimestampMixin, Base):
    __tablename__ = "papers"
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    combined_id = Column(String(50), nullable=True)
    title = Column(String(255), nullable=True)
    citation = Column(Text, nullable=True)
    abstract = Column(Text, nullable=True)
    country = Column(String(255), nullable=True)
    year = Column(Integer, nullable=True)
    doi_url = Column(String(255), nullable=True)
    doi = Column(Text, nullable=True)
    authors = Column(Text, nullable=True)
    volume = Column(Text, nullable=True)
    issue = Column(Text, nullable=True)
    pages = Column(Text, nullable=True)
    url = Column(Text, nullable=True)
    citation_count = Column(Integer, nullable=True)
    paper_type = Column(Text, nullable=True)
    journal_id = Column(Integer, nullable=True)
    abstract_summary = Column(Text, nullable=True)
