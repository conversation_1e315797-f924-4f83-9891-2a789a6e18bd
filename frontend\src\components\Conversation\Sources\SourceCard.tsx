import React from "react";
import {
    <PERSON>,
    Card,
    CardContent,
    Typography,
    Chip,
    IconButton,
    Link,
    useTheme,
} from "@mui/material";
import ArticleOutlinedIcon from "@mui/icons-material/ArticleOutlined";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import CloseIcon from '@mui/icons-material/Close';
import { sanitizeSourceText } from "../../../utils/SourceUtils";
import { toTitleCase, sentenceCase } from "../../../utils/text";

import { formatAuthorsMLA, generateLink, capitalizeWords } from './utils/SourceUtils';
import { Source } from "../../../types/ConversationTypes";

interface SourceCardProps {
    source: Source;
    isSelected: boolean;
    isExpanded: boolean;
    onCloseSource: (sourceId: string) => void;
    toggleSourceExpansion: (sourceId: string) => void;
    expandable?: boolean;
    alwaysExpanded?: boolean;
    hideArticleIcon?: boolean;
    maxAbstractLines?: number;
    disableSelectedBorder?: boolean;
    disableSelectedHighlight?: boolean;
    iconSpacing?: number;
    showCloseButton?: boolean;
    interventionDetails?: {
        name: string;
        description: string;
    };
    outcomeDetails?: {
        name: string;
        description: string;
    };
    hideBorder?: boolean;
}

const ICON_AREA_WIDTH = '32px';

const SourceCard: React.FC<SourceCardProps> = ({
    source,
    isSelected,
    isExpanded,
    onCloseSource,
    toggleSourceExpansion,
    expandable = false,
    alwaysExpanded = false,
    hideArticleIcon = false,
    maxAbstractLines = 3,
    disableSelectedBorder = false,
    disableSelectedHighlight = false,
    iconSpacing = 1,
    showCloseButton = false,
    interventionDetails,
    outcomeDetails,
    hideBorder = false,
}) => {
    const theme = useTheme();

    const selectedCardStyle = disableSelectedBorder ? {} : {
        borderColor: theme.palette.primary.light,
        borderWidth: 1,
    };

    const formattedAuthors = (() => {
        let authorsRaw = source.authors;
        let year = '';
        if ((!authorsRaw || !authorsRaw.trim()) && source.citation) {
            const cleanCitation = sanitizeSourceText(source.citation);
            const authorMatch = cleanCitation.match(/^(.+?)(?=\s*\(\d{4}\))/);
            if (authorMatch) {
                authorsRaw = authorMatch[1].trim();
            } else {
                const altMatch = cleanCitation.match(/^([^.;]+(?:;[^.;]+)*)(?=\.|;|$)/);
                if (altMatch) authorsRaw = altMatch[1].trim();
            }
        } else if (authorsRaw) {
            authorsRaw = sanitizeSourceText(authorsRaw);
        }

        if (source.citation) {
            const cleanCitation = sanitizeSourceText(source.citation);
            const yearMatch = cleanCitation.match(/\((\d{4})\)/);
            if (yearMatch) {
                year = yearMatch[1];
            }
        }
        let authorLine = '';
        if (authorsRaw) {
            authorsRaw = authorsRaw.replace(/^\(?\d{4}\)?[.,;:\s-]*/g, '').replace(/[.,;:\s-]*\(?\d{4}\)?$/g, '');
            const authorsNoOrcid = authorsRaw.replace(/\s*\[ORCID:[^\]]+\]/g, '');
            authorLine = formatAuthorsMLA(authorsNoOrcid);
        }

        let showYear = year;
        if (source.citation && source.citation.trim().startsWith(`(${year})`)) {
            showYear = '';
        }
        return { authorLine, showYear };
    })();

    const chips = (() => {
        const generatedChips = [];
        if (source?.country) {
            generatedChips.push(
                <Chip
                    key="country"
                    label={`Country: ${capitalizeWords(source.country)}`}
                    size="small"
                    variant="outlined"
                    sx={{
                        fontFamily: 'Roboto, sans-serif',
                        fontWeight: 500,
                        fontSize: '13px',
                        lineHeight: '14px',
                        letterSpacing: 0,
                        height: '22px',
                        borderRadius: '4px',
                        borderWidth: '1px',
                        borderColor: '#D4E4FC',
                        color: theme.palette.text.secondary,
                        px: '4px',
                        py: '0px',
                        textAlign: 'left',
                        alignItems: 'center',
                        display: 'flex',
                    }}
                />
            );
        }
        if (source?.sector) {
            let sectors = source.sector.split(';').map(s => s.trim()).filter(Boolean);
            let formatted = sectors.map(capitalizeWords).join(', ');
            generatedChips.push(
                <Chip
                    key="sector"
                    label={`Sector: ${formatted}`}
                    size="small"
                    variant="outlined"
                    sx={{
                        fontFamily: 'Roboto, sans-serif',
                        fontWeight: 500,
                        fontSize: '13px',
                        lineHeight: '14px',
                        letterSpacing: 0,
                        height: '22px',
                        borderRadius: '4px',
                        borderWidth: '1px',
                        borderColor: '#D4E4FC',
                        color: theme.palette.text.secondary,
                        px: '4px',
                        py: '0px',
                        textAlign: 'left',
                        alignItems: 'center',
                        display: 'flex',
                    }}
                />
            );
        }
        return generatedChips;
    })();

    const handleCloseClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (onCloseSource && source.short_paper_id) {
            onCloseSource(source.short_paper_id);
        }
    };

    const handleToggleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        toggleSourceExpansion(source.id);
    };

    return (
        <Card
            variant="outlined"
            sx={{
                border: hideBorder ? 'none' : `1px solid ${theme.palette.divider}`,
                bgcolor: (theme as any).common?.white?.main || '#fff',
                ...(isSelected && !disableSelectedHighlight ? selectedCardStyle : {}),
            }}
        >
            <CardContent
                sx={{
                    position: 'relative',
                    padding: '12px !important',
                    '&:last-child': { paddingBottom: '12px !important' },
                }}
            >
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: iconSpacing }}>
                    {!hideArticleIcon && (
                        <Box sx={{ flex: '0 0 auto', width: '24px', display: 'flex', alignItems: 'flex-start', justifyContent: 'center', mt: '2px' }}>
                            <ArticleOutlinedIcon sx={{ width: 20, height: 20, color: theme.palette.text.secondary }} />
                        </Box>
                    )}
                    <Box sx={{ flex: '1 1 auto', overflow: 'hidden', maxWidth: '100%' }}>
                        {source?.title && (
                            <Typography
                                variant="body2"
                                sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 500,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.4',
                                    letterSpacing: '0.14px',
                                    mb: 0.5,
                                    textAlign: 'left',
                                    alignItems: 'center',
                                    display: 'flex',
                                }}
                            >
                                {typeof source.title === 'string' ? toTitleCase(sanitizeSourceText(source.title)) : source.title}
                            </Typography>
                        )}

                        {formattedAuthors.authorLine || formattedAuthors.showYear ? (
                            <Typography
                                variant="body2"
                                sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    mb: 0.5,
                                    textAlign: 'left',
                                    alignItems: 'center',
                                    display: 'flex',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                }}
                            >
                                {formattedAuthors.authorLine}{formattedAuthors.showYear ? ` (${formattedAuthors.showYear})` : ''}
                            </Typography>
                        ) : null}
                        {source?.journal_name && (
                            <Typography
                                variant="body2"
                                sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontStyle: 'italic',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    textAlign: 'left',
                                    display: 'block',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                }}
                                title={sanitizeSourceText(source.journal_name)}
                            >
                                {sanitizeSourceText(source.journal_name)}
                            </Typography>
                        )}
                        {(source?.volume || source?.issue || source?.pages) && (
                            <Typography
                                variant="body2"
                                sx={{
                                    fontFamily: 'Roboto, sans-serif',
                                    fontWeight: 400,
                                    color: theme.palette.text.primary,
                                    fontSize: '14px',
                                    lineHeight: '1.66',
                                    letterSpacing: '0.4px',
                                    textAlign: 'left',
                                    display: 'block',
                                    maxWidth: '100%',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                }}
                                title={[
                                    source.volume ? `Vol. ${source.volume}` : null,
                                    source.issue ? `No. ${source.issue}` : null,
                                    source.pages ? `pp. ${source.pages}` : null
                                ].filter(Boolean).join(', ')}
                            >
                                {[
                                    source.volume ? `Vol. ${source.volume}` : null,
                                    source.issue ? `No. ${source.issue}` : null,
                                    source.pages ? `pp. ${source.pages}` : null
                                ].filter(Boolean).join(', ')}
                            </Typography>
                        )}
                    </Box>
                    <Box sx={{ flex: '0 0 auto', display: 'flex', alignItems: 'flex-start', gap: 2, mt: '2px' }}>
                        {source?.doi_url && (
                            <IconButton
                                component={Link}
                                href={generateLink(source.doi_url || undefined)}
                                target="_blank"
                                rel="noopener noreferrer"
                                size="small"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    padding: 0,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '36px',
                                    height: '36px',
                                    borderRadius: '50%',
                                }}
                                onClick={(e) => e.stopPropagation()}
                            >
                                <OpenInNewIcon sx={{ width: 20, height: 20 }} />
                            </IconButton>
                        )}
                        {showCloseButton && (
                            <IconButton
                                size="small"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '36px',
                                    height: '36px',
                                    borderRadius: '50%',
                                    padding: 0
                                }}
                                onClick={handleCloseClick}
                            >
                                <CloseIcon sx={{ width: 18, height: 18 }} />
                            </IconButton>
                        )}
                    </Box>
                </Box>
                {source?.abstract && (
                    <Box sx={{ mb: 1, ml: hideArticleIcon ? 0 : '32px', pr: ICON_AREA_WIDTH }}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontFamily: 'Roboto, sans-serif',
                                fontWeight: 400,
                                color: theme.palette.text.secondary,
                                fontSize: '14px',
                                lineHeight: '1.66',
                                letterSpacing: '0.4px',
                                textAlign: 'left',
                                alignItems: 'center',
                                display: '-webkit-box',
                                WebkitLineClamp: maxAbstractLines,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                            }}
                        >
                            <span style={{ fontWeight: 500, color: theme.palette.text.primary }}>Summary: </span>{sanitizeSourceText(source.abstract)}
                        </Typography>
                    </Box>
                )}
                <Box sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 0.5,
                    mb: 0.25,
                    ml: hideArticleIcon ? 0 : '32px',
                    pr: ICON_AREA_WIDTH
                }}>
                    {chips}
                </Box>
                {expandable && !alwaysExpanded && !isExpanded && (source?.intervention_name || source?.outcome_name) && (
                    <Box sx={{
                        position: 'absolute',
                        right: 12,
                        bottom: 12,
                        width: '24px',
                        height: '24px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        <IconButton
                            size="small"
                            onClick={handleToggleClick}
                            sx={{
                                color: theme.palette.text.secondary,
                                m: 0,
                                p: 0,
                                width: '24px',
                                height: '24px',
                                '&:hover': {
                                    background: 'transparent',
                                }
                            }}
                        >
                            <ExpandMoreIcon sx={{ width: 20, height: 20 }} />
                        </IconButton>
                    </Box>
                )}

                {((expandable && isExpanded) || alwaysExpanded) && (source?.intervention_name || source?.outcome_name) && (
                    <Box sx={{
                        mt: 1,
                        pt: 1,
                        position: 'relative',
                        ml: hideArticleIcon ? 0 : '32px',
                        mb: 0,
                        pb: alwaysExpanded ? 1 : 3,
                        pr: ICON_AREA_WIDTH
                    }}>
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: ICON_AREA_WIDTH,
                                height: '1px',
                                backgroundColor: theme.palette.divider
                            }}
                        />

                        {expandable && !alwaysExpanded && (
                            <Box sx={{
                                position: 'absolute',
                                right: 12,
                                bottom: 12,
                                width: '24px',
                                height: '24px',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                zIndex: 2
                            }}>
                                <IconButton
                                    size="small"
                                    onClick={handleToggleClick}
                                    sx={{
                                        color: theme.palette.text.secondary,
                                        m: 0,
                                        p: 0,
                                        width: '24px',
                                        height: '24px',
                                        '&:hover': {
                                            background: 'transparent',
                                        }
                                    }}
                                >
                                    <ExpandLessIcon sx={{ width: 20, height: 20 }} />
                                </IconButton>
                            </Box>
                        )}

                        <Box sx={{
                            position: 'relative',
                            zIndex: 1,
                            backgroundColor: (theme as any).common?.white?.main || theme.palette.background.paper
                        }}>
                            {(interventionDetails || source?.intervention_name) && (
                                <Box sx={{ mb: (outcomeDetails || source?.outcome_name) ? 2 : 0 }}>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            fontFamily: 'Roboto, sans-serif',
                                            fontWeight: 400,
                                            color: theme.palette.text.primary,
                                            fontSize: '14px',
                                            lineHeight: '1.66',
                                            letterSpacing: '0.4px',
                                            ml: 0,
                                        }}
                                    >
                                        <span style={{ fontWeight: 500, color: theme.palette.text.primary }}>Intervention Description: </span>
                                        {interventionDetails?.description || sentenceCase(source?.intervention_details || 'No description available')}
                                    </Typography>
                                </Box>
                            )}
                            {(outcomeDetails || source?.outcome_name) && (
                                <Box sx={{ mb: 0 }}>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            fontFamily: 'Roboto, sans-serif',
                                            fontWeight: 400,
                                            color: theme.palette.text.primary,
                                            fontSize: '14px',
                                            lineHeight: '1.66',
                                            letterSpacing: '0.4px',
                                            ml: 0,
                                        }}
                                    >
                                        <span style={{ fontWeight: 500, color: theme.palette.text.primary }}>Outcome Description: </span>
                                        {outcomeDetails?.description || sentenceCase(source?.outcome_details || 'No description available')}
                                    </Typography>
                                </Box>
                            )}
                        </Box>
                    </Box>
                )}
            </CardContent>
        </Card>
    );
};

export default SourceCard;