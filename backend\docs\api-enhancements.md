# API Documentation: Paper Abstract Integration

## Overview

This document describes the ImpactAI API capabilities for research source information, including full paper abstracts, comprehensive metadata, and advanced text processing for research summaries.

## API Functionality

### Paper Abstract Retrieval

#### Service: `get_paper_abstracts_by_ids()`

**Location**: `backend/services/paper.py`

**Purpose**: Efficiently retrieves paper abstracts from the database for multiple papers simultaneously.

**Function Signature**:
```python
async def get_paper_abstracts_by_ids(paper_ids: List[int]) -> List[Dict[str, Any]]
```

**Parameters**:
- `paper_ids` (List[int]): List of paper IDs to fetch abstracts for

**Returns**:
- `List[Dict[str, Any]]`: List of dictionaries containing paper ID and abstract text

**Usage Example**:
```python
from services.paper import get_paper_abstracts_by_ids

# Fetch abstracts for multiple papers
paper_ids = [123, 456, 789]
abstracts = await get_paper_abstracts_by_ids(paper_ids)

# Result format:
# [
#   {"id": 123, "abstract": "Full abstract text..."},
#   {"id": 456, "abstract": "Full abstract text..."},
#   {"id": 789, "abstract": "Full abstract text..."}
# ]
```

### Text Processing and Sanitization

The ImpactAI backend includes a comprehensive text processing engine that ensures clean, contextual research summaries with validated citations.

#### Service: `sanitized_summary()` Method

**Location**: `backend/models/agent.py` - `AgentResponse.sanitized_summary()`

**Purpose**: Provides multi-layer text processing for research summaries, including plot reference validation and source citation filtering.

**Processing Pipeline**:
```python
def sanitized_summary(self):
    # 1. Plot text sanitization
    clean_summary = sanitize_plot_text(response_text)
    
    # 2. Plot pair filtering
    pairs = extract_intervention_outcome_pairs(self.context.tool_data)
    clean_summary = remove_non_matching_plot_pairs(clean_summary, pairs, remove_all_if_empty=True)
    
    # 3. Source filtering
    sources = extract_source_ids(self.context.tool_data)
    clean_summary = remove_non_matching_sources(clean_summary, sources, remove_all_if_empty=True)
    
    return clean_summary
```

**Features**:
- **Plot Reference Validation**: Intervention-outcome pairs validated against available research data
- **Source Citation Filtering**: Source references like `[A123]`, `[P456]` validated against paper datasets
- **Contextual Processing**: Removes all invalid references when no supporting data exists
- **Multi-Layer Sanitization**: Comprehensive text cleaning for professional presentation

#### Source Filtering Implementation

**Function**: `remove_non_matching_sources()`

**Location**: `backend/utils/text.py`

**Purpose**: Validates source citations against available research papers and removes non-matching references.

**Parameters**:
- `text` (str): Text containing source references
- `source_ids` (List[str]): List of valid source IDs from available papers
- `remove_all_if_empty` (bool): Remove all sources when no data is available

**Behavior**:
- **Matching Sources**: References like `[A123]` are kept if "A123" exists in source_ids
- **Non-matching Sources**: Invalid references are removed for citation integrity
- **Empty Data Handling**: All source references removed when no data is available
- **Format Support**: Handles single source references `[A123]` and compound references `[A123,B456]`

#### Integration with Conversation Endpoints

**Endpoint**: `GET /conversations/{conversation_id}/messages/{message_id}/summary`

**Enhanced Processing**:
1. **Agent Response Generation**: Creates research summary with embedded citations
2. **Abstract Integration**: Adds full paper abstracts to source metadata
3. **Sanitized Summary Application**: Applies all text processing layers including source filtering
4. **Database Updates**: Stores cleaned summary with validated citations

**Quality Assurance**:
- **Citation Integrity**: Only validated sources appear in final summaries
- **Data Consistency**: Source references match available paper data
- **Professional Presentation**: Clean, formatted text suitable for development practitioners

## API Endpoints

### Conversation Message Sources

#### Endpoint: `GET /conversations/{conversation_id}/messages/{message_id}/sources`

**Functionality**: Returns full paper abstracts in response

**Response Format**:
```json
{
  "success": true,
  "data": {
    "sources": [
      {
        "id": "unique-source-id",
        "position": 1,
        "paper_id": "123",
        "short_paper_id": "ABC123",
        "title": "Paper Title",
        "doi_url": "https://doi.org/10.1234/example",
        "citation": "Full citation text",
        "journal_name": "Journal Name",
        "abstract": "Complete paper abstract providing context about methodology, findings, and implications...",
        "country": "Country of study",
        "region": "Geographic region",
        "income_group": "Income classification",
        "quality_score": 0.75,
        "quality_score_category": "High",
        "sector": "education;health",
        "intervention_name": "Intervention type",
        "intervention_details": "Detailed intervention description",
        "outcome_name": "Outcome measured",
        "outcome_details": "Detailed outcome description"
      }
    ]
  }
}
```

#### Endpoint: `GET /conversations/{conversation_id}/messages/{message_id}`

**Functionality**: Message responses include sources with full abstracts

**Key Features**:
- `abstract` field contains complete abstract text
- Citation metadata includes comprehensive research context
- Robust error handling for abstract retrieval

## Database Integration

### SQL Query

**File**: `backend/files/fetch-abstract-request.sql`

```sql
select id, abstract from papers where id in (:paper_ids);
```

This query efficiently retrieves abstracts for multiple papers in a single database call.

### Database Session Management

**Implementation**: Session handling in `get_core_db_session()`

**Features**:
- Robust error handling with automatic rollback
- Null-safe session management
- Comprehensive logging for monitoring
- Connection pool optimization (20 concurrent connections)

## Implementation Details

### Response Processing Flow

1. **Source Data Collection**: Extract paper IDs from conversation sources
2. **Batch Abstract Retrieval**: Use `get_paper_abstracts_by_ids()` for efficient lookup
3. **Data Merging**: Combine abstract data with existing source metadata
4. **Response Assembly**: Build enhanced response with full citation information

### Code Example

```python
# From backend/routers/conversations.py
sources_data = response.sources_data()
paper_ids = [source['paper_id'] for source in sources_data]

# Fetch abstracts efficiently
paper_id_abstracts = await get_paper_abstracts_by_ids(paper_ids)

# Create lookup dictionary
paper_id_abstract_dict = {}
for paper_id, abstract in paper_id_abstracts:
    paper_id_abstract_dict[paper_id] = abstract

# Merge with source data
sources_with_abstracts = []
for source in sources_data:
    paper_id = source['paper_id']
    if paper_id in paper_id_abstract_dict:
        if 'citation' in source:
            source['citation']['abstract'] = paper_id_abstract_dict[paper_id]['abstract']
    sources_with_abstracts.append(source)
```

## Testing

### Test Coverage

**File**: `backend/tests/services/test_paper.py`

The new paper service includes comprehensive test coverage for:
- Abstract retrieval functionality
- Error handling scenarios
- Database session management
- Response format validation

**File**: `backend/tests/utils/test_text.py`

Enhanced text processing tests cover:
- Plot pair filtering with various data scenarios
- Text sanitization rules validation
- Edge case handling for empty data
- Format standardization verification
- **Source filtering functionality** with comprehensive scenarios
- **Source reference validation** against paper datasets
- **Contextual processing** behavior with empty data

**File**: `backend/tests/models/agent_test.py`

Agent response testing includes:
- Sanitized summary generation with **multi-layer processing**
- **Source filtering integration** with plot pair filtering
- **Data-driven text filtering** for citation validation
- **Context-aware processing** for various data scenarios
- **Integration testing** between text processing layers

**File**: `agent/tests/services/agent_test.py`

Agent service testing covers:
- **Service response structure** compatibility with source filtering
- **Data availability** for source filtering functionality
- **Edge case handling** with empty data scenarios
- **Integration compatibility** with AgentResponse models

**File**: `backend/tests/routers/test_conversations.py`

Conversation endpoint testing includes:
- **End-to-end source filtering** in conversation flow
- **Integration with abstract processing** and source filtering
- **API endpoint behavior** with sanitized summaries
- **Mock verification** for sanitized summary calls
- **Complete data flow** testing from request to response

### Testing Scenarios

**Text Processing Test Cases**:
- **Empty Data Handling**: Validates behavior when no research data is available
- **Partial Data Scenarios**: Tests filtering with incomplete datasets
- **Source Validation**: Ensures only valid paper citations are preserved
- **Plot Reference Validation**: Confirms intervention-outcome pairs match available data
- **Multi-Layer Processing**: Validates integration between different processing layers

**Source Filtering Test Cases**:
- **Matching Sources**: Validates that valid source references are preserved
- **Non-matching Sources**: Confirms invalid references are removed
- **Mixed Scenarios**: Tests combinations of valid and invalid sources
- **Empty Source Lists**: Validates behavior with no available sources
- **Complex References**: Tests compound source references like `[A123,B456]`

**Agent Response Test Cases**:
- **Sanitized Summary Integration**: Tests complete text processing pipeline
- **Context-Aware Processing**: Validates behavior based on available data
- **Source and Plot Filtering**: Tests integration between different filtering layers
- **Edge Case Handling**: Validates behavior with various data scenarios

**Conversation Endpoint Test Cases**:
- **Abstract Integration**: Tests source filtering with abstract processing
- **API Response Validation**: Confirms proper response structure
- **Database Integration**: Validates stored data matches processed summaries
- **Error Handling**: Tests behavior during processing failures

### Test Quality Assurance

**Comprehensive Coverage**: 
- **27 total tests** covering source filtering functionality
- **4 test files** with integrated coverage
- **Multiple layers** of testing from unit to integration

**Mock Integration**:
- Proper mocking of database operations
- Correct async function mocking with AsyncMock
- Accurate parameter passing validation

**Edge Case Testing**:
- Empty data scenarios
- Partial data availability
- Error conditions and fallbacks
- Invalid input handling

**Integration Testing**:
- Cross-layer functionality validation
- End-to-end workflow testing
- API endpoint behavior verification

## Performance Characteristics

### Database Configuration

- **Pool Size**: 20 concurrent connections
- **Connection Health**: Pre-ping enabled for stale connection detection
- **Query Performance**: Compiled cache for fast execution speed
- **Reliability**: Robust timeout and retry mechanisms

### API Response Performance

- **Low Latency**: Batch abstract retrieval minimizes database calls
- **Rich Context**: Full abstracts provide immediate research context
- **Efficient Experience**: Researchers can assess paper relevance quickly
- **Clean Processing**: Text sanitization maintains fast response times

## Deployment Considerations

### API Compatibility

- All API endpoints maintain backward compatibility
- `abstract` field provides complete content 
- Consistent response structure across endpoints
- Enhanced text processing transparent to existing clients

### Infrastructure Requirements

- Database connection pool settings should match expected load
- Monitor connection pool metrics for optimal performance
- Test abstract retrieval performance under production load
- Validate text processing performance with large datasets

## Error Handling

### Graceful Degradation

- If abstract retrieval fails, API falls back to existing behavior
- Text processing errors don't break response generation
- Null abstracts are handled gracefully without breaking responses
- Comprehensive logging for debugging and monitoring

### Error Scenarios

- Missing papers in database
- Database connection failures
- Malformed paper ID lists
- Session timeout during abstract retrieval
- Invalid text formatting in source data
- Empty or malformed intervention-outcome pairs

All scenarios include appropriate error logging and graceful fallback behavior. 