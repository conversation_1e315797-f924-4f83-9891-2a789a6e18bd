"""Base pipeline class for agent pipelines."""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

from src.agent.config import AgentConfig
from src.tools.llm_client import LLMClient

logger = logging.getLogger(__name__)


class Pipeline(ABC):
    """Base class for all pipelines."""

    def __init__(
        self,
        name: str,
        description: str,
        intent: str,
        steps: List[str],
        arguments: List[Tuple[str, str]],
        outputs: str,
        config: Dict[str, Any] | None = None,
    ):
        """Initialize a pipeline with its metadata."""
        self.name = name
        self.description = description
        self.intent = intent
        self.steps = steps
        self.arguments = arguments
        self.outputs = outputs
        self.config = AgentConfig.from_dict(config or {})
        self.verbose = True

        # Initialize pipeline state tracking
        self.current_step = 0
        self.results = {}
        self.errors = []

        # Tracker will be set by pipeline manager
        self.tracker = None

        # Initialize LLM client if needed
        self.llm = LLMClient(
            model_name=self.config.model_name,
        )

        self._detailed_args = self._parse_arguments()

    def set_tracker(self, tracker):
        """Set the pipeline tracker."""
        self.tracker = tracker

    def _track_step_start(self, step_name: str, input_data: Dict[str, Any] = None):
        """Start tracking a step."""
        if self.tracker:
            self.tracker.start_step(step_name, input_data)

    def _track_step_end(
        self,
        success: bool = True,
        error_message: str = None,
        output_data: Dict[str, Any] = None,
    ):
        """End tracking a step."""
        if self.tracker:
            self.tracker.end_step(success, error_message, output_data)

    def _parse_arguments(self) -> List[Dict[str, Any]]:
        """Parse arguments to get detailed type information."""
        detailed_args = []

        for arg_name, arg_type_str in self.arguments:
            detailed_args.append({"name": arg_name, "type": arg_type_str})

        return detailed_args

    @abstractmethod
    async def execute(self, user_query: str, **kwargs) -> Dict[str, Any]:
        """Execute the pipeline with the given query and parameters.

        Args:
            user_query: The user's query to process
            **kwargs: Additional parameters for the pipeline

        Returns:
            Dictionary containing the pipeline results
        """
        pass

    @abstractmethod
    def get_steps(self) -> List[str]:
        """Get the list of steps/tools that this pipeline will execute.

        Returns:
            List of step names or tool names in execution order
        """
        pass

    async def handle_error(
        self, step: str, error: Exception, **kwargs
    ) -> Optional[Dict[str, Any]]:
        """Handle errors that occur during pipeline execution.

        Args:
            step: The step where the error occurred
            error: The exception that was raised
            **kwargs: Additional context for error handling

        Returns:
            Optional recovery result or None if cannot recover
        """
        error_info = {"step": step, "error": str(error), "type": type(error).__name__}
        self.errors.append(error_info)

        if self.verbose:
            logger.error(f"Error in pipeline {self.name} at step {step}: {error}")

        # Track the error
        self._track_step_end(success=False, error_message=str(error))

        # Default error handling - subclasses can override for custom recovery
        return None

    def reset(self) -> None:
        """Reset pipeline state for a new execution."""
        self.current_step = 0
        self.results = {}
        self.errors = []

    def get_status(self) -> Dict[str, Any]:
        """Get current pipeline execution status.

        Returns:
            Dictionary with pipeline status information
        """
        return {
            "name": self.name,
            "current_step": self.current_step,
            "total_steps": len(self.get_steps()),
            "has_errors": len(self.errors) > 0,
            "errors": self.errors,
            "results_keys": list(self.results.keys()),
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert pipeline to dictionary representation.

        Returns:
            Dictionary representation of the pipeline
        """
        return {
            "name": self.name,
            "description": self.description,
            "intent": self.intent,
            "steps": self.get_steps(),
            "config": (
                self.config.__dict__
                if hasattr(self.config, "__dict__")
                else self.config
            ),
        }

    def __str__(self) -> str:
        """String representation of the pipeline."""
        steps = " -> ".join(self.get_steps())
        return (
            f"Pipeline: {self.name}\n"
            f"Description: {self.description}\n"
            f"Steps: {steps}\n"
            f"Intent: {self.intent}\n"
            f"Arguments: {self._detailed_args}\n"
            f"Outputs: {self.outputs}\n"
        )
