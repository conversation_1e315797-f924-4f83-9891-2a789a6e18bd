import React, { useEffect, useCallback } from 'react';
import LinkComponent from '../../LinkComponent';
import { Source } from "../../../../../../types/ConversationTypes";

interface AnimatedTableCellProps {
    children: React.ReactNode;
    onComplete: () => void;
    messageId?: string;
    sources?: Source[];
    plotData?: any;
    onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
    onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
}

const AnimatedTableCell: React.FC<AnimatedTableCellProps> = ({
    children,
    onComplete,
    messageId,
    onViewOnPlotClicked,
    onViewOnSourceClicked,
    plotData,
}) => {
    useEffect(() => {
        onComplete();
    }, [onComplete]);

    const processChildNode = useCallback((child: React.ReactNode, index: number): React.ReactNode => {
        if (React.isValidElement(child)) {
            const { type, props } = child;

            if (type === 'a') {
                return (
                    <LinkComponent
                        key={child.key || index}
                        {...props}
                        messageId={messageId || ''}
                        onViewOnPlotClicked={onViewOnPlotClicked}
                        onViewOnSourceClicked={onViewOnSourceClicked}
                        plotData={plotData}
                    >
                        {React.Children.count(props.children) === 0 ? '[Link]' : props.children}
                    </LinkComponent>
                );
            } else if (type === 'em' || type === 'strong' || (typeof type === 'function' && (type.name === 'AnimatedEm' || type.name === 'AnimatedStrong'))) {
                return React.cloneElement(child, {
                    key: child.key || index,
                    children: React.Children.map(props.children, processChildNode)
                });
            }
            if (props?.children) {
                return React.cloneElement(child, {
                    key: child.key || index,
                    children: React.Children.map(props.children, processChildNode)
                });
            }
            return React.cloneElement(child, { key: child.key || index });
        } else if (typeof child === 'string') {
            return child;
        }
        return child;
    }, [messageId, onViewOnPlotClicked, onViewOnSourceClicked, plotData]);

    return (
        <td>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                {React.Children.map(children, processChildNode)}
            </div>
        </td>
    );
};

export default AnimatedTableCell;