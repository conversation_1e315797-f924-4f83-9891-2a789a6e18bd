import React, { useMemo, useState } from "react";
import { useTheme } from "@mui/material/styles";
import Sources from "../Sources/Sources";
import ComparisonView from "../ComparisonView/ComparisonView";
import { Source, FilterState, Message } from "../../../types/ConversationTypes";
import { extractFilterOptionsFromData, resolvePlotData, getActiveFiltersCount } from '../../../utils/Conversation/ConversationUtil';

interface RightSidePanelProps {
    openRightSection: "sources" | "charts" | null;
    activeInfoMessageIdForPanel: string | null;
    handleCloseRightSection: () => void;
    panelSources: Source[];
    selectedStudy: string;
    activeSourcePaperIds: string[];
    activeSourceMessageId: string | null;
    setActiveSourcePaperIds: (ids: string[]) => void;
    setActiveSourceMessageId: (id: string | null) => void;
    handleFiltersChange: (messageId: string, filters: FilterState, type: 'sources' | 'plot') => void;
    handleResetFilters: (messageId: string, type: 'sources' | 'plot') => void;
    activePlotDetails: any;
    onSelectStudy: (study: string) => void;
    initialConversationMessages: Message[];
}

export const RightSidePanel: React.FC<RightSidePanelProps> = ({
    openRightSection,
    activeInfoMessageIdForPanel,
    handleCloseRightSection,
    panelSources,
    selectedStudy,
    activeSourcePaperIds,
    activeSourceMessageId,
    setActiveSourcePaperIds,
    setActiveSourceMessageId,
    handleFiltersChange,
    handleResetFilters,
    activePlotDetails,
    onSelectStudy,
    initialConversationMessages
}) => {
    const theme = useTheme();
    const [activeFilterTab, setActiveFilterTab] = useState<'General' | 'Intervention' | 'Outcome'>('General');

    const { activeFilters, filterOptions, activeFiltersCount } = useMemo(() => {
        const activeMessage = initialConversationMessages.find(msg => msg.id === activeInfoMessageIdForPanel);
        const filters = openRightSection === 'sources' ? activeMessage?.filters_sources : activeMessage?.filters_plot;
        const options = activeMessage?.plot ? extractFilterOptionsFromData(resolvePlotData(activeMessage.plot)) : {};
        const count = getActiveFiltersCount(filters, options);
        return { activeFilters: filters, filterOptions: options, activeFiltersCount: count };
    }, [initialConversationMessages, activeInfoMessageIdForPanel, openRightSection]);

    const handleFiltersChangeWrapper = (filters: FilterState) => {
        if (activeInfoMessageIdForPanel && openRightSection) {
            handleFiltersChange(activeInfoMessageIdForPanel, filters, openRightSection);
        }
    };

    const handleResetFiltersWrapper = () => {
        if (activeInfoMessageIdForPanel && openRightSection && filterOptions) {
            const minYear = filterOptions?.years ? filterOptions.years[0] : undefined;
            const maxYear = filterOptions?.years ? filterOptions.years[filterOptions.years.length - 1] : undefined;
            const defaultFilters: FilterState = {
                years: (minYear !== undefined && maxYear !== undefined) ? [minYear, maxYear] : undefined,
                sectors: [],
                countries: [],
            };
            handleFiltersChange(activeInfoMessageIdForPanel, defaultFilters, openRightSection);
        }
    };

    if (openRightSection === "sources") {
        return (
            <Sources
                key={`sources-panel-${activeInfoMessageIdForPanel}`}
                onClose={handleCloseRightSection}
                sources={panelSources}
                messageId={activeInfoMessageIdForPanel}
                selectedStudy={selectedStudy}
                activeSourcePaperIds={activeSourcePaperIds}
                activeSourceMessageId={activeSourceMessageId}
                displayCloseButton={true}
                onClearFilter={() => {
                    setActiveSourcePaperIds([]);
                    setActiveSourceMessageId(null);
                }}
                onFiltersChange={handleFiltersChangeWrapper}
                onResetFilters={handleResetFiltersWrapper}
                filterOptions={filterOptions}
                activeFilters={activeFilters}
                isParentLoading={false}
                mode='standalone'
                activeFiltersCount={activeFiltersCount}
                activeFilterTab={activeFilterTab}
                onFilterTabChange={setActiveFilterTab}
            />
        );
    } else if (openRightSection === "charts") {
        return (
            <ComparisonView
                key={`plot-section-right-${activeInfoMessageIdForPanel}`}
                informationId={activeInfoMessageIdForPanel}
                sources={panelSources}
                theme={theme}
                onClose={handleCloseRightSection}
                activePlotDetails={activePlotDetails}
                activeSourcePaperIds={activeSourcePaperIds}
                activeSourceMessageId={activeSourceMessageId}
                selectedStudy={selectedStudy}
                onSelectStudy={onSelectStudy}
                onFiltersChange={handleFiltersChangeWrapper}
                onResetFilters={handleResetFiltersWrapper}
                filterOptions={filterOptions}
                activeFilters={activeFilters}
                activeFilterTab={activeFilterTab}
                onFilterTabChange={setActiveFilterTab}
            />
        );
    }
    return null;
};