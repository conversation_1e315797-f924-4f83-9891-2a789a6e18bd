import React, { useRef, useState, useEffect } from "react";
import * as d3 from "d3";
import { Pair } from "./ForestPlotJWT.types";
import "./StudyForestPlot.css";

interface StudyForestPlotProps {
  pair: Pair;
}

const StudyForestPlot = ({ pair }: StudyForestPlotProps) => {
  const [svgWidth, setSvgWidth] = useState(0);
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const titleWidth = 160;
        setSvgWidth(entry.contentRect.width - titleWidth);
      }
    });

    resizeObserver.observe(chartRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  const extent = d3.extent(
    Object.values(pair.data).map((d) => [d.score.upper, d.score.lower]).flat()
  );

  const maxValue = Math.max(Math.abs(extent[0]), Math.abs(extent[1]));

  const x = d3
    .scaleLinear()
    .domain([-maxValue, maxValue])
    .range([0, svgWidth])
    .nice();

  const height = 24;

  const scaleColor2 = d3.scaleLinear(
    [-1, -0.5, 0, 0.5, 1],
    ["#4575b4", "#91bfdb", "#FFFFFF", "#91bfdb", "#4575b4"]
  ).clamp(true);

  return (
    <div ref={chartRef}>
      <header>
        <div className="small">Intervention:</div>
        <h4>{pair.intervention}</h4>
        <div className="small">Outcome:</div>
        <h4>{pair.outcome}</h4>
      </header>
      {Object.values(pair.data)
        .sort((a, b) => b.score.value - a.score.value)
        .map((d, index) => (
          <div key={index} className="row">
            <div className="title">{d.label}</div>
            <div className="chart">
              <svg
                className="plot"
                height={height}
                width={svgWidth}
              >
                {svgWidth > 0 && (
                  <g>
                    <rect
                      width="100%"
                      height={height}
                      fill={scaleColor2(d.score.value)}
                    />
                    <rect
                      x={x(d.score.lower)}
                      width={x(d.score.upper) - x(d.score.lower)}
                      y={height / 4}
                      height={height / 2}
                      fill="rgb(0 34 68 / 16%)"
                    />
                    {x.ticks(5).map((tick) => (
                      <g>
                        <line
                          x1={x(tick)}
                          x2={x(tick)}
                          y2={height}
                          stroke="rgba(0, 34, 68, 0.1)"
                        />
                      </g>
                    ))}
                    <line
                      x1={x(0)}
                      y1={0}
                      x2={x(0)}
                      y2={24}
                      stroke="black"
                      strokeDasharray={1.5}
                    />
                    <circle cx={x(d.score.value)} cy={12} r={4} fill="black" />
                  </g>
                )}
              </svg>
            </div>
          </div>
        ))}
      <svg style={{ marginLeft: 161 }} width={svgWidth} height={height}>
        <g>
          {x.ticks(5).map((tick) => (
            <g>
              <line
                x1={x(tick)}
                x2={x(tick)}
                y2={8}
                stroke="rgba(0, 34, 68, 0.1)"
              />
              <text
                x={x(tick)}
                y={20}
                textAnchor="middle"
                style={{ fontSize: 11 }}
              >
                {tick}
              </text>
            </g>
          ))}
          <line
            x1={x(0)}
            y1={0}
            x2={x(0)}
            y2={8}
            stroke="black"
            strokeDasharray={1.5}
          />
        </g>
      </svg>
    </div>
  );
};

export default StudyForestPlot;
