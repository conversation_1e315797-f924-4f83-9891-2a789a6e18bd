import logging
import os
from typing import Dict, Any, List, Tuple

from src.pipelines.base import Pipeline
from src.pipelines.error_handling import <PERSON>pelineErrorHandler
from src.pipelines.outputs import create_pipeline_response
from src.tools.manager import ToolManager
from src.utils.flags import get_use_new_da_flag

logger = logging.getLogger(__name__)


USE_NEW_DA_FLAG = get_use_new_da_flag()
SKIP_SQL_GENERATION = os.environ.get("SKIP_SQL_GENERATION", "false").lower() == "true"

if SKIP_SQL_GENERATION and not USE_NEW_DA_FLAG:
    logger.warning(
        "SKIP_SQL_GENERATION is set to true, but USE_NEW_DA_FLAG is false. "
        "The old DA needs SQL generation to work properly. "
        "Overriding SKIP_SQL_GENERATION to false."
    )
    SKIP_SQL_GENERATION = False

logger.info("Using new DA flag: %s", USE_NEW_DA_FLAG)


class InScopePipeline(Pipeline):
    """Pipeline for generic in-scope queries"""

    def __init__(
        self,
        tool_manager: ToolManager,
        config: Dict[str, Any] | None = None,
        name: str = "inscope_pipeline",
        description: str = "Pipeline for generic in-scope queries",
        intent: str = "inscope",
        steps: List[str] = None,
        arguments: List[Tuple[str, str]] = None,
        outputs: str = "FinalAnswer(text: str, sources: List[str], metadata: Dict[str, Any])",
    ):
        """Initialize the impact pipeline."""
        if steps is None:
            steps = [
                "entity_extractor",
                "sql_generator",
                (
                    "structured_data_organizer_v2"
                    if USE_NEW_DA_FLAG
                    else "structured_data_organizer"
                ),
                "rag_search",
                "final_answer_generator",
            ]
        if arguments is None:
            arguments = [("user_query", "str")]

        super().__init__(
            name=name,
            description=description,
            intent=intent,
            steps=steps,
            arguments=arguments,
            outputs=outputs,
            config=config,
        )
        self.tool_manager = tool_manager
        self.error_handler = PipelineErrorHandler(
            verbose=self.config.get("verbose", False)
        )

    def get_steps(self) -> List[str]:
        """Get the steps for impact analysis."""
        return self.steps

    async def execute(self, user_query: str, **kwargs) -> Dict[str, Any]:
        """Execute the impact pipeline."""
        try:
            if self.verbose:
                logger.info(f"Starting impact pipeline for query: {user_query}")

            conversation_history = kwargs.get("conversation_history", None)

            summarizer_kwargs = {}
            if conversation_history:
                summarizer_kwargs["conversation_history"] = conversation_history

            # Step 1: Entity Extraction
            entities = await self._execute_step(
                "entity_extractor", user_query=user_query
            )
            if not self.error_handler.has_meaningful_entities(entities):
                return self.error_handler.handle_no_entities(
                    self.intent, user_query, self.name
                )

            query_result = None
            if not SKIP_SQL_GENERATION:
                # Step 2: SQL Generation and Execution
                query_result = await self._execute_step(
                    "sql_generator", entities=entities
                )
                if query_result.row_count != 0:
                    summarizer_kwargs["dataset_rows"] = query_result.dict_rows

            # Step 3: Structured Data Organization
            structured_data = None
            if USE_NEW_DA_FLAG:
                # Use the new structured data organizer if the flag is set
                structured_data = await self._execute_step(
                    "structured_data_organizer_v2", entities=entities
                )

                # Step 4: RAG Search for implementation information
                overall_paper_ids = set()
                if structured_data:
                    for sd in structured_data:
                        if sd.paper_ids:
                            overall_paper_ids.update(sd.paper_ids)

                rag_results = await self._execute_step(
                    "rag_search",
                    query_result=user_query,
                    num_results=10,
                    paper_ids=list(overall_paper_ids),
                )
            else:
                # Old Data Analysis needs the dataset from SQL execution
                if query_result.row_count != 0:
                    structured_data = await self._execute_step(
                        "structured_data_organizer",
                        user_query=user_query,
                        entities=entities,
                        dataset=query_result.dataset,
                    )
                # Step 4: RAG Search for implementation information
                rag_results = await self._execute_step(
                    "rag_search", query_result=user_query, num_results=10
                )
            summarizer_kwargs["structured_data"] = structured_data
            summarizer_kwargs["rag_results"] = rag_results

            # Step 5: Final Answer Generation
            final_answer = await self._execute_step(
                "final_answer_generator", user_query=user_query, **summarizer_kwargs
            )

            return create_pipeline_response(
                intent=self.intent,
                status="completed_successfully",
                user_query=user_query,
                observation=final_answer.text,
                answer=final_answer.text,
                pipeline_name=self.name,
                thought=f"Successfully executed {self.intent} pipeline.",
                steps_completed=self.get_steps(),
                data_points=query_result.row_count if query_result else 0,
                papers_analyzed=query_result.unique_papers if query_result else 0,
                rag_passages=len(rag_results.documents) if rag_results else 0,
            )

        except Exception as e:
            return self.error_handler.handle_pipeline_error(
                self.intent, e, user_query, self.name
            )

    async def _execute_step(self, step_name: str, **kwargs):
        """Execute a pipeline step with error handling and tracking."""
        try:
            self.current_step += 1
            if self.verbose:
                logger.info(f"Step {self.current_step}: Executing {step_name}")

            # Start tracking the step
            self._track_step_start(step_name, kwargs)

            result = await self.tool_manager.execute_with_cache(step_name, **kwargs)
            self.results[step_name] = result

            # Track successful completion
            output_summary = {"type": type(result).__name__}
            if hasattr(result, "row_count"):
                output_summary["row_count"] = result.row_count
            if hasattr(result, "text"):
                output_summary["text_length"] = len(result.text)

            self._track_step_end(success=True, output_data=output_summary)

            return result

        except Exception as e:
            if self.verbose:
                logger.error(f"Error in {step_name}: {e}")

            # Track the error
            self._track_step_end(success=False, error_message=str(e))
            raise
