import os
import json
import glob
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List
from collections import defaultdict, Counter
import statistics


class PipelineAnalyticsDashboard:
    """
    Analytics dashboard for pipeline metrics from logs/metrics/ directory.
    """

    def __init__(
        self,
        metrics_dir: str = "logs/metrics",
        output_dir: str = "logs/analytics/pipeline_plots",
    ):
        """
        Initialize the pipeline analytics dashboard.

        Args:
            metrics_dir: Directory where pipeline metrics files are stored
            output_dir: Directory to save visualization outputs
        """
        self.metrics_dir = metrics_dir
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Set visualization style
        plt.style.use("seaborn-v0_8-darkgrid")
        sns.set_context("talk")
        self.colors = sns.color_palette("viridis", 12)
        self.title_fontsize = 16
        self.label_fontsize = 14
        self.tick_fontsize = 12

    def gather_metrics_files(
        self, prefix_filter: str = "pipeline_metrics"
    ) -> List[str]:
        """
        Gather all JSON files in metrics directory that start with a certain string.

        Args:
            prefix_filter: String prefix to filter files

        Returns:
            List of file paths
        """
        pattern = os.path.join(self.metrics_dir, f"{prefix_filter}*.json")
        files = glob.glob(pattern)

        print(f"Found {len(files)} metrics files matching pattern: {pattern}")
        for file in files:
            print(f"  - {os.path.basename(file)}")

        return files

    def load_all_metrics(
        self, prefix_filter: str = "pipeline_metrics"
    ) -> Dict[str, Any]:
        """
        Load and aggregate all metrics from matching files.

        Args:
            prefix_filter: String prefix to filter files

        Returns:
            Aggregated metrics data
        """
        files = self.gather_metrics_files(prefix_filter)

        all_pipelines = []
        all_conversations = []
        total_pipelines = 0
        successful_pipelines = 0
        failed_pipelines = 0

        for file_path in files:
            try:
                with open(file_path, "r") as f:
                    data = json.load(f)

                # Extract metrics from each file
                pipelines = data.get("pipelines", [])
                all_pipelines.extend(pipelines)

                # Track conversation-level stats
                conversation_data = {
                    "conversation_id": data.get("conversation_id"),
                    "total_pipelines": data.get("total_pipelines", 0),
                    "successful_pipelines": data.get("successful_pipelines", 0),
                    "failed_pipelines": data.get("failed_pipelines", 0),
                    "generated_at": data.get("generated_at"),
                    "file_path": file_path,
                }
                all_conversations.append(conversation_data)

                # Aggregate totals
                total_pipelines += data.get("total_pipelines", 0)
                successful_pipelines += data.get("successful_pipelines", 0)
                failed_pipelines += data.get("failed_pipelines", 0)

            except Exception as e:
                print(f"Error loading {file_path}: {e}")

        return {
            "pipelines": all_pipelines,
            "conversations": all_conversations,
            "totals": {
                "total_pipelines": total_pipelines,
                "successful_pipelines": successful_pipelines,
                "failed_pipelines": failed_pipelines,
                "total_questions": len(
                    all_conversations
                ),  # Each conversation = 1 question
            },
        }

    def calculate_analytics(self, metrics_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate comprehensive analytics from the metrics data.

        Args:
            metrics_data: Aggregated metrics data

        Returns:
            Analytics summary
        """
        pipelines = metrics_data["pipelines"]
        totals = metrics_data["totals"]

        analytics = {
            "overview": totals,
            "pipeline_analytics": {},
            "step_analytics": {},
            "intent_distribution": {},
            "execution_times": {},
        }

        if not pipelines:
            return analytics

        # Pipeline-level analytics
        pipeline_types = defaultdict(
            lambda: {"count": 0, "success": 0, "failed": 0, "times": []}
        )
        intent_distribution = Counter()
        all_execution_times = []
        step_times_by_name = defaultdict(list)

        for pipeline in pipelines:
            pipeline_name = pipeline.get("pipeline_name", "Unknown")
            intent = pipeline.get("intent", "Unknown")
            success = pipeline.get("success", False)
            total_duration = pipeline.get("total_duration", 0)

            # Pipeline type analytics
            pipeline_types[pipeline_name]["count"] += 1
            if success:
                pipeline_types[pipeline_name]["success"] += 1
            else:
                pipeline_types[pipeline_name]["failed"] += 1

            if total_duration:
                pipeline_types[pipeline_name]["times"].append(total_duration)
                all_execution_times.append(total_duration)

            # Intent distribution
            intent_distribution[intent] += 1

            # Step-level analytics
            for step in pipeline.get("steps", []):
                step_name = step.get("step_name", "Unknown")
                step_duration = step.get("duration")
                if step_duration:
                    step_times_by_name[step_name].append(step_duration)

        # Calculate pipeline means
        for pipeline_name, data in pipeline_types.items():
            if data["times"]:
                data["mean_time"] = statistics.mean(data["times"])
                data["median_time"] = statistics.median(data["times"])
            else:
                data["mean_time"] = 0
                data["median_time"] = 0

        analytics["pipeline_analytics"] = dict(pipeline_types)
        analytics["intent_distribution"] = dict(intent_distribution)

        # Step analytics
        step_analytics = {}
        for step_name, times in step_times_by_name.items():
            step_analytics[step_name] = {
                "count": len(times),
                "mean_time": statistics.mean(times) if times else 0,
                "median_time": statistics.median(times) if times else 0,
                "min_time": min(times) if times else 0,
                "max_time": max(times) if times else 0,
            }

        analytics["step_analytics"] = step_analytics

        # Execution time distribution
        if all_execution_times:
            analytics["execution_times"] = {
                "all_times": all_execution_times,
                "mean": statistics.mean(all_execution_times),
                "median": statistics.median(all_execution_times),
                "min": min(all_execution_times),
                "max": max(all_execution_times),
                "std": (
                    statistics.stdev(all_execution_times)
                    if len(all_execution_times) > 1
                    else 0
                ),
            }

        return analytics

    def print_dashboard(self, analytics: Dict[str, Any]) -> None:
        """
        Print a text-based dashboard with key metrics.

        Args:
            analytics: Analytics data
        """
        print("\n" + "=" * 80)
        print("PIPELINE ANALYTICS DASHBOARD")
        print("=" * 80)

        # Overview
        overview = analytics["overview"]
        print("\n📊 OVERVIEW")
        print(f"   Questions Processed: {overview['total_questions']}")
        print(f"   Total Pipelines: {overview['total_pipelines']}")
        print(f"   Successful Pipelines: {overview['successful_pipelines']}")
        print(f"   Failed Pipelines: {overview['failed_pipelines']}")

        if overview["total_pipelines"] > 0:
            success_rate = (
                overview["successful_pipelines"] / overview["total_pipelines"]
            ) * 100
            print(f"   Success Rate: {success_rate:.1f}%")

        # Pipeline breakdown
        pipeline_analytics = analytics["pipeline_analytics"]
        print("\n🔧 PIPELINE BREAKDOWN")
        for pipeline_name, data in pipeline_analytics.items():
            print(f"   {pipeline_name}:")
            print(
                f"      Total: {data['count']} | Success: {data['success']} | Failed: {data['failed']}"
            )
            if data["mean_time"] > 0:
                print(
                    f"      Avg Time: {data['mean_time']:.2f}s | Median: {data['median_time']:.2f}s"
                )

        # Execution times
        exec_times = analytics.get("execution_times", {})
        if exec_times:
            print("\n⏱️  EXECUTION TIMES")
            print(f"   Mean: {exec_times['mean']:.2f}s")
            print(f"   Median: {exec_times['median']:.2f}s")
            print(f"   Range: {exec_times['min']:.2f}s - {exec_times['max']:.2f}s")

        # Step times
        step_analytics = analytics["step_analytics"]
        if step_analytics:
            print("\n🔄 TOP 5 SLOWEST STEPS")
            sorted_steps = sorted(
                step_analytics.items(), key=lambda x: x[1]["mean_time"], reverse=True
            )[:5]
            for step_name, data in sorted_steps:
                print(
                    f"   {step_name}: {data['mean_time']:.2f}s avg ({data['count']} executions)"
                )

        # Intent distribution
        intent_dist = analytics["intent_distribution"]
        print("\n🎯 INTENT DISTRIBUTION")
        for intent, count in sorted(
            intent_dist.items(), key=lambda x: x[1], reverse=True
        ):
            print(f"   {intent}: {count}")

        print("\n" + "=" * 80)

    def plot_pipeline_success_rate(self, analytics: Dict[str, Any]) -> plt.Figure:
        """Plot pipeline success rates by type."""
        pipeline_analytics = analytics["pipeline_analytics"]

        if not pipeline_analytics:
            print("No pipeline data available for success rate plot")
            return None

        # Prepare data
        names = []
        success_rates = []
        total_counts = []

        for name, data in pipeline_analytics.items():
            if data["count"] > 0:
                names.append(name.replace("Pipeline", ""))  # Shorten names
                success_rate = (data["success"] / data["count"]) * 100
                success_rates.append(success_rate)
                total_counts.append(data["count"])

        if not names:
            return None

        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))

        # Create bars with color based on success rate
        colors = [
            (
                self.colors[0]
                if rate >= 80
                else self.colors[3] if rate >= 50 else self.colors[7]
            )
            for rate in success_rates
        ]
        bars = ax.bar(names, success_rates, color=colors, alpha=0.8)

        # Add value labels
        for i, (bar, count) in enumerate(zip(bars, total_counts)):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + 1,
                f"{height:.1f}%\n({count} runs)",
                ha="center",
                va="bottom",
                fontsize=self.tick_fontsize,
            )

        ax.set_ylabel("Success Rate (%)", fontsize=self.label_fontsize)
        ax.set_title("Pipeline Success Rates", fontsize=self.title_fontsize)
        ax.set_ylim(0, 110)
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Save plot
        filepath = os.path.join(self.output_dir, "pipeline_success_rates.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        return fig

    def plot_execution_time_distribution(self, analytics: Dict[str, Any]) -> plt.Figure:
        """Plot distribution of execution times."""
        exec_times = analytics.get("execution_times", {})
        all_times = exec_times.get("all_times", [])

        if not all_times:
            print("No execution time data available")
            return None

        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Histogram
        ax1.hist(all_times, bins=20, color=self.colors[4], alpha=0.7, edgecolor="black")
        ax1.axvline(
            exec_times["mean"],
            color=self.colors[1],
            linestyle="--",
            linewidth=2,
            label=f"Mean: {exec_times['mean']:.2f}s",
        )
        ax1.axvline(
            exec_times["median"],
            color=self.colors[3],
            linestyle="-",
            linewidth=2,
            label=f"Median: {exec_times['median']:.2f}s",
        )

        ax1.set_xlabel("Execution Time (seconds)", fontsize=self.label_fontsize)
        ax1.set_ylabel("Frequency", fontsize=self.label_fontsize)
        ax1.set_title("Execution Time Distribution", fontsize=self.title_fontsize)
        ax1.legend()

        # Box plot
        ax2.boxplot(
            all_times,
            vert=True,
            patch_artist=True,
            boxprops=dict(facecolor=self.colors[5], alpha=0.7),
        )
        ax2.set_ylabel("Execution Time (seconds)", fontsize=self.label_fontsize)
        ax2.set_title("Execution Time Box Plot", fontsize=self.title_fontsize)
        ax2.set_xticklabels(["All Pipelines"])

        plt.tight_layout()

        # Save plot
        filepath = os.path.join(self.output_dir, "execution_time_distribution.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        return fig

    def plot_pipeline_execution_times(self, analytics: Dict[str, Any]) -> plt.Figure:
        """Plot mean execution times by pipeline type."""
        pipeline_analytics = analytics["pipeline_analytics"]

        # Filter pipelines with execution data
        pipelines_with_times = {
            name: data
            for name, data in pipeline_analytics.items()
            if data.get("mean_time", 0) > 0
        }

        if not pipelines_with_times:
            print("No pipeline execution time data available")
            return None

        # Prepare data
        names = [name.replace("Pipeline", "") for name in pipelines_with_times.keys()]
        mean_times = [data["mean_time"] for data in pipelines_with_times.values()]

        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))

        # Sort by execution time
        sorted_data = sorted(zip(names, mean_times), key=lambda x: x[1], reverse=True)
        names, mean_times = zip(*sorted_data)

        bars = ax.bar(names, mean_times, color=self.colors[6], alpha=0.8)

        # Add value labels
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + height * 0.01,
                f"{height:.2f}s",
                ha="center",
                va="bottom",
                fontsize=self.tick_fontsize,
            )

        ax.set_ylabel("Mean Execution Time (seconds)", fontsize=self.label_fontsize)
        ax.set_title("Mean Execution Time by Pipeline", fontsize=self.title_fontsize)
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Save plot
        filepath = os.path.join(self.output_dir, "pipeline_execution_times.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        return fig

    def plot_step_execution_times(self, analytics: Dict[str, Any]) -> plt.Figure:
        """Plot mean execution times by step."""
        step_analytics = analytics["step_analytics"]

        if not step_analytics:
            print("No step execution time data available")
            return None

        # Get top 10 slowest steps
        sorted_steps = sorted(
            step_analytics.items(), key=lambda x: x[1]["mean_time"], reverse=True
        )[:10]

        if not sorted_steps:
            return None

        names = [name for name, _ in sorted_steps]
        mean_times = [data["mean_time"] for _, data in sorted_steps]
        counts = [data["count"] for _, data in sorted_steps]

        # Create figure
        fig, ax = plt.subplots(figsize=(14, 8))

        bars = ax.bar(range(len(names)), mean_times, color=self.colors[8], alpha=0.8)

        # Add value labels with counts
        for i, (bar, count) in enumerate(zip(bars, counts)):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + height * 0.01,
                f"{height:.2f}s\n({count}x)",
                ha="center",
                va="bottom",
                fontsize=self.tick_fontsize,
            )

        ax.set_ylabel("Mean Execution Time (seconds)", fontsize=self.label_fontsize)
        ax.set_title(
            "Mean Execution Time by Step (Top 10)", fontsize=self.title_fontsize
        )
        ax.set_xticks(range(len(names)))
        ax.set_xticklabels(names, rotation=45, ha="right")
        plt.tight_layout()

        # Save plot
        filepath = os.path.join(self.output_dir, "step_execution_times.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        return fig

    def plot_intent_and_pipeline_distribution(
        self, analytics: Dict[str, Any]
    ) -> plt.Figure:
        """Plot distribution of intents and pipelines."""
        intent_dist = analytics["intent_distribution"]
        pipeline_analytics = analytics["pipeline_analytics"]

        if not intent_dist and not pipeline_analytics:
            print("No distribution data available")
            return None

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # Intent distribution
        if intent_dist:
            intents = list(intent_dist.keys())
            intent_counts = list(intent_dist.values())

            ax1.pie(
                intent_counts,
                labels=intents,
                autopct="%1.1f%%",
                colors=self.colors[: len(intents)],
                startangle=90,
            )
            ax1.set_title("Intent Distribution", fontsize=self.title_fontsize)

        # Pipeline distribution
        if pipeline_analytics:
            pipeline_names = [
                name.replace("Pipeline", "") for name in pipeline_analytics.keys()
            ]
            pipeline_counts = [data["count"] for data in pipeline_analytics.values()]

            ax2.pie(
                pipeline_counts,
                labels=pipeline_names,
                autopct="%1.1f%%",
                colors=self.colors[: len(pipeline_names)],
                startangle=90,
            )
            ax2.set_title("Pipeline Distribution", fontsize=self.title_fontsize)

        plt.tight_layout()

        # Save plot
        filepath = os.path.join(self.output_dir, "intent_pipeline_distribution.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        return fig

    def plot_execution_time_vs_row_count(self, analytics: Dict[str, Any]) -> plt.Figure:
        """Plot execution time vs row count with separate charts for each pipeline."""
        # Get pipelines from the raw metrics data instead of analytics
        metrics_data = getattr(self, "_last_metrics_data", {})
        pipelines = metrics_data.get("pipelines", [])

        if not pipelines:
            print("No pipeline data available for execution time vs row count plot")
            print("Available analytics keys:", list(analytics.keys()))
            return None

        print(f"Found {len(pipelines)} pipelines to analyze")

        # Extract data for scatter plot
        scatter_data = []

        for i, pipeline in enumerate(pipelines):
            pipeline_name = pipeline.get("pipeline_name", "Unknown")
            total_duration = pipeline.get("total_duration")

            # Skip if no duration data
            if not total_duration:
                continue

            # Look for QueryResult output_data with row_count
            row_count = None
            output_data = pipeline.get("output_data")

            # Check different possible structures for output_data
            if output_data is not None:
                # Case 1: Direct dict with type and row_count
                if isinstance(output_data, dict):
                    if (
                        output_data.get("type") == "QueryResult"
                        and "row_count" in output_data
                    ):
                        row_count = output_data.get("row_count")
                    # Case 2: Maybe row_count is directly in output_data
                    elif "row_count" in output_data:
                        row_count = output_data.get("row_count")
                    # Case 3: Maybe there's nested data
                    elif "data" in output_data and isinstance(
                        output_data["data"], dict
                    ):
                        nested_data = output_data["data"]
                        if "row_count" in nested_data:
                            row_count = nested_data.get("row_count")

                # Case 4: Maybe output_data is a list and we need to check items
                elif isinstance(output_data, list) and len(output_data) > 0:
                    for item in output_data:
                        if (
                            isinstance(item, dict)
                            and item.get("type") == "QueryResult"
                            and "row_count" in item
                        ):
                            row_count = item.get("row_count")
                            break

            # Also check if row_count might be elsewhere in the pipeline data
            if row_count is None:
                # Check if row_count is directly in pipeline
                if "row_count" in pipeline:
                    row_count = pipeline.get("row_count")
                # Check steps for row_count
                elif "steps" in pipeline:
                    for step in pipeline.get("steps", []):
                        if isinstance(step, dict) and "row_count" in step:
                            row_count = step.get("row_count")
                            break
                        elif isinstance(step, dict) and "output_data" in step:
                            step_output = step["output_data"]
                            if (
                                isinstance(step_output, dict)
                                and "row_count" in step_output
                            ):
                                row_count = step_output.get("row_count")
                                break

            # Only add if we have both duration and row count
            if row_count is not None and total_duration is not None:
                scatter_data.append(
                    {
                        "pipeline_name": pipeline_name,
                        "total_duration": total_duration,
                        "row_count": row_count,
                    }
                )

        if not scatter_data:
            print("No data points with both execution time and row count available")
            print("This might mean:")
            print(
                "1. No pipelines have output_data with type='QueryResult' and row_count"
            )
            print("2. The data structure is different than expected")
            print("3. No QueryResult pipelines were executed")
            return None

        # Group data by pipeline
        pipeline_data = {}
        for data_point in scatter_data:
            pipeline_name = data_point["pipeline_name"]
            if pipeline_name not in pipeline_data:
                pipeline_data[pipeline_name] = {"durations": [], "row_counts": []}
            pipeline_data[pipeline_name]["durations"].append(
                data_point["total_duration"]
            )
            pipeline_data[pipeline_name]["row_counts"].append(data_point["row_count"])

        # Get unique pipeline names
        unique_pipelines = list(pipeline_data.keys())
        num_pipelines = len(unique_pipelines)

        # Calculate subplot layout (try to make it roughly square)
        if num_pipelines == 1:
            rows, cols = 1, 1
        elif num_pipelines == 2:
            rows, cols = 1, 2
        elif num_pipelines <= 4:
            rows, cols = 2, 2
        elif num_pipelines <= 6:
            rows, cols = 2, 3
        elif num_pipelines <= 9:
            rows, cols = 3, 3
        else:
            rows = int(num_pipelines**0.5) + 1
            cols = (num_pipelines + rows - 1) // rows

        # Create figure with subplots
        fig, axes = plt.subplots(rows, cols, figsize=(5 * cols, 4 * rows))

        # Handle case where there's only one subplot
        if num_pipelines == 1:
            axes = [axes]
        elif rows == 1 or cols == 1:
            # axes is 1D array
            pass
        else:
            # axes is 2D array, flatten it
            axes = axes.flatten()

        # Plot each pipeline in its own subplot
        for i, (pipeline_name, data) in enumerate(pipeline_data.items()):
            ax = axes[i]

            durations = data["durations"]
            row_counts = data["row_counts"]

            # Create scatter plot for this pipeline
            ax.scatter(
                row_counts,
                durations,
                c=self.colors[i % len(self.colors)],
                alpha=0.7,
                s=100,
                edgecolors="black",
                linewidth=0.5,
            )

            # Set labels and title
            ax.set_xlabel("Number of Rows Processed", fontsize=self.label_fontsize - 2)
            ax.set_ylabel("Execution Time (seconds)", fontsize=self.label_fontsize - 2)
            ax.set_title(
                pipeline_name.replace("Pipeline", ""), fontsize=self.title_fontsize - 2
            )

            # Add grid for better readability
            ax.grid(True, alpha=0.3)

            # Add some statistics as text
            if len(durations) > 1:
                import numpy as np

                # Calculate correlation if we have enough points
                if len(durations) >= 3:
                    correlation = np.corrcoef(row_counts, durations)[0, 1]
                    ax.text(
                        0.05,
                        0.95,
                        f"Correlation: {correlation:.3f}",
                        transform=ax.transAxes,
                        fontsize=10,
                        bbox=dict(
                            boxstyle="round,pad=0.3", facecolor="white", alpha=0.8
                        ),
                    )

        # Hide unused subplots
        for i in range(num_pipelines, len(axes)):
            axes[i].set_visible(False)

        # Add overall title
        fig.suptitle(
            "Pipeline Execution Time vs Number of Rows Processed",
            fontsize=self.title_fontsize,
            y=0.98,
        )

        plt.tight_layout()
        plt.subplots_adjust(top=0.93)  # Make room for suptitle

        # Save plot
        filepath = os.path.join(self.output_dir, "execution_time_vs_row_count.png")
        fig.savefig(filepath, bbox_inches="tight", dpi=300)
        print(f"Plot saved to {filepath}")

        # Print some summary statistics
        print("\nScatter plot data summary:")
        print(f"  Total data points: {len(scatter_data)}")
        print(f"  Unique pipelines: {num_pipelines}")
        for pipeline_name, data in pipeline_data.items():
            durations = data["durations"]
            row_counts = data["row_counts"]
            print(f"  {pipeline_name}:")
            print(f"    Data points: {len(durations)}")
            print(f"    Row count range: {min(row_counts)} - {max(row_counts)}")
            print(f"    Duration range: {min(durations):.2f}s - {max(durations):.2f}s")

        return fig

    def generate_full_analysis(
        self, prefix_filter: str = "pipeline_metrics"
    ) -> Dict[str, Any]:
        """
        Generate complete analysis and all visualizations.

        Args:
            prefix_filter: String prefix to filter metrics files

        Returns:
            Complete analytics data
        """
        print("🚀 Starting Pipeline Analytics Analysis...")
        print(f"📁 Looking for files matching: {prefix_filter}*.json")

        # Load all metrics
        metrics_data = self.load_all_metrics(prefix_filter)
        # Store metrics data for scatter plot access
        self._last_metrics_data = metrics_data

        # Calculate analytics
        analytics = self.calculate_analytics(metrics_data)

        # Print dashboard
        self.print_dashboard(analytics)

        # Generate all plots
        print("\n📈 Generating visualizations...")
        self.plot_pipeline_success_rate(analytics)
        self.plot_execution_time_distribution(analytics)
        self.plot_pipeline_execution_times(analytics)
        self.plot_step_execution_times(analytics)
        self.plot_intent_and_pipeline_distribution(analytics)
        self.plot_execution_time_vs_row_count(analytics)

        print(f"\n✅ Analysis complete! Check {self.output_dir} for plots.")

        return analytics


def run_pipeline_analytics(prefix_filter: str = "pipeline_metrics"):
    """
    Convenience function to run the full pipeline analytics.

    Args:
        prefix_filter: String prefix to filter metrics files
    """
    dashboard = PipelineAnalyticsDashboard()
    return dashboard.generate_full_analysis(prefix_filter)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Generate pipeline analytics dashboard"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="logs/analytics/pipeline_plots",
        help="Output directory for plots (default: logs/analytics/pipeline_plots)",
    )
    parser.add_argument(
        "--prefix",
        type=str,
        default="pipeline_metrics",
        help="Prefix filter for metrics files (default: pipeline_metrics)",
    )

    args = parser.parse_args()

    run_pipeline_analytics(args.prefix)
