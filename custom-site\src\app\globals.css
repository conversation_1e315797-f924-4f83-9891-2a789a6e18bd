@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  width: 100vw;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --1: 8px;
  --2: 16px;
  --3: 24px;
  --borderRadius: 4px;
  --padding-horizontal: 16px;
  /* Default horizontal padding */
}

@media (min-width: 600px) {
  :root {
    --padding-horizontal: 32px;
    /* Adjust for larger screens */
  }
}

@media (min-width: 1200px) {
  :root {
    --padding-horizontal: 64px;
    /* Adjust for even larger screens */
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: #004370;
  background: var(--background);
  font-family: Roboto, Helvetica, Arial, sans-serif;
  padding: '0px';
}

@font-face {
  font-display: swap;
  font-family: 'HostGrotesk';
  font-weight: 400;
  src: url('/fonts/host-grotesk-v4-latin-regular.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'HostGroteskBold';
  font-weight: 500;
  src: url('/fonts/host-grotesk-v4-latin-500.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Open Sans';
  font-weight: 500;
  src: url('/fonts/OpenSans-Regular.woff2') format('woff2');
}

@font-face {
  font-display: swap;
  font-family: 'Open Sans Bold';
  font-weight: 500;
  src: url('/fonts/OpenSans-Bold.woff2') format('woff2');
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'HostGrotesk', 'Roboto', 'Helvetica', sans-serif;
}

@media (max-width: 599px) {
  h1 {
    font-size: 20px;
    line-height: 40px;
  }

  h2 {
    font-size: 24px;
    line-height: 43px;
  }

  h3 {
    font-size: 20px;
    line-height: 30px;
  }

  h4 {
    font-size: 18px;
    line-height: 30px;
  }
}

@media (min-width: 768px) and (max-width: 899px) {
  h1 {
    font-size: 30px !important;
    line-height: 40px !important;
  }

  h2 {
    font-size: 24px !important;
    line-height: 43px !important;
  }

  h3 {
    font-size: 20px !important;
    line-height: 30px !important;
  }

  h4 {
    font-size: 18px !important;
    line-height: 30px !important;
  }
}