import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from helpers.filesystem import get_fixture_file_path


def load_mock_data():
    file_path = get_fixture_file_path("mock-agent-responses.json")
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


MOCK_DATA = load_mock_data()


@pytest.fixture
def mock_agent_service_instance():
    """Create a mock AgentService instance"""
    mock_service = MagicMock()
    mock_service.execute = AsyncMock(return_value=MOCK_DATA[0]["response"])
    mock_service.generate_summary = AsyncMock()
    mock_service.generated_summary_data = AsyncMock()
    return mock_service


@pytest.fixture
def mock_modules():
    mock_agent_module = MagicMock()
    mock_agent_class = MagicMock()
    mock_agent_module.AgentService = mock_agent_class

    return {
        "src.services.agent": mock_agent_module,
        "src.utils.db": MagicMock(engine=MagicMock()),
        "structlog": MagicMock(),
        "structlog.processors": MagicMock(TimeStamper=MagicMock()),
        "structlog.stdlib": MagicMock(
            LoggerFactory=MagicMock(), BoundLogger=MagicMock()
        ),
    }


@pytest.fixture
def app_with_mocks(mock_modules, mock_agent_service_instance):
    with patch.dict("sys.modules", mock_modules):
        # Patch the AgentService class to return our mock instance
        with patch("src.services.agent.AgentService", return_value=mock_agent_service_instance):
            from api import app
            return app





async def make_request(app, method, url, **kwargs):
    """Helper function to make requests to the app using ASGI transport"""
    from httpx import ASGITransport, AsyncClient

    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://testserver") as client:
        if method.upper() == "GET":
            return await client.get(url, **kwargs)
        elif method.upper() == "POST":
            return await client.post(url, **kwargs)


@pytest.mark.asyncio
async def test_execute_endpoint_response_structure(app_with_mocks, mock_agent_service_instance):
    """Test /execute endpoint returns correct response structure that upstream apps expect"""
    mock_data = MOCK_DATA[0]
    mock_agent_service_instance.execute.return_value = mock_data["response"]

    conversation_id = mock_data["response"]["context"]["conversation_id"]
    query = mock_data["response"]["context"]["query"]

    response = await make_request(
        app_with_mocks,
        "POST",
        "/execute",
        json={"conversation_id": conversation_id, "query": query},
    )

    # Critical: Test exact response structure that upstream depends on
    assert response.status_code == 200
    response_data = response.json()

    # Top-level structure
    assert "response" in response_data
    assert isinstance(response_data["response"], dict)

    # Response structure
    assert "response" in response_data["response"]
    assert "context" in response_data["response"]

    # Context structure (critical for upstream)
    context = response_data["response"]["context"]
    assert "query" in context
    assert "conversation_id" in context
    assert "tool_data" in context

    # Tool data structure (test what's actually in the response)
    tool_data = context["tool_data"]
    assert "data_used" in tool_data
    assert isinstance(tool_data["data_used"], list)
    # Note: structured_data and rag_results may or may not be present depending on the agent's execution

    # Verify service called correctly
    mock_agent_service_instance.execute.assert_called_once_with(conversation_id, query)


@pytest.mark.asyncio
async def test_execute_endpoint_validation_error(app_with_mocks):
    """Test /execute endpoint validation errors that could break upstream"""
    # Missing required query field
    response = await make_request(
        app_with_mocks, "POST", "/execute", json={"conversation_id": "test-123"}
    )
    assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_execute_endpoint_empty_body(app_with_mocks):
    """Test /execute endpoint with empty body"""
    response = await make_request(app_with_mocks, "POST", "/execute", json={})
    assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_execute_endpoint_optional_conversation_id(
    app_with_mocks, mock_agent_service_instance
):
    """Test /execute endpoint with null conversation_id (should work)"""
    mock_data = MOCK_DATA[0]
    mock_agent_service_instance.execute.return_value = mock_data["response"]

    response = await make_request(
        app_with_mocks,
        "POST",
        "/execute",
        json={"conversation_id": None, "query": "test query"},
    )

    assert response.status_code == 200
    mock_agent_service_instance.execute.assert_called_once_with(None, "test query")


@pytest.mark.asyncio
async def test_health_check_healthy(app_with_mocks):
    """Test /health endpoint returns expected format"""
    response = await make_request(app_with_mocks, "GET", "/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy!"}


@pytest.mark.asyncio
async def test_root_endpoint_preview(app_with_mocks):
    """Test root endpoint returns HTML preview"""
    response = await make_request(app_with_mocks, "GET", "/")
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/html; charset=utf-8"
    assert "Agent - Preview Tool" in response.text


@pytest.mark.asyncio
async def test_get_conversation_message_summary_streams_correctly(app_with_mocks, mock_agent_service_instance):
    """Test summary endpoint streams data as text/event-stream and calls generate_summary with correct parameters"""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    query = "What is the summary?"
    
    # Mock the generate_summary method to yield test data
    async def mock_generate_summary(*args, **kwargs):
        yield "First chunk of summary"
        yield "Second chunk of summary"
        yield "Final chunk of summary"
    
    mock_agent_service_instance.generate_summary = mock_generate_summary
    
    # Make request to the streaming endpoint
    response = await make_request(
        app_with_mocks,
        "GET", 
        f"/conversations/{conversation_id}/messages/{message_id}/summary",
        params={"query": query}
    )
    
    # Verify response is a streaming response with correct content type
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    
    # For streaming responses, we need to read the content differently
    # The content should contain the yielded data
    response_text = response.text
    # The streaming response should contain the yielded content
    assert "First chunk of summary" in response_text
    assert "Second chunk of summary" in response_text  
    assert "Final chunk of summary" in response_text


@pytest.mark.asyncio
async def test_get_conversation_message_summary_calls_service_with_correct_params(app_with_mocks, mock_agent_service_instance):
    """Test summary endpoint calls agent service generate_summary method with the exact parameters from URL and query"""
    conversation_id = "test_conv_456"
    message_id = "msg_789"
    query = "Generate a detailed summary"
    
    # Mock the generate_summary method to track calls
    async def mock_generate_summary(conv_id, msg_id, q):
        # Verify parameters are passed correctly
        assert conv_id == conversation_id
        assert msg_id == message_id  
        assert q == query
        yield "Summary response"
    
    mock_agent_service_instance.generate_summary = mock_generate_summary
    
    # Make request to trigger the service call
    response = await make_request(
        app_with_mocks,
        "GET",
        f"/conversations/{conversation_id}/messages/{message_id}/summary", 
        params={"query": query}
    )
    
    # Verify successful response (which confirms service was called correctly)
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_get_conversation_message_data_returns_service_response(app_with_mocks, mock_agent_service_instance):
    """Test data endpoint returns exactly what the service method provides"""
    conversation_id = "test_conv_789"
    message_id = "msg_101"
    
    # Mock data that the service should return
    expected_data = {
        "query": "test query",
        "stream_started_at": "2024-01-01 12:00:00.000000",
        "stream_ended_at": "2024-01-01 12:05:00.000000",
        "summary_text": "This is the completed summary",
        "summary_data": {"key": "value", "count": 42}
    }
    
    # Mock the generated_summary_data method
    mock_agent_service_instance.generated_summary_data = AsyncMock(return_value=expected_data)
    
    # Make request to the data endpoint
    response = await make_request(
        app_with_mocks,
        "GET",
        f"/conversations/{conversation_id}/messages/{message_id}/data"
    )
    
    # Verify response structure and content
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    response_data = response.json()
    
    # Verify response contains exactly what the service returned
    assert response_data == expected_data
    
    # Verify service was called with correct parameters
    mock_agent_service_instance.generated_summary_data.assert_called_once_with(conversation_id, message_id)


@pytest.mark.asyncio
async def test_get_conversation_message_data_handles_none_response(app_with_mocks, mock_agent_service_instance):
    """Test data endpoint handles when service returns None (no data found)"""
    conversation_id = "test_conv_999"
    message_id = "msg_404"
    
    # Mock the service to return None (no data found)
    mock_agent_service_instance.generated_summary_data = AsyncMock(return_value=None)
    
    # Make request to the data endpoint
    response = await make_request(
        app_with_mocks,
        "GET", 
        f"/conversations/{conversation_id}/messages/{message_id}/data"
    )
    
    # Verify response indicates no data found
    assert response.status_code == 200
    response_data = response.json()
    assert response_data is None
    
    # Verify service was called with correct parameters
    mock_agent_service_instance.generated_summary_data.assert_called_once_with(conversation_id, message_id)


@pytest.mark.asyncio
async def test_get_conversation_message_data_calls_service_with_correct_params(app_with_mocks, mock_agent_service_instance):
    """Test data endpoint calls agent service with exact conversation_id and message_id from URL"""
    conversation_id = "specific_conv_id_123"
    message_id = "specific_msg_id_456"
    
    # Mock the service method to verify parameters
    async def mock_generated_summary_data(conv_id, msg_id):
        # Verify parameters are passed correctly
        assert conv_id == conversation_id
        assert msg_id == message_id
        return {"test": "data"}
    
    mock_agent_service_instance.generated_summary_data = mock_generated_summary_data
    
    # Make request to trigger the service call
    response = await make_request(
        app_with_mocks,
        "GET",
        f"/conversations/{conversation_id}/messages/{message_id}/data"
    )
    
    # Verify successful response (which confirms service was called correctly)
    assert response.status_code == 200
    assert response.json() == {"test": "data"}
