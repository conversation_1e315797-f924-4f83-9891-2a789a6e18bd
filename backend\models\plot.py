# pylint: disable=trailing-whitespace
from dataclasses import dataclass, field
from dataclasses_json import dataclass_json
from typing import List, Optional
from enum import StrEnum, auto


@dataclass_json
@dataclass
class TagWithLevel:
    tag_label: str
    level: int


@dataclass_json
@dataclass
class Score:
    lower: float
    upper: float
    value: float


@dataclass_json
@dataclass
class EffectSizeDataPoint:
    label: str
    score: Score
    country: str
    paper_id: int
    paper_title: str
    paper_citation: str


@dataclass_json
@dataclass
class EffectSize:
    data: List[EffectSizeDataPoint]
    outcome: str
    outcome_id: int
    intervention: str
    intervention_id: int
    aggregate: Score


@dataclass_json
@dataclass
class Study:
    id: str
    label: str
    pulication_year: int
    first_author: Optional[str] = None
    citation: Optional[str] = None
    population: Optional[int] = None
    country: Optional[str] = None
    region_code: Optional[str] = None
    region_label: Optional[str] = None
    quality_score: Optional[int] = None
    quality_score_group: Optional[str] = None
    income_group_code: Optional[str] = None
    income_group_label: Optional[str] = None
    sector: Optional[str] = None
    journal_name: Optional[str] = None


class DefaultTabType(StrEnum):
    Geography = auto("geography")


@dataclass_json
@dataclass
class RawEffectSize:
    paper_id: str
    paper_combined_id: str
    title: Optional[str] = None
    year: Optional[int] = None
    doi_url: Optional[str] = None
    doi: Optional[str] = None
    authors: Optional[str] = None
    first_author: Optional[str] = None
    journal_name: Optional[str] = None
    country_code: Optional[str] = None
    country_name: Optional[str] = None
    region: Optional[str] = None
    income_group: Optional[str] = None
    quality_score: Optional[float] = None
    quality_score_category: Optional[str] = None
    treatment_arm: Optional[str] = None
    intervention_id: Optional[str] = None
    intervention_tag_ids: Optional[str] = None
    intervention_tags_with_levels: Optional[List[TagWithLevel]] = field(
        default_factory=list
    )
    outcome_tags_with_levels: Optional[List[TagWithLevel]] = field(default_factory=list)
    intervention_tag_labels: Optional[str] = None
    intervention_tag_short_labels: Optional[str] = None
    intervention_tag_definitions: Optional[str] = None
    intervention_target_populations: Optional[str] = None
    intervention_sectors: Optional[str] = None
    intervention_objective: Optional[str] = None
    intervention_scale: Optional[str] = None
    intervention_intensity: Optional[str] = None
    intervention_fidelity: Optional[str] = None
    intervention_description: Optional[str] = None
    intervention_analysis_unit: Optional[str] = None
    intervention_cost: Optional[str] = None
    outcome_ids: Optional[str] = None
    outcome_tag_ids: Optional[str] = None
    outcome_tag_labels: Optional[str] = None
    outcome_tag_short_labels: Optional[str] = None
    outcome_tag_definition: Optional[str] = None
    outcome_target_populations: Optional[str] = None
    outcome_sectors: Optional[str] = None
    outcome_description: Optional[str] = None
    outcome_analysis_unit: Optional[str] = None
    outcome_connotation: Optional[str] = None
    outcome_type: Optional[str] = None
    is_primary_period: Optional[int] = None
    data_collection_round: Optional[str] = None
    cohen_d: Optional[float] = None
    hedges_d: Optional[float] = None
    standardized_ci_lower: Optional[float] = None
    standardized_ci_upper: Optional[float] = None

    @property
    def plot_datapoint_pair(self) -> List[int]:
        if not self.intervention_tag_ids or not self.outcome_tag_ids:
            return []

        # splitting because it can be: "20402;21855" or "20402"
        intervention_ids = self.intervention_tag_ids.split(';')
        outcome_ids = self.outcome_tag_ids.split(';')
        return [int(intervention_ids[0]), int(outcome_ids[0])]

@dataclass_json
@dataclass
class RawIntervention:
    intervention_id: Optional[int] = None
    intervention_tag_ids: Optional[str] = None
    intervention_tag_labels: Optional[str] = None
    intervention_tag_short_labels: Optional[str] = None
    intervention_tag_definitions: Optional[str] = None
    intervention_target_populations: Optional[str] = None
    intervention_sectors: Optional[str] = None
    intervention_objective: Optional[str] = None
    intervention_scale: Optional[str] = None
    intervention_intensity: Optional[str] = None
    intervention_fidelity: Optional[str] = None
    intervention_description: Optional[str] = None
    intervention_analysis_unit: Optional[str] = None
    intervention_cost: Optional[str] = None
    intervention_tags_with_levels: Optional[List[TagWithLevel]] = field(
        default_factory=list
    )


@dataclass_json
@dataclass
class RawOutcome:
    outcome_ids: Optional[str] = None
    outcome_tag_ids: Optional[str] = None
    outcome_tag_labels: Optional[str] = None
    outcome_tag_short_labels: Optional[str] = None
    outcome_tag_definition: Optional[str] = None
    outcome_target_populations: Optional[str] = None
    outcome_sectors: Optional[str] = None
    outcome_description: Optional[str] = None
    outcome_analysis_unit: Optional[str] = None
    outcome_connotation: Optional[str] = None
    outcome_type: Optional[str] = None
    outcome_tags_with_levels: Optional[List[TagWithLevel]] = field(default_factory=list)


@dataclass_json
@dataclass
class PlotData:
    default_tab: DefaultTabType
    studies: List[Study]
    interventions: List[RawIntervention]
    outcomes: List[RawOutcome]
    flat_effect_sizes: List[RawEffectSize]
    effect_sizes: List[EffectSize]


class PlotType(StrEnum):
    DescriptivePlot = auto("descriptive_plot")


@dataclass_json
@dataclass
class Plot:
    type: PlotType
    data: PlotData
