import time, os
from functools import wraps
from dotenv import load_dotenv
import structlog

load_dotenv()

in_debug = os.getenv("DEBUG", "false").lower() == "true"

logger = structlog.get_logger(__name__)

def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not in_debug:
            result = func(*args, **kwargs)
            return result

        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        logger.info(
            function=func.__name__,
            execution_time=f"{execution_time:.4f} seconds"
        )
        return result

    return wrapper


def measure_async_time(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if not in_debug:
            result = await func(*args, **kwargs)
            return result

        start_time = time.perf_counter()
        result = await func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        logger.info(
            function=func.__name__,
            execution_time=f"{execution_time:.4f} seconds"
        )
        return result

    return wrapper
