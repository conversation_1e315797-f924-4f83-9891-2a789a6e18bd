.markdown-container > * {
    line-height: 1.75;
    text-decoration: none;
}

.markdown-container > :first-child {
    margin-top: 0 !important;
}

.markdown-container > :last-child {
    margin-bottom: 0 !important;
}

.markdown-container {
    text-align: left !important;
    overflow-wrap: break-word;
    max-width: 100%;
    white-space: normal;
    word-spacing: normal;
    text-decoration: none;
}

.markdown-container em {
    font-style: normal;
    font-weight: inherit;
    text-decoration: none;
    color: inherit;
}

.markdown-container ul {
    padding-left: 15px;
}

.graph-sticky-element {
    position: sticky;
    top: 0px;
    transition: top 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.animated-word {
    opacity: 0;
    animation: fadeIn 0.05s forwards;
}


@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.markdown-container hr {
    border: none;
    border-top: 1px solid rgba(212, 228, 252, 1);
    margin: 16px 0;
}

.markdown-container table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
    margin-bottom: 16px;
    border: 1px solid rgba(212, 228, 252, 1);
    color: rgba(0, 51, 128, 1);
}

.markdown-container th,
.markdown-container td {
    border: 1px solid rgba(212, 228, 252, 1);
    padding: 8px;
    text-align: left;
    vertical-align: top;
}

.markdown-container th {
    background-color: rgba(232, 240, 252, 1);
    font-weight: bold;
}