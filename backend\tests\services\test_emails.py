import pytest
from unittest.mock import patch
from services.emails import (
    send_resend_email,
    template_var_injection,
    send_password_reset,
    FROM_EMAIL,
    RESEND_API_URL,
)


def test_template_var_injection():
    # Test basic template injection
    template = "Hello {{name}}, welcome to {{company}}"
    data = {"name": "<PERSON>", "company": "ImpactAI"}
    result = template_var_injection(template, data)
    assert result == "Hello John, welcome to ImpactAI"

    # Test empty data
    template = "No variables here"
    data = {}
    result = template_var_injection(template, data)
    assert result == "No variables here"

    # Test missing variable in data
    template = "Hello {{name}}"
    data = {}
    result = template_var_injection(template, data)
    assert result == "Hello {{name}}"


@pytest.mark.asyncio
async def test_send_resend_email():
    test_email = "<EMAIL>"
    test_subject = "Test Subject"
    test_content = "<p>Test content</p>"

    expected_headers = {
        "Authorization": "Bearer re_123456789",
        "Content-Type": "application/json",
    }

    expected_data = {
        "from": FROM_EMAIL,
        "to": [test_email],
        "subject": test_subject,
        "html": test_content,
    }

    # Mock the post_json function
    with patch("services.emails.post_json") as mock_post:
        await send_resend_email(test_email, test_subject, test_content)

        # Verify post_json was called with correct arguments
        mock_post.assert_called_once_with(
            url=RESEND_API_URL,
            body=expected_data,
            headers=expected_headers,
            timeout=10.0,
        )


@pytest.mark.asyncio
async def test_send_password_reset():
    test_email = "<EMAIL>"
    test_token = "test-token"
    expected_link = f"https://impact-ai-dev.app/auth?token={test_token}"

    # Mock the template loading and email sending
    with patch(
        "services.emails.FORGOT_PASSWORD_EMAIL_TEMPLATE", "Reset link: {{cta_link}}"
    ), patch("services.emails.send_resend_email") as mock_send:

        await send_password_reset(test_email, test_token)

        # Verify send_resend_email was called with correct arguments
        mock_send.assert_called_once_with(
            to_email=test_email,
            subject="Password Reset Request",
            html_content=f"Reset link: {expected_link}",
        )
