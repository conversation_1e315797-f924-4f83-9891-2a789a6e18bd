import React from "react";
import { <PERSON>, Box, Grid, Typo<PERSON>, Card, CardContent, CardMedia } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface Feature {
    image: string;
    headerLabel: string;
    caption: string;
    description: string;
    url: string;
    altText: string;
}
const NewsAndEventsFrame = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const features: Feature[] = [
        // {
        //     image: "/images/newsandevents/news-1.jpg",
        //     headerLabel: "New Way Now: How the World Bank provides crucial information to global policymakers in seconds",
        //     caption: "News | December 12, 2024",
        //     description: "<PERSON>, World Bank’s DIME AI Program lead, discusses ImpactAI, an open-source tool that uses generative AI to extract insights from policy research, reducing time for literature review and analysis. With thousands of studies added annually, it aims to revolutionize data-driven decision-making in global development.",
        //     url: "https://www.youtube.com/watch?v=VhUQI6Khfws",
        //     altText: "New Way Now - Leaders creating a new way forward with Google Cloud"
        // },
        {
            image: "/images/newsandevents/news-13.jpg",
            headerLabel: "AWF LEADS Workshop",
            caption: "News | August 1, 2025",
            description: "At the recent AFW LEADS Workshop in Togo, the AI Lab deepened collaborations with regional stakeholders, showcased our flagship product ImpactAI, and left with a refreshed vision for evidence-driven, context-aware development.",
            url: "https://dime-ai-test.my.canva.site/afw-leads-2025",
            altText: "AWF LEADS"
        },
        {
            image: "/images/newsandevents/news-12.jpg",
            headerLabel: "Togo Data Lab",
            caption: "News | July 28, 2025",
            description: "The Togo Data Lab is pioneering the use of AI and data tools to improve public services in Togo. At the 2025 LEADS Workshop, the Lab showcased how embedded, locally tailored solutions can support smarter policymaking and facilitate regional learning.",
            url: "https://dime-ai-test.my.canva.site/togo-data-lab-spotlight",
            altText: "Togo Data Lab"
        },
        {
            image: "/images/newsandevents/news-2.jpg",
            headerLabel: "Government AI 100 (2025)",
            caption: "News | December 11, 2024",
            description: "Apolitical's Government AI 100 list celebrates public servants from around the world leading on AI adoption, capacity-building, and regulation to shape the future of this powerful technology. Read how DIME AI is harnessing AI for capacity-building.",
            url: "https://apolitical.co/list/en/government-ai-100-2025#AIcapacityandcapabilitybuilding",
            altText: "Apolitical's Government AI 100 2025 List"
        },
        {
            image: "/images/newsandevents/news-3.jpg",
            headerLabel: "AI for Social Good",
            caption: "News | December 11, 2024",
            description: "Google's webinar on December 2, 2024, covered how the World Bank and other nonprofits are leveraging generative AI to supercharge climate action, education, crisis response, and more. ",
            url: "https://cloudonair.withgoogle.com/events/ai-for-social-good-webinar",
            altText: "Google webinar on AI for social good"
        },
        // {
        //     image: "/images/newsandevents/news-4.jpg",
        //     headerLabel: "Exploring Generative AI for Development Impact: Learnings from a high-level panel, hosted by DIME, supported by Google.org",
        //     caption: "News | October 23, 2024",
        //     description: "The ImpactAI team highlights ley takeaways from the GenAI 4 Development Impact event, which brought together experts from the World Bank, Google, and the public sector to discuss how GenAI can promote solutions to some of the world’s most pressing challenges.",
        //     url: "https://worldbank-dime-ai.my.canva.site/exploring-generative-ai-for-development-impact",
        //     altText: "Panel discussion about Generative AI for development impact"
        // },
        {
            image: "/images/newsandevents/news-5.jpg",
            headerLabel: "GenAI 4 Development Impact",
            caption: "News | October 23, 2024",
            description: "Hosted by the World Bank with support from Google.org, GenAI 4 Development Impact brought together leaders from the World Bank, governments, private sector, academia, and policy to explore the transformative potential of generative AI in tackling global development challenges.",
            url: "https://www.worldbank.org/en/events/2024/10/23/genai-4-development-impact#1",
            altText: "World Bank GenAI for development event banner"
        },
        {
            image: "/images/newsandevents/news-6.jpg",
            headerLabel: "Green shoots, AI roots: Responding to crises and building sustainable futures with gen AI",
            caption: "News | October 22, 2024",
            description: "Nonprofits are building AI models and agents -- like ImpactAI -- to tackle global challenges and uncovering business lessons for organizations everywhere.",
            url: "https://cloud.google.com/transform/gen-ai-crisis-response-sustainability-google-org-nonprofit-ai-incubator/",
            altText: "Windfarms in an open field with a rainbow over them"
        },
        {
            image: "/images/newsandevents/news-7.jpg",
            headerLabel: "AI for Social Good E-Book",
            caption: "News | October 17, 2024",
            description: "Google showcases how nonprofits, including the World Bank's Development Impact Group, are tackling urgent societal issues across climate action, education, crisis response and more.",
            url: "https://cloud.google.com/resources/gen-ai-for-social-good?utm_source=linkedin&utm_medium=unpaidsoc&utm_campaign=FY24-Q3-global-ENDM409-website-dl-gen-ai-for-social-good-ebook&utm_content=google-org-accelerator&utm_term=-",
            altText: "AI for Social Good E-Book"
        },
        {
            image: "/images/newsandevents/news-8.jpg",
            headerLabel: "321 real-world gen AI use cases from the world’s leading organizations",
            caption: "News | September 24, 2024",
            description: "ImpactAI is in good company. Top companies, governments, researchers, and startups are already enhancing their work with Google's AI solutions. ",
            url: "https://cloud.google.com/transform/101-real-world-generative-ai-use-cases-from-industry-leaders?e=********?utm_source=linkedin&utm_medium=unpaidsoc&utm_campaign=fy24q3-googlecloud-blog-ai-in_feed-no-brand-global&utm_content=-&utm_term=-&linkId=********",
            altText: "Real-world generative AI use cases"
        },
        // {
        //     image: "/images/newsandevents/news-9.jpg",
        //     headerLabel: "From Research to Action: Introducing ImpactAI at the Google.org Impact Summit",
        //     caption: "Blog | September 06, 2024",
        //     description: "At the Google.org Impact Summit, the World Bank’s DIME AI team showcased ImpactAI, a chatbot assistant using generative AI to provide evidence to policy makers developing solutions for global challenges.",
        //     url: "https://worldbank-dime-ai.my.canva.site/introducing-impactai-at-the-google-org-impact-summit",
        //     altText: "Woman on stage at Google.org Impact Summit speaking to an audience"
        // },
        {
            image: "/images/newsandevents/news-10.jpg",
            headerLabel: "Measuring Development 2024: AI, the Next Generation",
            caption: "Event | May 02, 2024",
            description: "The World Bank's Development Impact Group (DIME) and Development Data Group (DECDG), the Center for Effective Global Action (CEGA), and the University of Chicago hosted the tenth annual Measuring Development Conference, featuring speakers shaping the way generative AI tools will be adopted and regulated.",
            url: "https://www.worldbank.org/en/events/2024/05/02/measuring-development-2024",
            altText: "Measuring Development 2024: AI, the Next Generation"
        },
        {
            image: "/images/newsandevents/news-11.jpg",
            headerLabel: "21 nonprofits join our first generative AI accelerator",
            caption: "News | March 28, 2024",
            description: "ImpactAI is part of the first cohort of the Google.org Accelerator: Generative AI, which provides mentorship, technical training, and pro bono support to nonprofits working with generative AI.",
            url: "https://blog.google/outreach-initiatives/google-org/google-generative-ai-accelerator-nonprofits/",
            altText: "Members of Google's GenAI accelerator taking a picture outside a Google building on a sunny day"
        }
    ];
    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                boxShadow: 0,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: isMobile ? "48px" : isTablet ? "90px" : "126px",
                borderRadius: "32px",
                background: "none",
            }}
        >
            {/* Header Section */}
            <Box
                sx={{
                    textAlign: "center",
                    display: "flex",
                    justifyContent: "center",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 1
                }}>
                <Typography variant={isMobile ? "h1" : isTablet ? "h1" : "h1"} sx={{ color: theme.palette.text.primary }}>
                    ImpactAI News & Events
                </Typography>
                <Typography variant={isMobile ? "body2" : isTablet ? "body1" : "body1"}
                    sx={{
                        width: isMobile ? "90%" : "60%",
                        textAlign: "center",
                        color: theme.palette.text.primary,
                        fontSize: isMobile ? "12px" : isTablet ? "16px" : "16px"
                    }}>
                    Stay informed on official announcements, media coverage, and public engagements.
                </Typography>
            </Box>
            {/* Main Image Section */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                }}
            >
                <Grid container spacing={2} justifyContent="flex-start">
                    {features.map((feature, index) => (
                        <Grid item xs={12} sm={4} md={3} key={index}>
                            <Card
                                component="a"
                                href={feature.url}
                                target={feature.url.startsWith("https://worldbank") ? "_self" : "_blank"}
                                rel={feature.url.startsWith("https://worldbank") ? "" : "noopener noreferrer"} sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "stretch",
                                    borderRadius: "0px",
                                    p: '0px',
                                    boxShadow: 0,
                                    height: "100%",
                                    background: '#F2F6FC',
                                    textDecoration: 'none',
                                }}
                            >
                                <CardMedia
                                    component="img"
                                    image={feature.image}
                                    alt={feature.altText}
                                    sx={{
                                        width: "100%",
                                        height: "171px",
                                        objectFit: "contain",
                                        borderRadius: 0,
                                    }}
                                />

                                <CardContent
                                    sx={{
                                        flex: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "flex-start",
                                        alignItems: "flex-start",
                                        gap: 1,
                                        p: 2
                                    }}
                                >
                                    <Typography
                                        variant={"body2"}
                                        sx={{
                                            fontSize: isMobile ? "12px" : isTablet ? "10.595px" : "14px",
                                            fontWeight: "700"
                                        }}
                                    >
                                        {feature.headerLabel}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            color: 'rgba(0, 67, 112, 1) !important',
                                            fontSize: isMobile ? "12px" : isTablet ? "11px" : "12px",
                                        }}
                                    >
                                        {feature.caption}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            color: theme.palette.text.primary,
                                            fontSize: isMobile ? "11px" : isTablet ? "10.595px" : "14px",
                                        }}
                                    >
                                        <span dangerouslySetInnerHTML={{ __html: feature.description }} />
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </Paper>
    );
};

export default NewsAndEventsFrame;