import { useRef, useState, useEffect } from "react";
import * as d3 from "d3";
import { Box, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import Link from "./Link";
import LabelBox from "./LabelBox";
import {
  NewForestPlotProps,
  Pair,
  Label,
  Edge,
  HoverState,
} from "./ForestPlotJWT.types";
import "./ForestPlotJWT.css";

const NewForestPlot = ({ data, hoveredPair, onInterventionHover, onOutcomeHover }: NewForestPlotProps) => {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();
  const [svgWidth, setSvgWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  const pairHeight = 30;
  const pairMarginBottom = 14;

  const margin = {
    top: 40,
    right: 20,
    bottom: 0,
    left: 20,
  };

  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;


  const allOutcomes: Label[] = Array.from(
    new Set(data.map((d) => `${d.outcome_id}_${d.outcome}`))
  ).map((d, i) => {
    const [id, label] = d.split("_");


    return {
      id: +id,
      label: label,
      y: i * (pairHeight + pairMarginBottom),
      links: data.filter(d => d.outcome_id === +id)
    };
  });

  const allInterventions: Label[] = Array.from(
    new Set(data.map((d) => `${d.intervention_id}_${d.intervention}`))
  ).map((d, i) => {
    const [id, label] = d.split("_");

    return {
      id: +id,
      label: label,
      y: i * (pairHeight + pairMarginBottom),
      links: data.filter(d => d.intervention_id === +id)
    };
  });

 

  const pairLinks: Edge[] = data.map((d, i) => {
    const from = allOutcomes.find((outcome) => outcome.id === d.outcome_id)
    const to = allInterventions.find((intervention) => intervention.id === d.intervention_id)

    return {
      outcome_id: d.outcome_id,
      outcome: to,
      intervention_id: d.intervention_id,
      intervention: from,
      from: from ? from.y : 0,
      to: to ? to.y : 0,
      details: d
    }
  })

  // console.log('data', data)
  // console.log("allInterventions", allInterventions)
  // console.log("allOutcomes", allOutcomes)
  // console.log("pairLinks", pairLinks)

  function getSymmetricalRange() {
    const minLower = d3.min(data, (d: Pair) => d.aggregate.lower) || 0;

    const maxUpper = d3.max(data, (d: Pair) => d.aggregate.upper) || 0;

    return [
      -Math.max(Math.abs(minLower), Math.abs(maxUpper)),
      Math.max(Math.abs(minLower), Math.abs(maxUpper)),
    ];
  }

  const symmetricalRange = getSymmetricalRange();

  const scaleX = d3.scaleLinear().domain(symmetricalRange).range([0, w]).nice();

  const [hover, setHover] = useState<HoverState>({
    outcomes: [],
    interventions: [],
  });

  function setHoverByOutcome(outcome_id: number) {
    const hoverData = {
      outcomes: [outcome_id],
      interventions: data
        .filter((d: Pair) => d.outcome_id === outcome_id)
        .map((d: Pair) => d.intervention_id),
    }
    setHover(hoverData);
  }

  function setHoverByIntervention(intervention_id: number) {
    const hoverData = {
      interventions: [intervention_id],
      outcomes: data
        .filter((d: Pair) => d.intervention_id === intervention_id)
        .map((d: Pair) => d.outcome_id),
    }
    setHover(hoverData);
  }

  function resetHover() {
    const hoverData = {
      interventions: [],
      outcomes: [],
    }
    setHover(hoverData);
  }

  return (
    <Box className="forest-plot" ref={containerRef}>
      <div style={{ position: "relative" }}>
        <svg
          width={svgWidth}
          height={
            Math.max(allInterventions.length * (pairHeight + pairMarginBottom), allOutcomes.length * (pairHeight + pairMarginBottom)) +
            margin.top +
            margin.bottom
          }
        >
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            <g
              style={{
                fill: "rgb(0 34 68 / 87%)",
                fontWeight: "bold",
                textAnchor: "middle",
              }}
              transform="translate(0, -12)"
            >
              <text x={70}>Intervention</text>
              <text x={w - 70}>Outcome</text>
            </g>
            <g className="pairLinks">
              {pairLinks.map((link) => (
                <Link
                  from={[scaleX.range()[1] - 140, 20 + link.from]}
                  to={[
                    scaleX.range()[0] / 2 + 140,
                    20 + link.to,
                  ]}
                  isHovered={
                    (hover.outcomes.includes(link.outcome_id) &&
                    hover.interventions.includes(link.intervention_id)) || (hoveredPair?.outcome_id === link.outcome_id && hoveredPair?.intervention_id === link.intervention_id)
                  }
                  isNothingHovered={
                    (hover.outcomes.length === 0 &&
                    hover.interventions.length === 0)
                  }
                  details={link.details}
                />
              ))}
            </g>
            <g className="interventions">
              {allInterventions.map((intervention) => (
                <LabelBox
                  onLabelPointerOver={() => {
                    setHoverByIntervention(intervention.id)
                    onInterventionHover(intervention.id)
                  }}
                  onLabelPointerOut={() => {
                    resetHover()
                    onInterventionHover(undefined)
                  }}
                  pairMarginBottom={pairMarginBottom}
                  pairHeight={pairHeight}
                  isHovered={hover.interventions.includes(intervention.id) || hoveredPair?.intervention_id === intervention.id}
                  isNothingHovered={
                    hover.interventions.length === 0 &&
                    hover.outcomes.length === 0 && !hoveredPair
                  }
                  label={intervention}
                  x={0}
                />
              ))}
            </g>
            <g className="outcomes">
              {allOutcomes.map((outcome) => (
                <LabelBox
                  onLabelPointerOver={() => {
                    setHoverByOutcome(outcome.id)
                    onOutcomeHover(outcome.id)
                  }
                  }
                  onLabelPointerOut={() => {
                    resetHover()
                    onOutcomeHover(outcome.id)
                  }}
                  pairMarginBottom={pairMarginBottom}
                  pairHeight={pairHeight}
                  isHovered={hover.outcomes.includes(outcome.id) || hoveredPair?.outcome_id === outcome.id}
                  isNothingHovered={
                    hover.interventions.length === 0 &&
                    hover.outcomes.length === 0 && !hoveredPair
                  }
                  label={outcome}
                  x={scaleX.range()[1] - 140}
                />
              ))}
            </g>
          </g>
        </svg>
      </div>
      <Box
        ref={tooltipRef}
        className="tooltip"
        sx={{
          background: theme.palette.background.paper,
          color: theme.palette.text.primary,
          border: "1px solid #0091C2",
          boxShadow: "none",
        }}
      >
        <Typography id="title"></Typography>
      </Box>
    </Box>
  );
};

export default NewForestPlot;
