import { useRef, useEffect, useState } from "react";
import * as d3 from "d3";

import "./StudiesYearPlot.css";

const StudiesYearPlot = ({ plotData, chartWidth }) => {
  console.log("STUDIES YEAR PLOT DATA", plotData);

  const uniqueStudies = plotData;

  console.log(d3.groups(plotData, (d) => d.pulication_year));
  const byYear = d3
    .groups(uniqueStudies, (d) => d.pulication_year)
    .sort((a, b) => b[1].length - a[1].length);
  console.log(byYear);

  const byYear2 = d3.group(uniqueStudies, (d) => d.pulication_year);

  const yearExtent = d3.extent(byYear, (d) => d[0]);
  const allYears = d3
    .range(yearExtent[0], yearExtent[1] + 1)
    .sort((a, b) => a[0] - b[0])
    .map((d) => [d, byYear2.get(d) || []]);

  const minYear = Math.floor(yearExtent[0] / 5) * 5;
  const maxYear = (Math.floor(new Date().getFullYear() / 5) + 1) * 5
  const binSize = 5;
  const thresholds = d3.range(minYear, maxYear + binSize, binSize);

  const bin = d3
    .bin()
    .domain([minYear, maxYear])
    .value((d) => d.pulication_year)
    .thresholds(thresholds);

  console.log(
    "HAHAHA",
    yearExtent,
    minYear,
    maxYear,
    thresholds,
    bin(uniqueStudies)
  );

  const margin = {
    top: 20,
    right: 60,
    bottom: 20,
    left: 20,
  };

  const height = 200;

  const w = chartWidth - margin.left - margin.right;
  const h = height - margin.top - margin.bottom;

  const timeScale = d3
    .scaleTime()
    .domain([new Date(minYear, 0, 1), new Date(maxYear, 0, 1)])
    .range([0, w]);

  const yScale = d3
    .scaleLinear()
    .domain([0, d3.max(byYear, (d) => Math.max(10, d[1].length))])
    .range([0, h]);

  return (
    <div className="studies-year-plot container">
      <svg width={chartWidth} height={height}>
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {bin(uniqueStudies).map((d) => (
            <g transform={`translate(${timeScale(new Date(d.x0, 0, 1))}, 0)`}>
              <rect
                width={
                  timeScale(new Date(d.x1, 0, 1)) -
                  timeScale(new Date(d.x0, 0, 1)) -
                  1
                }
                y={h - yScale(d.length)}
                height={yScale(d.length)}
                fill="black"
              />
              {d.length > 0 && <text
                y={h - yScale(d.length)}
                dy={-4}
                x={
                  (timeScale(new Date(d.x1, 0, 1)) -
                    timeScale(new Date(d.x0, 0, 1))) /
                  2
                }
                style={{
                  textAnchor: "middle",
                  fontSize: 12,
                  fill: "rgb(22 54 97)",
                }}
              >
                {d.length}
              </text>}
            </g>
          ))}
          <g>
            {/* <line x1={0} x2={0} y1={h} y2={0} /> */}
            <line x1={0} x2={w} y1={h} y2={h} />
            {timeScale.ticks(5).map((tick) => (
              <g
                className="tick"
                transform={`translate(${timeScale(tick)}, ${h})`}
              >
                <line y2={4} />
                <text dy={20}>{tick.getFullYear()}</text>
              </g>
            ))}
          </g>
        </g>
      </svg>
    </div>
  );
};

export default StudiesYearPlot;
