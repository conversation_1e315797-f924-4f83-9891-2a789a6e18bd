import os


def get_env() -> str:
    """
    Get the current environment from the TARGET environment variable.
    Valid environments are: local, development, testing, production, and experimental.
    Raises:
        ValueError: If the environment is invalid or not recognized.
    """
    env = os.getenv("TARGET", "development")
    if env not in ["local", "development", "testing", "production", "experimental"]:
        raise ValueError(f"Invalid environment: {env}")
    return env


def get_el_tool_url() -> str:
    """
    Get the URL for the entity linking tool based on the environment.
    The URL can be overridden by the ENTITY_API_URL environment variable.
    If the environment variable is not set, it defaults to a specific URL based on the environment
    Valid environments are: local, experimental, development, testing, and production.
    Raises ValueError if the environment is invalid.

    Returns:
        str: The URL for the entity linking tool.
    Raises:
        ValueError: If the environment is invalid or not recognized.
    """

    override_url = os.getenv("ENTITY_API_URL", None)
    if override_url:
        return override_url

    env = get_env()

    el_tool_urls = {
        "local": "https://entity-linking-api-dev-564807556547.us-central1.run.app/entity-linking",
        "experimental": "https://impactai-tool-entity-linking-development-564807556547.us-central1.run.app/entity-linking",
        "development": "https://entity-linking-api-dev-564807556547.us-central1.run.app/entity-linking",
        "testing": "https://entity-linking-api-dev-564807556547.us-central1.run.app/entity-linking",
        "production": "https://entity-linking-api-dev-564807556547.us-central1.run.app/entity-linking",
    }

    if env in el_tool_urls:
        return el_tool_urls[env]

    raise ValueError(f"Invalid environment: {env}")


def get_da_tool_url() -> str:
    """
    Get the URL for the data analysis tool based on the environment.
    The URL can be overridden by the DATA_ANALYSIS_API_URL environment variable.
    If the environment variable is not set, it defaults to a specific URL based on the environment
    Valid environments are: local, experimental, development, testing, and production.
    Raises ValueError if the environment is invalid.

    Returns:
        str: The URL for the data analysis tool.
    Raises:
        ValueError: If the environment is invalid or not recognized.
    """

    override_url = os.getenv("DATA_ANALYSIS_API_URL", None)

    if override_url:
        return override_url

    env = get_env()

    da_tool_urls = {
        "local": "https://data-analysis-din4qs2qka-uc.a.run.app/analyze",
        "experimental": "https://impactai-tool-data-analysis-v1-experimental-564807556547.us-central1.run.app/analyze",
        "development": "https://data-analysis-din4qs2qka-uc.a.run.app/analyze",
        "testing": "https://data-analysis-din4qs2qka-uc.a.run.app/analyze",
        "production": "https://data-analysis-din4qs2qka-uc.a.run.app/analyze",
    }

    if env in da_tool_urls:
        return da_tool_urls[env]

    raise ValueError(f"Invalid environment: {env}")


def get_rag_tool_url() -> str:
    """
    Get the URL for the RAG tool based on the environment.
    The URL can be overridden by the RAG_API_URL environment variable.
    If the environment variable is not set, it defaults to a specific URL based on the environment
    Valid environments are: local, experimental, development, testing, and production.
    Raises ValueError if the environment is invalid.

    Returns:
        str: The URL for the RAG tool.
    Raises:
        ValueError: If the environment is invalid or not recognized.
    """

    override_url = os.getenv("RAG_API_URL", None)

    if override_url:
        return override_url

    env = get_env()

    rag_tool_urls = {
        "local": "https://rag-qdrant-api-564807556547.us-central1.run.app",
        "experimental": "https://rag-qdrant-api-564807556547.us-central1.run.app",
        "development": "https://rag-qdrant-api-564807556547.us-central1.run.app",
        "testing": "https://rag-qdrant-api-564807556547.us-central1.run.app",
        "production": "https://rag-qdrant-api-564807556547.us-central1.run.app",
    }
    if env in rag_tool_urls:
        return rag_tool_urls[env]

    raise ValueError(f"Invalid environment: {env}")


def get_summarizer_tool_url() -> str:
    """
    Get the URL for the summarizer tool based on the environment.
    The URL can be overridden by the SUMMARIZER_API_URL environment variable.
    If the environment variable is not set, it defaults to a specific URL based on the environment.
    Valid environments are: local, experimental, development, testing, and production.
    Raises ValueError if the environment is invalid.

    Returns:
        str: The URL for the summarizer tool.
    Raises:
        ValueError: If the environment is invalid or not recognized.
    """

    override_url = os.getenv("SUMMARIZER_API_URL", None)

    if override_url:
        return override_url

    env = get_env()

    summarizer_tool_urls = {
        "local": "https://final-answer-564807556547.us-central1.run.app/final_answer",
        "experimental": "https://impactai-tool-summarizer-experimental-564807556547.us-central1.run.app/summarize",
        "development": "https://final-answer-564807556547.us-central1.run.app/final_answer",
        "testing": "https://final-answer-564807556547.us-central1.run.app/final_answer",
        "production": "https://final-answer-564807556547.us-central1.run.app/final_answer",
    }
    if env in summarizer_tool_urls:
        return summarizer_tool_urls[env]

    raise ValueError(f"Invalid environment: {env}")
