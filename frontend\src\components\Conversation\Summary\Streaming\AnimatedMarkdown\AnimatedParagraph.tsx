import React, { useState, useRef, useEffect, useCallback } from 'react';
import LinkComponent from "../LinkComponent";
import AnimatedWord from "./AnimatedWord";
import { Source } from "../../../../../types/ConversationTypes";

interface AnimatedParagraphProps {
  children: React.ReactNode;
  onComplete: () => void;
  messageId?: string;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  sources?: Source[];
  plotData?: any;
}

const AnimatedParagraph = React.memo(({
  children,
  onComplete,
  messageId,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  sources,
  plotData
}: AnimatedParagraphProps) => {
  const [animatedWords, setAnimatedWords] = useState<Set<number>>(new Set());
  const totalWordsRef = useRef(0);
  const [content, setContent] = useState<
    {
      word: string;
      emphasized: boolean;
      bold: boolean;
      isLink: boolean;
      index: number;
      element?: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
    }[]
  >([]);
  const animationSpeed = 0.02;

  useEffect(() => {
    const processContent = (items: React.ReactNode) => {
      let processedContent: {
        word: string;
        emphasized: boolean;
        bold: boolean;
        isLink: boolean;
        index: number;
        element?: React.ReactElement<any, string | React.JSXElementConstructor<any>>;
      }[] = [];
      const process = (item: React.ReactNode, formatting: { emphasized?: boolean; bold?: boolean } = {}) => {
        if (Array.isArray(item)) {
          item.forEach(child => process(child, formatting));
        } else if (React.isValidElement(item)) {
          const { type, props } = item;
          if (type === 'em' || type?.name === 'AnimatedEm') {
            process(props.children, { ...formatting, emphasized: true });
          } else if (type === 'strong') {
            process(props.children, { ...formatting, bold: true });
          } else if (type === 'a') {
            processedContent.push({
              element: item,
              isLink: true,
              index: processedContent.length,
              word: '',
              emphasized: false,
              bold: false,
            });
          } else if (props?.children) {
            process(props.children, formatting);
          }
        } else if (typeof item === 'string') {
          const parts = item.split(/(\*\*([^*]+)\*\*|\*([^*]+)\*)/g).filter(Boolean);
          parts.forEach(part => {
            let word = part;
            let emphasized = formatting.emphasized || false;
            let bold = formatting.bold || false;

            if (part.startsWith('**') && part.endsWith('**')) {
              word = part.slice(2, -2);
              bold = true;
            } else if (part.startsWith('*') && part.endsWith('*')) {
              word = part.slice(1, -1);
              emphasized = true;
            }

            word.split(/(\s+)/).filter(Boolean).forEach(w => {
              processedContent.push({
                word: w,
                emphasized,
                bold,
                isLink: false,
                index: processedContent.length,
              });
            });
          });
        }
      };
      process(children);
      return processedContent;
    };

    const processedContent = processContent(children);
    totalWordsRef.current = processedContent.filter(item => !item.isLink).length;
    setContent(processedContent);
    setAnimatedWords(new Set());
  }, [children]);

  const handleWordAnimated = useCallback((index: number) => {
    setAnimatedWords(prev => {
      const next = new Set(prev);
      next.add(index);
      if (next.size === totalWordsRef.current) {
        requestAnimationFrame(() => onComplete());
      }
      return next;
    });
  }, [onComplete]);

  return (
    <div className="animated-paragraph-container">
      {content.map((item) => {
        if (item.isLink) {
          return (
            <span
              key={`link-${item.index}`}
              className="animated-word"
              style={{ animationDelay: `${item.index * animationSpeed}s` }}
            >
              <LinkComponent
                {...item.element?.props}
                messageId={messageId || ''}
                onViewOnPlotClicked={onViewOnPlotClicked}
                onViewOnSourceClicked={onViewOnSourceClicked}
                plotData={plotData}
              />
            </span>
          );
        } else {
          return (
            <React.Fragment key={`${item.word}-${item.index}`}>
              {item.emphasized ? (
                <em>
                  <AnimatedWord
                    word={item.word}
                    delay={item.index * animationSpeed}
                    onAnimated={() => handleWordAnimated(item.index)}
                    isEmphasized={true}
                    isBold={item.bold}
                  />
                </em>
              ) : item.bold ? (
                <strong>
                  <AnimatedWord
                    word={item.word}
                    delay={item.index * animationSpeed}
                    onAnimated={() => handleWordAnimated(item.index)}
                    isEmphasized={false}
                    isBold={true}
                  />
                </strong>
              ) : (
                <AnimatedWord
                  word={item.word}
                  delay={item.index * animationSpeed}
                  onAnimated={() => handleWordAnimated(item.index)}
                  isEmphasized={false}
                  isBold={false}
                />
              )}
            </React.Fragment>
          );
        }
      })}
    </div>
  );
});

export default AnimatedParagraph;