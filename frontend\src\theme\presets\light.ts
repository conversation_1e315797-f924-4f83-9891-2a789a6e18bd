import { createTheme } from '@mui/material/styles';

const lightTheme = createTheme({
  breakpoints: {
    values: {
      xs: 0,    // 0px to 599px
      sm: 600,  // 600px to 899px
      md: 900,  // 900px to 1199px
      lg: 1200, // 1200px to 1535px
      lg_wide: 1366,
      xl: 1536, // 1536px to 1919px
      xxl: 1920, // 1920px to 2559px
      uhd: 2560, // 2560px to 3839px
      '4k': 3840, // 3840px and up
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      'Helvetica',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    h2: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    h3: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    h4: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    h5: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    h6: {
      fontFamily: 'HostGrotesk, Roboto, Helvetica',
      fontWeight: '400'
    },
    button: {
      fontFamily: 'HostGrotesk,  Roboto, Helvetica',
      fontSize: '15px',
      fontWeight: '500',
    }
  },
  palette: {
    mode: 'light',
    divider: 'rgba(212, 228, 252, 1)', // Converted from '#D4E4FC' with 50% opacity
    text: {
      primary: 'rgba(0, 51, 128, 1)', // '#003380' 100%
      secondary: 'rgba(0, 51, 128, 0.7)', // '#003380' 70%
      disabled: 'rgba(0, 51, 128, 0.38)', // '#003380' 38%
      hover: 'rgba(0, 51, 128, 0.04)', // '#003380' 4%
      selected: 'rgba(0, 51, 128, 0.08)', // '#003380' 8%
      focus: 'rgba(0, 51, 128, 0.12)', // '#003380' 12%
      focusVisible: 'rgba(0, 51, 128, 0.3)', // '#003380' 30%
    },
    primary: {
      main: 'rgba(0, 71, 178, 1)', // '#0047B2' 100%
      dark: 'rgba(0, 51, 128, 1)', // '#003380' 100%
      light: 'rgba(71, 143, 252, 1)', // '#478FFC' 100%
      contrast: 'rgba(255, 255, 255, 1)', // '#FFFFFF' 100%
      hover: 'rgba(0, 71, 178, 0.09)', // '#0047B2' 9%
      selected: 'rgba(0, 71, 178, 0.09)', // '#0047B2' 9%
      focus: 'rgba(0, 71, 178, 0.12)', // '#0047B2' 12%
      focusVisible: 'rgba(0, 71, 178, 0.22)', // '#0047B2' 22%
      outlinedBorder: 'rgba(0, 71, 178, 0.5)', // '#0047B2' 50%
    },
    secondary: {
      main: 'rgba(131, 179, 252, 1)', // '#83B3FC' 100%
      dark: 'rgba(0, 92, 229, 1)', // '#005CE5' 100%
      light: 'rgba(212, 228, 252, 1)', // '#D4E4FC' 100%
      contrast: 'rgba(255, 255, 255, 1)', // '#FFFFFF' 100%
      hover: 'rgba(171, 204, 252, 0.09)', // '#ABCCFC' 9%
      selected: 'rgba(171, 204, 252, 0.09)', // '#ABCCFC' 9%
      focus: 'rgba(131, 179, 252, 0.12)', // '#83B3FC' 12%
      focusVisible: 'rgba(131, 179, 252, 0.22)', // '#83B3FC' 22%
      outlinedBorder: 'rgba(131, 179, 252, 0.50)', // '#83B3FC' 50%
    },
    action: {
      // The color of an active action like an icon button.
      default: 'rgba(0, 71, 178, 0.56)', // '#0047B2' 56%
      active: 'rgba(0, 71, 178, 1)', // '#0047B2' 100%
      hover: 'rgba(158, 198, 251, 0.65)', // '#9EC6FB' 65%
      selected: 'rgba(158, 198, 251, 0.09)', // '#9EC6FB' 9%
      focus: 'rgba(158, 198, 251, 0.55)', // '#9EC6FB' 55%
      disabled: 'rgba(22, 54, 97, 0.38)', // '#163661' 38%
      disabledBackground: 'rgba(212, 228, 252, 0.18)', // '#D4E4FC' 18%
    },
    error: {
      main: 'rgba(233, 59, 51, 1)', // E93B33 100%
      dark: 'rgba(175, 44, 38, 1)', // AF2C26 100%
      light: 'rgba(244, 157, 153, 1)', // F49D99 100%
      contrast: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
      hover: 'rgba(233, 59, 51, 0.10)', // E93B33 10%
      selected: 'rgba(233, 59, 51, 0.15)', // E93B33 15%
      focusVisible: 'rgba(233, 59, 51, 0.30)', // E93B33 30%
      outlinedBorder: 'rgba(233, 59, 51, 0.50)', // E93B33 50%
    },
    warning: {
      main: 'rgba(241, 157, 56, 1)', // F19D38 100%
      dark: 'rgba(181, 118, 42, 1)', // B5762A 100%
      light: 'rgba(248, 206, 155, 1)', // F8CE9B 100%
      contrast: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
      hover: 'rgba(241, 157, 56, 0.10)', // F19D38 10%
      selected: 'rgba(241, 157, 56, 0.15)', // F19D38 15%
      focusVisible: 'rgba(241, 157, 56, 0.30)', // F19D38 30%
      outlinedBorder: 'rgba(241, 157, 56, 0.50)', // F19D38 50%
    },
    info: {
      main: 'rgba(0, 176, 255, 1)', // 00B0FF 100%
      dark: 'rgba(26, 135, 204, 1)', // 1A87CC 100%
      light: 'rgba(196, 236, 254, 1)', // C4ECFE 100%
      contrast: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
      hover: 'rgba(0, 176, 255, 0.10)', // 00B0FF 10%
      selected: 'rgba(0, 176, 255, 0.15)', // 00B0FF 15%
      focusVisible: 'rgba(0, 176, 255, 0.30)', // 00B0FF 30%
      outlinedBorder: 'rgba(0, 176, 255, 0.50)', // 00B0FF 50%
    },
    success: {
      main: 'rgba(102, 189, 80, 1)', // 66BD50 100%
      dark: 'rgba(51, 95, 40, 1)', // 335F28 100%
      light: 'rgba(178, 222, 167, 1)', // B2DEA7 100%
      contrast: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
      hover: 'rgba(102, 189, 80, 0.10)', // 66BD50 10%
      selected: 'rgba(102, 189, 80, 0.15)', // 66BD50 15%
      focusVisible: 'rgba(102, 189, 80, 0.30)', // 66BD50 30%
      outlinedBorder: 'rgba(102, 189, 80, 0.50)', // 66BD50 50%
    },
    background: {
      paper: 'rgba(245, 249, 254, 1)', // F5F9FE 100%
      default: 'rgba(255, 255, 255, 1)', // FFFFFF 100%
      paperElevationZero: 'rgba(245, 249, 254, 1)', // F5F9FE 100%
      paperElevationTwo: 'rgba(232, 240, 252, 1)', // #E8F0FC 100%
      paperElevationSixteen: 'rgba(212, 228, 252, 1)', // D4E4FC 100%
      paperElevationTwentyFour: 'rgba(171, 204, 252, 1)', // F8FAFC 100%
    },
  },
  components: {
    icon: {
      default: 'rgba(0, 71, 178, 1)', // '#6FA1CB' 100%
      defaultLight: 'rgba(0, 51, 128, 0.6)', // #003380, 0.6
      hoverBold: 'rgba(0, 71, 178, 1)', // #6FA1CB, 100%
      hover: 'rgba(67, 97, 122, 1)', // '#43617A' 100%
      disabled: 'rgba(171, 204, 252, 1)', // '#ABCCFC' 100%
      backgroundDisabled: 'rgba(212, 228, 252, 1)', // '#D4E4FC' 100%
      backgroundIconDisabled: 'rgba(22, 54, 97, 1)', // '#163661' 100%,
      backgroundDefaultFill: 'rgba(242, 246, 252, 1)', // '#F2F6FC' 100%
      backgroundHover: 'rgba(232, 240, 252, 1)', // '#E8F0FC' 100%
    },
    switch: {
      knobSlideFill: 'rgba(96, 160, 245, 1)', // '#60A0F5' 100%
      knobFillEnabled: 'rgba(96, 160, 245, 1)', // '#60A0F5' 100%
      knobFillHovered: 'rgba(136, 184, 247, 1)', // '#88B8F7' 100%
      knobFillFocused: 'rgba(56, 136, 242, 1)', // '#3888F2' 100%
      knobFillDisabled: 'rgba(235, 243, 254, 1)', // '#EBF3FE' 100%
      knobFillFalse: 'rgba(175, 207, 250, 1)', // '#AFCFFA' 100%
    },
    avatar: {
      fill: 'rgba(0, 71, 178, 1)', // '#0047B2' 100%
    },
    input: {
      standard: {
        hoverBorder: 'rgba(131, 179, 252, 1)', // '#83B3FC' 100%
        enabledBorder: 'rgba(55, 137, 247, 0.5)', // '#3789F7' 50%
      },
      outlined: {
        hoverBorder: 'rgba(131, 179, 252, 1)', // '#83B3FC' 100%
        enabledBorder: 'rgba(212, 228, 252, 1)', // '#D4E4FC' 50%
        focusedBorder: 'rgba(71, 143, 252, 1)', // '#478FFC' 100%
        disabledBorder: 'rgba(232, 240, 252, 1)', // '#E8F0FC' 50%
      },
      filled: {
        enabledFill: 'rgba(242, 246, 252, 1).', // '#F2F6FC' 100%
        hoverFill: 'rgba(215, 231, 252, 0.65)', // '#D7E7FC' 65%
        fillDefault: 'rgba(215, 231, 253, 0.5)', // '#D7E7FD' 50%
        disabled: 'rgba(215, 231, 253, 0.25)', // '#D7E7FD' 25%
      },
    },
    rating: {
      enabledBorder: 'rgba(175, 207, 250, 1)', // '#AFCFFA' 100%
      activeFill: 'rgba(255, 180, 0, 1)', // '#FFB400' 100%
    },
    tooltip: {
      fill: 'rgba(101, 105, 108, 1)', // '#65696C' 100%
    },
    snackbar: {
      fill: 'rgba(71, 143, 252, 1)', // '#478FFC' 100%
    },
    chip: {
      defaultCloseFill: 'rgba(0, 92, 229, 1)', // '#005CE5' 100%
      defaultHoverFill: 'rgba(131, 179, 252, 1)', // '#83B3FC' 100%
      defaultEnabledBorder: 'rgba(131, 179, 252, 1)', // '#83B3FC' 100%
      defaultFocusFill: 'rgba(171, 204, 252, 1)' // '#ABCCFC' 100%
    }
  },
  sidebar: {
    secondary: 'rgba(247, 252, 255, 1)', // '#EBF2FC' 100%
    secondaryHover: 'rgba(89, 129, 162, 1)', // '#5981A2' 100%
    secondaryFocused: 'rgba(44, 64, 81, 1)', // '#2C4051' 100%
  },
  common: {
    black: {
      main: 'rgba(67, 97, 122, 1)', // 43617A 100%
      hover: 'rgba(44, 64, 81, 0.04)', // 2C4051 4%
      selected: 'rgba(44, 64, 81, 0.08)', // 2C4051 8%
      focus: 'rgba(44, 64, 81, 0.12)', // 2C4051 12%
      focusVisible: 'rgba(44, 64, 81, 0.3)', // 2C4051 30%
      outlinedBorder: 'rgba(44, 64, 81, 0.5)', // 2C4051 50%
    },
    white: {
      main: 'rgba(255, 255, 255, 1)', // FFF 100%
      hover: 'rgba(250, 250, 250, 0.04)', // FAFAFA 4%
      selected: 'rgba(250, 250, 250, 0.08)', // FAFAFA 8%
      focus: 'rgba(250, 250, 250, 0.12)', // FAFAFA 12%
      focusVisible: 'rgba(250, 250, 250, 0.3)', // FAFAFA 30%
      outlinedBorder: 'rgba(250, 250, 250, 0.5)', // FAFAFA 50%
    }
  },
  brandVisuals: {
    fiveHundred: 'rgba(169, 199, 224, 1)', // '#A9C7E0' 100%
    threeHundred: 'rgba(197, 217, 234, 1)', // '#C5D9EA' 100%
    twoHundred: 'rgba(226, 236, 245, 1)', // '#D2ECF5' 100%
    oneHundred: 'rgba(241, 246, 250, 1)', // '#F1F6FA' 100% 
  },
  elevation: {
    outlined: 'rgba(227, 232, 235, 1)', // '#E3E8EB' 100%
    default: 'rgba(197, 217, 234, 1)', // '#C5D9EA' 100%
    paperElevation: 'rgba(245, 249, 254, 1)', // '#F9FBFF' 100%
    paperElevationZero: 'rgba(242, 246, 252, 1)', // '#F2F6FC' 100%
    paperElevationTwo: 'rgba(232, 240, 252, 1)', // '#E8F0FC' 100%
    paperElevationSixteen: 'rgba(212, 228, 252, 1)', // '#D4E4FC' 100%
    paperElevationTwentyFour: 'rgba(171, 204, 252, 1)', // '#ABCCFC' 100%
  }
});

export default lightTheme;