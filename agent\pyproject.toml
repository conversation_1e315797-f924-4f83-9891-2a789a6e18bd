[tool.poetry]
name = "impactai-agent"
version = "0.1.0"
description = "An intelligent agent for data analysis and insights generation"
authors = ["agombert <<EMAIL>>"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "~0.100.0"
uvicorn = "^0.27.1"
requests = "^2.32.0"
sqlalchemy = "^2.0.28"
pymysql = "^1.1.1"
cryptography = "^44.0.1"
pandas = "^2.2.1"
dataclasses-json = "^0.6.4"
jinja2 = "^3.1.6"
databases = "^0.9.0"
aiomysql = "^0.2.0"
structlog = "^24.4.0"
openai = "^1.55.3"
pyjwt = "^2.10.1"
python-dotenv = "^1.0.1"
langchain = ">=0.1.20,<0.4.0"
langchain-google-genai = ">=0.0.8,<3.0.0"
pydantic = "^2.6.3"
plotly = ">=5.19.0,<7.0.0"
networkx = "^3.2.1"
google-cloud-secret-manager = "^2.18.0"
google-cloud-storage = ">=2.14.0,<4.0.0"
google-cloud = "^0.34.0"
cloud-sql-python-connector = "^1.7.0"
google-auth = ">=2.28.1"
google-auth-oauthlib = ">=1.2.0"
google-auth-httplib2 = ">=0.1.1"
faiss-cpu = "^1.7.4"
langchain-core = ">=0.1.53,<0.4.0"
langchain-community = ">=0.0.38,<0.4.0"
vertexai = "^1.71.1"
websockets = "^15.0.1"
matplotlib = "^3.10.1"
seaborn = "^0.13.2"
rich = "^14.0.0"
httpx = "^0.28.1"
aiohttp = "^3.12.14"
redis = "^5.0.2"
litellm = "^1.74.9.post1"
google-api-core = "^2.25.1"
google = "^3.0.0"
google-genai = "^1.27.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.1.1"
black = ">=24.2.0,<26.0.0"
ruff = ">=0.3.0,<0.10.0"
pre-commit = ">=3.6.2,<5.0.0"
pytest-cov = ">=4.1.0,<7.0.0"
ipykernel = "^6.29.5"
jupyter = "^1.0.0"
nbformat = "^5.9.2"
pytest-asyncio = ">=0.23.5,<0.26.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.ruff]
line-length = 88
target-version = "py311"
