import React, { useState, useEffect, useRef } from "react";
import { Box, Chip, useTheme } from "@mui/material";
import { Message } from "../../../types/ConversationTypes";
import { useIsMobile } from "../../Layout/MobileUtils";

interface UserAnswerDisplayProps {
    answer: Message;
    isTwoColumnLayoutWithPanelOpen: boolean;
}

const CHARACTER_LIMIT_DEFAULT = 65;
const CHARACTER_LIMIT_TWO_COLUMN_PANEL_OPEN = 40;
const MULTI_LINE_HEIGHT_THRESHOLD = 40;

const UserAnswerDisplay: React.FC<UserAnswerDisplayProps> = ({
    answer,
    isTwoColumnLayoutWithPanelOpen
}) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const chipRef = useRef<HTMLDivElement>(null);
    const [chipBorderRadius, setChipBorderRadius] = useState<'20px' | '100px'>('100px');
    const [alignmentStyle, setAlignmentStyle] = useState<"flex-start" | "center">("center");

    const effectiveCharacterLimit = isTwoColumnLayoutWithPanelOpen
        ? CHARACTER_LIMIT_TWO_COLUMN_PANEL_OPEN
        : CHARACTER_LIMIT_DEFAULT;

    useEffect(() => {
        if (chipRef.current) {
            const chipHeight = chipRef.current.offsetHeight;
            if (chipHeight > MULTI_LINE_HEIGHT_THRESHOLD) {
                setChipBorderRadius('20px');
                setAlignmentStyle('flex-start');
            } else {
                const isLongTextByCharacterCount = answer.text && answer.text.length > effectiveCharacterLimit;
                setChipBorderRadius(isLongTextByCharacterCount ? '20px' : '100px');
                setAlignmentStyle(isLongTextByCharacterCount ? 'flex-start' : 'center');
            }
        } else if (answer?.text?.length <= effectiveCharacterLimit) {
            setChipBorderRadius('100px');
            setAlignmentStyle('center');
        } else {
            setChipBorderRadius('20px');
            setAlignmentStyle('flex-start');
        }
    }, [answer?.text, isMobile, theme, effectiveCharacterLimit]);

    if (!answer?.text?.length) {
        return null;
    }

    return (
        <Box sx={{ width: '100%', p: 0, m: 0, display: 'flex', flexDirection: 'column', justifyContent: alignmentStyle }}>
            <Box sx={{ textAlign: 'end' }} ref={chipRef}>
                <Chip
                    label={answer.text}
                    sx={{
                        backgroundColor: theme.elevation.paperElevationTwo,
                        color: theme.palette.text.primary,
                        padding: "10px 20px",
                        textTransform: "none",
                        borderRadius: chipBorderRadius,
                        justifyContent: 'flex-end',
                        textAlign: 'left',
                        fontSize: '16px',
                        height: isMobile ? 'auto' : (chipBorderRadius === '100px' && answer?.text?.length <= effectiveCharacterLimit ? '40px' : 'auto'),
                        maxWidth: isMobile
                            ? '100%'
                            : (isTwoColumnLayoutWithPanelOpen
                                ? '100%'
                                : '100%'),
                        "& .MuiChip-label": {
                            p: 0,
                            wordBreak: 'break-word',
                            whiteSpace: 'normal',
                            overflow: 'visible',
                            textOverflow: 'unset',
                        }
                    }}
                />
            </Box>
        </Box>
    );
};

export default UserAnswerDisplay;