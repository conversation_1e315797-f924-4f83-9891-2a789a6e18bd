import React, { useCallback, useMemo, useState, useRef } from 'react';
import Markdown from 'markdown-to-jsx';
import { replaceTags } from "./Utils";
import AnimatedHeader from "./AnimatedMarkdown/AnimatedHeader";
import AnimatedParagraph from "./AnimatedMarkdown/AnimatedParagraph";
import AnimatedList from "./AnimatedMarkdown/AnimatedList";
import AnimatedEm from "./AnimatedMarkdown/AnimatedEm";
import AnimatedHr from "./AnimatedMarkdown/AnimatedHr";
import AnimatedTable from "./AnimatedMarkdown/AnimatedTable/AnimatedTable";
import AnimatedTableHeaderCell from "./AnimatedMarkdown/AnimatedTable/AnimatedTableHeaderCell";
import AnimatedTableCell from "./AnimatedMarkdown/AnimatedTable/AnimatedTableCell";
import { Source } from "../../../../types/ConversationTypes";

interface MemoizedMarkdownProps {
  text: string;
  onComplete: () => void;
  sources?: Source[];
  plotData?: any;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  messageId?: string;
}

const MemoizedMarkdown = React.memo(({
  text,
  onComplete,
  sources,
  plotData,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  messageId,
}: MemoizedMarkdownProps) => {
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const currentHeaderLevelRef = useRef(0);

  const handleAnimationComplete = useCallback(() => {
    if (!isAnimationComplete) {
      setIsAnimationComplete(true);
      onComplete();
    }
  }, [onComplete, isAnimationComplete]);

  const headerLevels = ['h1', 'h2', 'h3', 'h4', 'h5'] as const;
  type HeaderLevel = typeof headerLevels[number];

  const markdownOptions = useMemo(() => ({
    overrides: {
      ul: {
        component: (props: any) => (
          <ul start={props.start} className="streaming">
            <AnimatedList
              {...props}
              onComplete={handleAnimationComplete}
              messageId={messageId}
              onViewOnPlotClicked={onViewOnPlotClicked}
              onViewOnSourceClicked={onViewOnSourceClicked}
              sources={sources}
              plotData={plotData}
            />
          </ul>
        ),
      },
      ol: {
        component: (props: any) => (
          <ol start={props.start} className="streaming">
            <AnimatedList
              {...props}
              onComplete={handleAnimationComplete}
              messageId={messageId}
              onViewOnPlotClicked={onViewOnPlotClicked}
              onViewOnSourceClicked={onViewOnSourceClicked}
              sources={sources}
              plotData={plotData}
            />
          </ol>
        ),
      },
      li: {
        component: (props: any) => (
          <AnimatedParagraph
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            sources={sources}
            plotData={plotData}
          />
        )
      },
      ...headerLevels.reduce((acc, level) => {
        acc[level as HeaderLevel] = {
          component: (props: any) => (
            <AnimatedHeader
              key={props.key}
              {...props}
              onComplete={handleAnimationComplete}
              as={level}
              currentHeaderLevelRef={currentHeaderLevelRef}
            />
          ),
        };
        return acc;
      }, {} as Record<HeaderLevel, any>),
      p: {
        component: (props: any) => (
          <AnimatedParagraph
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            sources={sources}
            plotData={plotData}
          />
        ),
      },
      em: {
        component: AnimatedEm
      },
      hr: {
        component: (props: any) => (
          <AnimatedHr
            {...props}
            onComplete={handleAnimationComplete}
          />
        ),
      },
      table: {
        component: (props: any) => (
          <AnimatedTable
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            plotData={plotData}
          />
        ),
      },
      th: {
        component: (props: any) => (
          <AnimatedTableHeaderCell
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            sources={sources}
            plotData={plotData}
          />
        ),
      },
      td: {
        component: (props: any) => (
          <AnimatedTableCell
            {...props}
            onComplete={handleAnimationComplete}
            messageId={messageId}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            sources={sources}
            plotData={plotData}
          />
        ),
      },
    },
  }), [handleAnimationComplete, messageId, onViewOnPlotClicked, onViewOnSourceClicked]);

  return (
    <Markdown
      className="streaming animating"
      options={markdownOptions}
    >
      {replaceTags(text, sources, plotData)}
    </Markdown>
  );
}, (prevProps, nextProps) => {
  return prevProps.text === nextProps.text;
});

export default MemoizedMarkdown;