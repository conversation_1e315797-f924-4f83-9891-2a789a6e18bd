import { Snackbar, Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useIsMobile, useIsTablet } from '../../Layout/MobileUtils';

interface FeedbackSnackbarProps {
    open: boolean;
    message: string;
    onClose: () => void;
}

const FeedbackSnackbar = ({ open, message, onClose }: FeedbackSnackbarProps) => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;

    return (
        <Snackbar
            open={open}
            autoHideDuration={3000}
            onClose={onClose}
            anchorOrigin={{ vertical: 'bottom', horizontal: isMobile ? 'center' : 'left' }}
            sx={{ pointerEvents: 'none' }}
        >
            <Alert
                onClose={onClose}
                severity="success"
                icon={false}
                sx={{
                    background: theme.components.snackbar.fill,
                    color: theme.palette.info.contrast,
                    ...theme.typography.body2,
                    width: isMobileOrTablet ? '250px' : '320px'
                }}
            >
                {message}
            </Alert>
        </Snackbar>
    );
};

export default FeedbackSnackbar;
