import { post } from "./apiService";

export const registerUser = async (email: string, password: string, name: string) => {
    try {
        const data = await post<{ email: string, password: string, name: string }, { success: boolean; data: { token: string } }>(
            `/auth/register`,
            { email, password, name }
        );

        if (!data.success) {
            throw new Error("Registration failed");
        }
        return data.data;
    } catch (error) {
        console.error("Register error:", error);
        throw error;
    }
};

export const loginUser = async (email: string, password: string) => {
    try {
        const data = await post<{ email: string, password: string }, { success: boolean; data: { token: string } }>(
            `/auth/login`,
            { email, password }
        );

        if (!data.success) {
            throw new Error("Login failed");
        }
        return data.data;
    } catch (error) {
        console.error("Login error:", error);
        throw error;
    }
};

export const forgotPassword = async (email: string) => {
    try {
        const data = await post<{ email: string }, { success: boolean }>(
            `/auth/forgot-password`,
            { email }
        );

        if (!data.success) {
            throw new Error("Failed to send password reset link.");
        }
    } catch (error) {
        console.error("Failed to send password reset link.", error);
        throw error;
    }
};

export const resetPassword = async (token: string, password: string) => {
    try {
        const data = await post<{ token: string, password: string }, { success: boolean, data: { token: string, user: { id: string, email: string, created_at: string } } }>(
            `/auth/change-password`,
            { token, password }
        );

        if (data.success) {
            if (data?.data?.token) {
                localStorage.setItem("token", data?.data?.token);
            }
            return { message: "Password updated successfully" };
        } else {
            throw new Error("Failed to update password");
        }
    } catch (error) {
        console.error("Change password error:", error);
        throw error;
    }
};

export const logoutUser = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("userData");
};
