# ImpactAI Documentation

Welcome to the ImpactAI project documentation. This repository contains a comprehensive AI-powered platform for development economics research analysis and insights.

## Overview

ImpactAI is a specialized platform designed for development practitioners, policymakers, researchers, and international organizations. It provides research-backed insights using customized large language models (LLMs) and a structured knowledge database of validated research studies.

### Key Features

- AI-powered research analysis
- Interactive visualization of policy interventions
- Curated knowledge database of validated research
- Structured information on policy interventions and outcomes
- Quality-assessed research findings
- Bias mitigation through structured prompting

### Repository Structure

The repository is organized into several main components:

- [`backend/`](./backend.md) - Main application backend API
- [`agent/`](../docs/agent/README.md) - AI agent for processing and analyzing research papers
- [`frontend/`](./frontend.md) - Application frontend interface
- [`website/`](./website.md) - Landing page and marketing website
- [`infrastructure/`](./infrastructure.md) - Deployment and infrastructure configuration
- [`proxy/`](./proxy.md) - Proxy service configuration

## Getting Started

1. Install Docker following the [official installation guide](https://docs.docker.com/engine/install/)

2. Clone the repository:
   ```bash
   git clone https://github.com/worldbank/causal-ai-product.git
   cd causal-ai-product
   ```

3. Start the development environment:

- If you want to develop for the frontend
   ```bash
   make up
   ```
   You can also just `cd frontend` and `npm run dev` to start frontend development without docker

- If you want to develop for the backend
   ```bash
   make up-backend
   ```

- If you want to develop for the agent
   ```bash
   make up-agent
   ```

### Access Points

The application provides the following services (defined in `.honcho.yml`):

- **Frontend**: Web application interface
- **Backend**: API server with documentation
- **Agent**: AI processing service
- **Caddy**: Reverse proxy for domain routing

For local development URLs, see the service configuration in `services.yml` and the service map in `infrastructure/service-map.sh`.

To stop the development environment:
```bash
make down
```

## Platform Features

### Research Source Information
ImpactAI provides rich research source data including:
- **Full Paper Abstracts**: Complete abstract text retrieved directly from database
- **Comprehensive Metadata**: Detailed research context including quality scores
- **High Performance**: Optimized database connection pooling (20 concurrent connections)
- **Rich User Experience**: Informative research context for development practitioners

### Advanced Text Processing
ImpactAI includes sophisticated text processing capabilities for clean, contextual research summaries:
- **Intelligent Sanitization**: Automated text cleaning and formatting for research content
- **Data-Driven Filtering**: Intervention-outcome pairs validated against available data
- **Context Awareness**: Removes irrelevant references when no supporting data is available
- **Source Citation Filtering**: Validates source references like `[A123]`, `[P456]` against paper datasets
- **Multi-Layer Processing**: Comprehensive text cleaning through integrated processing pipeline
- **Professional Presentation**: Clean, formatted text optimized for development practitioners

### Quality Assurance
The enhanced text processing system ensures:
- **Citation Integrity**: Only sources with supporting data appear in summaries
- **Evidence-Based Research**: All citations backed by actual research data
- **Professional Quality**: Publication-ready text suitable for policy documents
- **Consistent Formatting**: Standardized text presentation across all responses

For technical details, see [API Documentation](./backend/docs/api-enhancements.md).

## Deployment Strategy

ImpactAI uses a multi-environment deployment approach designed to minimize production risks while enabling continuous integration and testing.

### Agent Deployment Model

To ensure stability and prevent automatic deployment issues, the platform uses different deployment strategies for agent services:

#### Automatic Deployments (Main Branch)
When code is merged to the `main` branch:
- ✅ **Core Services** → Deployed automatically to **Development** environment
  - Backend, Frontend, Proxy, Custom-Site
- ✅ **Agent Service** → Deployed automatically to **Experimental** environment
  - URL: `https://agent-experimental.impact-ai-dev.app`
  - Isolated testing environment for agent features
  - No impact on core development workflow

#### Manual Deployments (User-Controlled)
Agent deployments to primary environments require manual approval to prevent issues:

**Development Environment**
```bash
# Trigger via GitHub Actions
1. Go to "Development Environment Deployment" workflow
2. Click "Run workflow" 
3. Enable "Deploy agent to development environment"
4. Deploys to: https://agent.impact-ai-dev.app
```

**Testing Environment**
```bash
# Trigger via GitHub Actions  
1. Go to "Testing Environment Deployment" workflow
2. Type "DEPLOY_TO_TESTING" to confirm
3. Enable "Deploy agent to testing environment" 
4. Deploys to: https://agent.impact-ai-test.app
```

**Production Environment**
```bash
# Trigger via GitHub Actions
1. Go to "Production Environment Deployment" workflow  
2. Type "DEPLOY_TO_PRODUCTION" to confirm
3. Provide deployment reason
4. Enable "Deploy agent to production environment"
5. Deploys to: https://agent.impact-ai-prod.app
```

### Deployment Benefits

**🛡️ Risk Mitigation**
- Core services deploy independently without agent blocking
- Agent issues don't prevent critical service updates
- Manual control over production agent deployments

**🔄 Continuous Integration**
- Automatic experimental agent testing on every merge
- Core services maintain rapid deployment cycle
- Independent validation of agent features

**⚡ Flexibility**
- Deploy core services immediately when needed
- Test agent features in isolation (experimental environment)
- Choose when to promote agent to production

### Environment URLs

| Environment | Core Services | Agent Service |
|-------------|---------------|---------------|
| **Development** | `https://impact-ai-dev.app` | `https://agent.impact-ai-dev.app` (manual) |
| **Experimental** | N/A | `https://agent-experimental.impact-ai-dev.app` (auto) |
| **Testing** | `https://impact-ai-test.app` | `https://agent.impact-ai-test.app` (manual) |
| **Production** | `https://impact-ai-prod.app` | `https://agent.impact-ai-prod.app` (manual) |

### Workflow Summary

1. **Develop** → Push to `main` branch
2. **Auto-Deploy** → Core services to dev + Agent to experimental 
3. **Test** → Manually deploy agent to dev/test as needed
4. **Validate** → Ensure all services work correctly
5. **Deploy** → Manually deploy to production when ready

This approach ensures maximum stability while maintaining development velocity.

## Additional Documentation

- [Backend documentation](./backend/README.md)
- [Agent documentation](./agent/README.md)
- [Frontend documentation](./frontend/README.md)
- [Website documentation](./website/README.md)
- [Infrastructure documentation](./docs/infrastructure.md)
- [API Documentation](./backend/docs/api-enhancements.md)
- [Architecture overview](./docs/architecture.md)
- [Development guide](./docs/development.md)
