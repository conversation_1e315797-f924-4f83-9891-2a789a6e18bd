# Development Economics Research Assistant

An AI-powered agent that helps analyze academic papers in development economics by answering complex queries about methodologies, findings, and data.

## 🚀 Quick Start

### 1. Installation

```bash
git clone https://github.com/worldbank/causal-ai-product.git
cd causal-ai-product/agent
curl -sSL https://install.python-poetry.org | python3 -
poetry install
```

### 2. Environment Setup

Create a `.env` file:
```bash
GOOGLE_API_KEY=your_google_api_key
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json

# Database Configuration
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_HOST=your_mysql_host
MYSQL_DATABASE=your_database_name
```

## 🤖 Agent Architecture

The agent is a ReAct (Reasoning and Acting) agent that uses specialized pipelines to answer different types of research questions about development economics.

### Pipeline-Based Question Processing

The agent automatically selects the most appropriate pipeline based on your question type:

**🔍 Descriptive Pipeline** - For "what, where, when" questions
- *Example*: "What are conditional cash transfer programs?"
- *Tools*: Entity Extractor → SQL Generator → Structured Data Organizer → Final Answer Generator

**⚡ Impact Pipeline** - For causal impact questions
- *Example*: "What are the effects of cash transfers on education?"
- *Tools*: Entity Extractor → SQL Generator → Structured Data Organizer → Final Answer Generator

**⚖️ Comparative Pipeline** - For head-to-head comparisons
- *Example*: "Compare cash transfers vs. microfinance effectiveness"
- *Tools*: Entity Extractor → SQL Generator → Structured Data Organizer → Final Answer Generator

**🔧 Implementation Pipeline** - For practical execution questions
- *Example*: "How are cash transfer programs typically implemented?"
- *Tools*: Entity Extractor → SQL Generator → RAG Search

**🔄 Theory of Change Pipeline** - For mechanism and pathway questions
- *Example*: "Through what mechanisms do cash transfers affect education?"
- *Tools*: Entity Extractor → SQL Generator → RAG Search

**🌍 Generalizability Pipeline** - For cross-context applicability
- *Example*: "Do cash transfer effects generalize across countries?"
- *Tools*: Entity Extractor → SQL Generator → Structured Data Organizer → RAG Search → Final Answer Generator

### How It Works

1. **Query Analysis**: The agent analyzes your question to determine intent and extract key entities
2. **Pipeline Selection**: Based on the question type, the agent selects the most appropriate pipeline
3. **Data Retrieval**: Uses SQL generation to find relevant papers and data from the database
4. **Information Processing**: Applies either structured data analysis or semantic search (RAG) as needed
5. **Response Generation**: Synthesizes findings into a comprehensive answer

The agent maintains conversation context, so follow-up questions can build on previous responses and use comparative analysis across different pipelines.

## 🖥️ Interactive Testing

The easiest way to test and explore the agent is through the interactive CLI:

### Start Interactive Mode

```bash
# Basic interactive mode
poetry run python -m src.agent.run_agent --interactive

# Interactive mode with detailed logging
poetry run python -m src.agent.run_agent --interactive --verbose
```

### Interactive Commands

Once in interactive mode, you can use these commands:

- **Ask questions**: Simply type your research question
- **`help`** - Show available commands and example queries
- **`stats`** - Display execution statistics for the current session
- **`config`** - Show agent configuration and available pipelines
- **`clear`** - Clear conversation history and start fresh
- **`exit/quit/bye`** - Exit the interactive session

### 📊 Logs and Tracking Metrics

The agent provides comprehensive logging and metrics tracking for monitoring performance and debugging.

#### Log Files Structure

All logs are automatically saved to the `logs/` directory with timestamped filenames:

```
logs/
├── agent_cli_20241201_143022.log              # General agent logs
├── agent_cli_20241201_143022_actions.log      # Action-specific logs
└── metrics/
    └── pipeline_metrics_20241201_143022_default.json  # Pipeline metrics
```

#### General Logs (`agent_cli_*.log`)

Contains comprehensive information about:
- Agent initialization and configuration
- Query processing steps and pipeline selection
- LLM interactions and responses
- Tool executions and results
- Error messages and debugging info
- Session management and cleanup

**Example log entries:**
```
2024-12-01 14:30:22 - src.agent.main - INFO - Starting agent execution for query: What are the effects of cash transfers?
2024-12-01 14:30:23 - src.pipelines.manager - INFO - Executing pipeline impact_pipeline for intent causal impact
2024-12-01 14:30:25 - src.tools.sql_generator - INFO - Generated SQL query with 150 results
2024-12-01 14:30:27 - src.pipelines.impact - INFO - Step 3: Executing structured_data_organizer
```

#### Action Logs (`*_actions.log`)

Focused on user-facing actions and status updates:
- Query start/completion notifications
- Pipeline decisions and reasoning
- Iteration progress tracking
- Final answer generation

**Example action entries:**
```
2024-12-01 14:30:22 - waiting_messages - INFO - Starting to process query: What are the effects of cash transfers?
2024-12-01 14:30:23 - waiting_messages - INFO - ITERATION 1/10
2024-12-01 14:30:24 - waiting_messages - INFO - DECIDED: Use pipeline impact_pipeline
2024-12-01 14:30:27 - waiting_messages - INFO - PIPELINE_COMPLETE: impact_pipeline completed in 3.45 seconds
```

#### Pipeline Metrics (`pipeline_metrics_*.json`)

Detailed performance metrics for each pipeline execution:

```json
{
  "conversation_id": "default",
  "total_pipelines": 1,
  "successful_pipelines": 1,
  "failed_pipelines": 0,
  "generated_at": "2024-12-01T14:30:30",
  "pipelines": [
    {
      "pipeline_name": "impact_pipeline",
      "intent": "causal impact",
      "user_query": "What are the effects of cash transfers?",
      "total_duration": 3.45,
      "success": true,
      "num_steps": 4,
      "failed_steps": 0,
      "steps": [
        {
          "step_name": "entity_extractor",
          "duration": 0.85,
          "success": true,
          "input_keys": ["user_query"],
          "output_keys": ["interventions", "outcomes", "regions"],
          "timestamp": "2024-12-01T14:30:23"
        },
        {
          "step_name": "sql_generator",
          "duration": 1.20,
          "success": true,
          "row_count": 150,
          "timestamp": "2024-12-01T14:30:24"
        }
      ]
    }
  ]
}
```

#### Pipeline Metrics - Visualizations

The agent includes a comprehensive analytics dashboard for visualizing pipeline performance across multiple executions. This helps identify bottlenecks, success rates, and performance patterns.

##### Generating Analytics Data

First, run the analytics service to process questions and generate pipeline metrics:

```bash
# Run analytics on a set of questions
python -m src.services.analytics --questions questions.json --output results.json --force

# Example questions.json format:
# [
#   "What are the effects of cash transfers on education?",
#   "How do we measure the impact of health interventions?",
#   "Compare microfinance vs. cash transfers for poverty reduction"
# ]
```

This generates:
- **Summary results** (`results.json`): High-level query results with execution times and pipeline information
- **Detailed results** (`results_detailed.json`): Step-by-step pipeline details with input/output data
- **Pipeline metrics** (`logs/metrics/pipeline_metrics_*.json`): Performance tracking data

##### Creating Visualizations

After generating analytics data, create visual dashboards:

```bash
# Generate dashboard for all pipeline metrics
python -m src.services.analytics_viz

# Generate dashboard for specific date/prefix
python -m src.services.analytics_viz --prefix "pipeline_metrics_20250603"

# Generate dashboard for specific conversation
python -m src.services.analytics_viz --prefix "pipeline_metrics_20250603_111202"
```

##### Dashboard Features

The visualization dashboard provides:

**📊 Text Dashboard**
- Questions processed count
- Successful vs failed pipeline statistics
- Pipeline breakdown by type with success rates
- Mean execution times (overall, by pipeline, by step)
- Top 5 slowest pipeline steps
- Intent distribution across queries

**📈 Generated Plots** (saved to `logs/analytics/pipeline_plots/`)
- **Pipeline Success Rates**: Bar chart showing success percentage by pipeline type
- **Execution Time Distribution**: Histogram and box plot of pipeline execution times
- **Pipeline Execution Times**: Mean execution time comparison across pipeline types
- **Step Execution Times**: Top 10 slowest pipeline steps with execution counts
- **Intent & Pipeline Distribution**: Pie charts showing usage patterns


#### Viewing Metrics in Interactive Mode

Use the `stats` command to see real-time execution statistics:

```bash
Query #1: stats

┏━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓
┃ Iteration ┃ Intent          ┃ Thinking Time (s) ┃ Action Time (s)  ┃ Input Tokens ┃ Output Tokens ┃
┡━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩
│ 1         │ causal impact   │ 1.25              │ 3.45             │ 2847         │ 156           │
│ TOTAL     │                 │ 1.25              │ 3.45             │ 2847         │ 156           │
└───────────┴─────────────────┴───────────────────┴──────────────────┴──────────────┴───────────────┘

Total conversations: 1
Total history entries: 1
Log file: logs/agent_cli_20241201_143022.log
```

#### Accessing Metrics Programmatically

```python
from src.agent.main import Agent

# After running queries
agent = Agent(config)
result = await agent.execute("Your query")

# Get execution statistics
stats = agent.get_execution_stats()
print(f"Total thinking time: {stats['total_thinking_time']:.2f}s")
print(f"Pipeline executions: {stats['pipeline_executions']}")
print(f"Successful queries: {stats['successful_queries']}")

# Get pipeline metrics
pipeline_metrics = agent.pipeline_manager.get_metrics_summary()
print(f"Successful pipelines: {pipeline_metrics['successful_pipelines']}")
print(f"Average pipeline time: {pipeline_metrics['average_pipeline_time']:.2f}s")
```

#### Log Analysis Tips

1. **Performance Debugging**: Check step durations in pipeline metrics to identify bottlenecks
2. **Error Investigation**: Search general logs for ERROR level messages and stack traces
3. **Query Understanding**: Review entity extraction results to see how queries are interpreted
4. **Pipeline Selection**: Check action logs to understand why specific pipelines were chosen
5. **Tool Performance**: Monitor individual tool execution times in pipeline metrics

#### Monitoring Long-Running Sessions

For extended sessions, you can monitor logs in real-time:

```bash
# Monitor general logs
tail -f logs/agent_cli_*.log

# Monitor action logs only
tail -f logs/agent_cli_*_actions.log

# Monitor metrics (after pipeline completions)
watch -n 5 'ls -la logs/metrics/ && echo "Latest metrics:" && cat logs/metrics/pipeline_metrics_*.json | tail -20'

# Search for specific patterns
grep "ERROR\|PIPELINE_COMPLETE\|DECIDED" logs/agent_cli_*_actions.log
```

#### Log Levels and Verbosity

- **Normal Mode**: Shows warnings and errors in console, all details in log files
- **Verbose Mode** (`--verbose`): Shows info-level messages in console plus all file logging
- **Debug Mode**: Set `logging.basicConfig(level=logging.DEBUG)` for maximum detail

The logs are particularly useful for:
- Understanding which pipeline was selected and why
- Debugging complex queries and tool interactions
- Tracking performance across different question types
- Monitoring LLM reasoning and decision-making
- Analyzing pipeline execution metrics and bottlenecks
- Identifying patterns in successful vs. failed queries

## 📊 Real-time Progress Status Updates

The agent now provides intelligent, real-time status updates that keep users informed while their queries are being processed. This is especially important for complex research questions that may take 30-60 seconds to complete.

### How Progress Tracking Works

The agent uses a sophisticated system to track and communicate its progress:

1. **Execution Step Storage**: Each tool execution (started/finished) is automatically logged to Redis
2. **Smart Status Generation**: An LLM analyzes execution steps and generates natural, contextual status messages  
3. **Real-time Monitoring**: The ProgressMonitor service watches for new steps and emits user-friendly updates
4. **Frontend Integration**: Status messages are sent to the frontend via Server-Sent Events (SSE)

### Execution Step Flow

```mermaid
graph TD
    A[User Query] --> B[Agent Starts Processing]
    B --> C[Tool Execution Begins]
    C --> D[Log Step to Redis]
    D --> E[ProgressMonitor Detects New Step]
    E --> F[Generate Status Message via LLM]
    F --> G[Send Update to Frontend]
    G --> H{More Tools?}
    H -->|Yes| C
    H -->|No| I[Final Answer Ready]
    
    style D fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#e8f5e8
```

### Status Message Examples

The system generates contextual, natural language status messages:

**Entity Extraction:**
- First run: "Understanding your question about cash transfer effectiveness"
- Repeat run: "Refining understanding of poverty reduction interventions"

**Database Search:**
- First run: "Searching studies database for education impact research"  
- Repeat run: "Expanding search for additional education studies"

**Data Organization:**
- First run: "Organizing research data on microfinance programs"
- Repeat run: "Refining organization of development economics findings"

### Technical Implementation

#### AgentCache Execution Logging

The agent automatically logs each tool execution step:

```python
# Automatic logging when tools start/finish
agent_cache.log_tool_execution(
    conversation_id="conv_123", 
    tool_name="entity_extractor",
    status="started",
    data={"query": "What are the effects of cash transfers?"}
)
```

#### ProgressMonitor Service

The `ProgressMonitor` class watches for new execution steps and generates status updates:

```python
from src.services.agent import ProgressMonitor

# Initialize progress monitoring
monitor = ProgressMonitor(
    cache=redis_cache,
    conversation_id="conv_123",
    query="What are the effects of cash transfers?",
    llm_client=llm_client
)

# Check for new updates
status_update = await monitor.check_for_updates()
# Returns: {"title": "Understanding your question about cash transfers", "status": "started"}
```

#### Smart Status Message Generation

Status messages are generated using a specialized LLM prompt that:
- Incorporates the user's original question for context
- Tracks whether tools have run before (for "refining" vs. "initial" language)
- Extracts key entities and concepts from execution data
- Ensures messages are concise (≤100 characters) and natural

### Integration with Frontend

The progress status system is designed to integrate seamlessly with web frontends:

1. **Server-Sent Events (SSE)**: Real-time status updates pushed to the browser
2. **JSON Format**: Structured status data for easy frontend consumption
3. **Fallback Support**: Works without Redis using static fallback messages

### Waiting Message Service (Legacy)

The original `waiting_messages.py` service provides file-based status monitoring:

```bash
# Run the waiting message service
poetry run python -m src.services.waiting_messages

# Customize log path and update interval
poetry run python -m src.services.waiting_messages --log-path custom/path/to/logs.log --interval 5.0

# Set number of unchanged iterations before stopping
poetry run python -m src.services.waiting_messages --max-unchanged 3
```

This service:
1. Monitors the agent log file for status updates
2. Parses log entries containing "waiting_messages"
3. Uses an LLM to generate natural language status messages
4. Provides real-time updates about what the agent is doing
5. Automatically stops when activity ceases for a configurable period

### Running Agent and Waiting Messages Together

For the best user experience, run both the agent and waiting message service simultaneously:

```bash
# Terminal 1: Run the agent
python -m agent.src.agent.run_agent --query "What is the impact of climate change on agricultural yields?"

# Terminal 2: Run the waiting message service
python -m agent.src.services.waiting_messages
```

Or create a simple shell script to run both:

```bash
#!/bin/bash
# save as run_agent_with_status.sh

# Start the agent in the background
python -m agent.src.agent.run_agent --query "$1" &
AGENT_PID=$!

# Start the waiting message service
python -m agent.src.services.waiting_messages

# When the waiting service exits, kill the agent if it's still running
kill $AGENT_PID 2>/dev/null
```

The waiting message service will automatically stop when it detects no changes in the log file for a configurable number of iterations.

## 🤖 Agent Architecture

### ReAct Agent Workflow

The agent follows the ReAct (Reasoning and Acting) methodology:

1. **Observe**: Analyzes the query and previous observations
2. **Think**: Plans next steps and breaks down complex queries
3. **Act**: Executes appropriate tools when needed
4. **Reflect**: Analyzes tool outputs and adjusts strategy
5. **Answer**: Provides final response when sufficient data is gathered

### Asynchronous LLM Operations

The agent utilizes non-blocking async operations for LLM interactions, particularly with the Gemini model:

```python
# Example of async LLM completion
response = await gemini_client.complete(messages)
```

This async implementation enables:
- Improved handling of concurrent requests
- Better resource utilization
- Reduced latency for parallel operations
- Enhanced scalability for high-load scenarios

### Tool Manager

The `ToolManager` orchestrates tool execution and maintains a shared cache:

```python
from src.tools.manager import ToolManager

# Initialize tools with shared configuration
tool_manager = ToolManager(config={
    "verbose": True,
    "session": shared_session  # Optional shared aiohttp session
})

# Execute tools with caching
result = await tool_manager.execute_with_cache("entity_extractor", query="your query")
```

### Core Tools

1. **Entity Extractor**
```python
# Extracts key concepts from queries
entities = await tool_manager.execute("entity_extractor",
    query="What is the impact of cash transfers on education in Africa?")
# Returns: ExtractedEntities(interventions=["cash transfers"],
#                          outcomes=["education"], regions=["Africa"])
```

2. **SQL Generator**
```python
# Generates and executes SQL queries based on entities
query_result = await tool_manager.execute("sql_generator",
    entities=entities)
# Returns: QueryResult with papers and structured data
```

3. **RAG Searcher**
```python
# Performs semantic search within papers
rag_results = await tool_manager.execute("rag_search",
    query_result=query_result,
    num_results=15)
# Returns: RAGResults with relevant passages and sources
```

4. **Structured Data Organizer**
```python
# Organizes and formats structured data
structured_data = await tool_manager.execute("structured_data_organizer",
    query_result=query_result,
    entities=entities)
# Returns: StructuredData with formatted analysis
```

### Tool Outputs

Each tool provides detailed output:

```python
# Entity extraction results
print(entities)  # Shows extracted concepts

# SQL query results
print(query_result)  # Shows papers and data points

# RAG search results
print(rag_results)  # Shows relevant passages

# Final structured data
print(structured_data)  # Shows formatted analysis
```

### Cache Inspection

```python
# View tool cache contents
print(tool_manager.get_tool_data())

# Clear cache if needed
tool_manager.clear_cache()
```

## 🔧 Advanced Configuration

### Custom Tool Setup

```python
from src.tools.base import Tool

class CustomTool(Tool):
    def __init__(self, config):
        super().__init__(
            name="custom_tool",
            description="Your tool description",
            func=self.execute,
            arguments=[("arg1", "type1")],
            outputs=[("output1", "type1")],
            config=config
        )

    async def execute(self, **kwargs):
        # Your tool logic here
        pass
```

### Session Management

```python
import aiohttp

async with aiohttp.ClientSession() as session:
    config["shared_session"] = session
    agent = Agent(config)
    # Use agent with shared session
```
