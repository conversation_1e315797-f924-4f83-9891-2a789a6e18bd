import * as d3 from "d3";
import { PlotData } from "../../../../types/ConversationTypes";

interface LinkProps {
  from: number[];
  to: number[];
  isHovered: boolean;
  isNothingHovered: boolean;
  details: PlotData;
}

const l = d3
  .line()
  .x((d) => d[0])
  .y((d) => d[1])
  .curve(d3.curveBumpX);

const Link = ({ from, to, isHovered, isNothingHovered, details }: LinkProps) => {
  return (
    <path
      d={l([from, to]) || undefined}
      fill="none"
      stroke={isHovered ? "rgb(35 63 92)" : "rgb(224 231 237)"}
      opacity={isNothingHovered || isHovered ? 1 : 0.2}
      strokeWidth={details?.data.length * 2}
    />
  );
};

export default Link;
