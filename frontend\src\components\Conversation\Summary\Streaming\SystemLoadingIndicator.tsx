import React from "react";
import { Box, styled, useTheme, Theme } from "@mui/material";
import { LoadingState } from '../../LoadingState';

interface SystemLoadingIndicatorProps {
    isLoading: boolean;
    data: { title: string; description: string } | null;
}

const LoadingContainer = styled('div')(({ theme }: { theme: Theme }) => ({
    transition: theme.transitions.create('opacity', {
        duration: '0.3s',
        easing: 'ease-in-out',
    }),
    opacity: 0,
    '&.show': {
        opacity: 1,
    },
    width: '100%'
}));

const SystemLoadingIndicator: React.FC<SystemLoadingIndicatorProps> = ({ isLoading, data }: SystemLoadingIndicatorProps) => {
    const theme = useTheme();

    if (!isLoading) {
        return null;
    }

    return (
        <LoadingContainer className={isLoading ? 'show' : ''}>
            <Box mt={0} display="flex" justifyContent="flex-start">
                <LoadingState
                    theme={theme}
                    layoutType='text'
                    loadingTexts={data ? [data.title] : ["Thinking."]}
                    currentTextIndex={0}
                />
            </Box>
        </LoadingContainer>
    );
};

export default SystemLoadingIndicator;