#!/bin/bash

# Source colors if not already loaded
if [[ -z "${RED:-}" ]]; then
    source "$(dirname "${BASH_SOURCE[0]}")/colors.sh"
fi

# Logging functions with color-coded output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
} 