import React from 'react';
import { Button as M<PERSON><PERSON>utton } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { ButtonType, ButtonProps } from './ButtonTypes';

const Button: React.FC<ButtonProps> = ({ buttonType = ButtonType.MUI, iconOnly, onClick, children, ...props }) => {
  let button;

  if (buttonType === ButtonType.MUI) {
    button = (
      <MUIButton
        {...props}
        color="primary"
        variant="outlined"
        size="medium"
        onClick={onClick}
        sx={{
          width: '100%',
          minHeight: '36px',
          padding: '6px 16px',
          borderRadius: '4px',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          flex: '1 0 0',
          transition: 'transform 0.3s ease',
          margin: 0,
        }}
        startIcon={!iconOnly && <AddIcon />}
      >
        {!iconOnly && children}
      </MUIButton>
    );
  } else {
    button = (
      <button {...props} onClick={onClick} style={{ border: '1px solid black', outline: 'none' }}>
        {iconOnly ? <AddIcon /> : children}
      </button>
    );
  }

  return <>{button}</>;
};

export default Button;
