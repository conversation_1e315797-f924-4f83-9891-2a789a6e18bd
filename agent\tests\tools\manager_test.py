import os
import sys
from unittest.mock import Async<PERSON><PERSON>, Mock, patch

import pytest

# Add the src directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../src"))

from tools.manager import ToolManager  # noqa: E402


@pytest.fixture
def tool_manager():
    """Create a ToolManager instance with mocked dependencies"""
    config = {"verbose": True}

    # Create mock tools with async __call__ methods
    def create_mock_tool():
        tool = AsyncMock(return_value="mock_result")
        tool.func = AsyncMock(return_value="mock_result")
        return tool

    with patch.multiple(
        "tools.manager",
        DatasetManager=Mock(),
        EntityExtractor=Mock(return_value=create_mock_tool()),
        SQLGenerator=Mock(return_value=create_mock_tool()),
        RAGSearcher=Mock(return_value=create_mock_tool()),
        StructuredDataOrganizer=Mock(return_value=create_mock_tool()),
        FinalAnswerGenerator=Mock(return_value=create_mock_tool()),
        MethodologyExplainer=Mock(return_value=create_mock_tool()),
    ):
        return ToolManager(config, conversation_id="test_conversation")


@pytest.fixture
def mock_tool():
    """Create a mock tool for testing"""
    tool = AsyncMock(return_value="test_result")
    tool.func = AsyncMock(return_value="test_result")
    return tool


def test_tool_manager_initialization(tool_manager):
    """Test that ToolManager initializes correctly."""
    assert tool_manager.tools != {}
    assert tool_manager.cache is not None


@pytest.mark.asyncio
async def test_tool_execution_works(tool_manager, mock_tool):
    """Test that tools can be executed and return results."""
    tool_manager.register("test_tool", mock_tool)

    result = await tool_manager.execute("test_tool", arg1="value1")

    assert result == "test_result"
    mock_tool.assert_called_once_with(arg1="value1")


@pytest.mark.asyncio
async def test_built_in_tools_can_execute(tool_manager):
    """Test that built-in tools can be executed."""
    # Test that entity extractor can be executed (basic functionality)
    result = await tool_manager.execute("entity_extractor", query="test query")
    assert result is not None

    # Test that SQL generator can be executed
    tool_manager.cache.entities = Mock()
    result = await tool_manager.execute("sql_generator", query="test query")
    assert result is not None
