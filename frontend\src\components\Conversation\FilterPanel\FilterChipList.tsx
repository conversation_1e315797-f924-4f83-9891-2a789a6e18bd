import React from 'react';
import { Box, Typography, Chip, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface FilterChipListProps {
  label: string;
  options: string[];
  activeValues: string[];
  onChipChange: (value: string) => void;
}

const FilterChipList: React.FC<FilterChipListProps> = ({
  label,
  options,
  activeValues,
  onChipChange,
}) => {
  const theme = useTheme();

  const handleChipClick = (value: string) => {
    onChipChange(value);
  };

  const handleChipDelete = (value: string) => {
    onChipChange(value);
  };
  
  const labelStyles = {
    color: 'rgba(0, 51, 128, 0.70)',
    fontFeatureSettings: "'liga' off, 'clig' off",
    fontFamily: 'Roboto',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 600,
    lineHeight: '12px',
    letterSpacing: '0.15px',
    mb: 2,
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography sx={labelStyles}>
        {label}
      </Typography>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
        {options.map((option) => (
          <Chip
            key={option}
            label={option}
            size="small"
            onClick={() => handleChipClick(option)}
            onDelete={activeValues.includes(option) ? () => handleChipDelete(option) : undefined}
            deleteIcon={<CloseIcon sx={{ fontSize: '16px' }} />}
            sx={{
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              padding: '3px 4px',
              borderRadius: '100px',
              border: `1px solid ${theme.palette.secondary.main}`,
              opacity: activeValues.includes(option) ? 1 : 0.38,
              backgroundColor: activeValues.includes(option) ? '#F2F6FC' : 'inherit',
              '& .MuiChip-label': {
                fontFamily: 'Roboto',
                fontSize: '13px',
                fontWeight: 400,
                lineHeight: '18px',
                letterSpacing: '0.16px',
                color: activeValues.includes(option) ? theme.palette.text.primary : theme.palette.text.secondary,
                padding: '0 4px',
              },
              '& .MuiChip-deleteIcon': {
                color: theme.palette.primary.main,
                fontSize: '16px',
                '&:hover': {
                  color: theme.palette.primary.main,
                },
              },
              '&:hover': {
                backgroundColor: '#F2F6FC',
                opacity: 1,
              },
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default FilterChipList;