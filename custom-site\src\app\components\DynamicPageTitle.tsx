"use client";
import { useEffect } from "react";

interface DynamicPageTitleProps {
  title: string;
  description: string;
}

const DynamicPageTitle = ({ title, description }: DynamicPageTitleProps) => {
  useEffect(() => {
    const newTitle = document.createElement("title");
    newTitle.textContent = title;

    const metaDescription = document.createElement("meta");
    metaDescription.name = "description";
    (metaDescription as HTMLMetaElement).content = description;

    const head = document.head;
    if (head) {
      if (head.firstChild) {
        head.insertBefore(newTitle, head.firstChild);
        if (head.firstChild.nextSibling) {
          head.insertBefore(metaDescription, head.firstChild.nextSibling);
        } else {
          head.appendChild(metaDescription);
        }
      } else {
        head.appendChild(newTitle);
        head.appendChild(metaDescription);
      }
    }
    return () => {
      if (head) {
        const existingTitle = head.querySelector("title");
        const existingMeta = head.querySelector('meta[name="description"]') as HTMLMetaElement;
        if (existingTitle && existingTitle.textContent === title) {
          head.removeChild(existingTitle);
        }
        if (existingMeta && existingMeta.content === description) {
          head.removeChild(existingMeta);
        }
      }
    };
  }, [title, description]);
  return null;
};

export default DynamicPageTitle;