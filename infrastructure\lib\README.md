# Infrastructure Library Modules

This directory contains shared utility libraries for infrastructure scripts. These modules provide consistent error handling, logging, and common functionality across all infrastructure scripts.

## Library Files

### `colors.sh`
Defines ANSI color constants for terminal output:
- `RED` - Error messages
- `GREEN` - Success messages  
- `YELLOW` - Warning messages
- `BLUE` - Info messages
- `NC` - No Color (reset)

### `logging.sh`
Provides standardized logging functions with color-coded output:
- `log_info(message)` - Blue info messages
- `log_success(message)` - Green success messages
- `log_warning(message)` - Yellow warning messages
- `log_error(message)` - Red error messages

### `gcloud.sh`
Google Cloud utilities:
- `check_gcloud_installed()` - Check if gcloud CLI is installed
- `check_gcloud_auth()` - Verify gcloud authentication
- `check_sql_instance(name, type)` - Check if Cloud SQL instance exists
- `check_gcs_bucket(name)` - Check if GCS bucket exists and is accessible

### `common.sh`
Common utilities and patterns:
- `init_script()` - Initialize script with strict error handling (`set -euo pipefail`)
- `setup_cleanup(function)` - Set up exit trap with custom cleanup function
- `default_cleanup()` - Standard cleanup that logs exit codes
- `show_help_header(script, description)` - Standard help header format
- `show_help_footer()` - Standard help footer with common options
- `require_env_var(var_name, description)` - Validate required environment variables
- `validate_enum(value, var_name, allowed...)` - Validate value against allowed list
- `create_timestamped_filename(prefix, suffix)` - Generate timestamped filenames

### `container.sh`
Container engine utilities:
- `check_container_engine()` - Detect and set available container engine (Docker/Podman)

### `deploy.sh`
Cloud Run deployment utilities:
- `validate_deployment_environment()` - Validate TARGET and SERVICE environment variables
- `get_database_instance(target)` - Get database instance name based on target environment
- `build_and_push_image(dir, tag, dry_run)` - Build and push container images
- `deploy_cloud_run_service(name, image, config, dry_run)` - Deploy generic Cloud Run service
- `deploy_backend_service(name, image, config, target, dry_run)` - Deploy backend with database connectivity
- `deploy_frontend_service(name, image, target, dry_run)` - Deploy frontend service
- `create_image_tag(service)` - Generate git-based image tags (branch-commit format)

## Usage Example

```bash
#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"
source "$SCRIPT_DIR/lib/deploy.sh"

# Initialize script with strict error handling
init_script

# Set up cleanup
setup_cleanup default_cleanup

# Use logging functions
log_info "Starting deployment..."

# Validate environment variables
validate_deployment_environment

# Check prerequisites
check_gcloud_auth

# Build and deploy
image_tag=$(create_image_tag "$SERVICE")
build_and_push_image "$SERVICE" "$image_tag" false
deploy_backend_service "my-service" "$image_tag" "env.yml" "$TARGET" false

log_success "Deployment completed!"
```

## Benefits

1. **Consistency** - All scripts use the same logging format and error handling
2. **Maintainability** - Common functionality is centralized and easier to update
3. **Reusability** - New scripts can quickly adopt proven patterns
4. **Modularity** - Scripts only import what they need
5. **Testing** - Library functions can be tested independently

## Migration

Scripts that use these libraries should:
1. Remove duplicated color constants, logging functions, and common utilities
2. Add script directory detection and source statements
3. Replace inline functionality with library function calls
4. Use the standardized help and argument parsing patterns

This approach makes the codebase more maintainable and ensures consistent behavior across all infrastructure scripts. 