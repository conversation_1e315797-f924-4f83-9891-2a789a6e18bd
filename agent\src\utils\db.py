"""Database connection utilities."""

import os
import sqlalchemy
from google.cloud.sql.connector import Connector
import logging
import pymysql

from sqlalchemy.sql import text

logger = logging.getLogger(__name__)

db_name = os.getenv("MYSQL_DATABASE", "impactai-silver-db")
db_user = os.getenv("MYSQL_USER")
db_password = os.getenv("MYSQL_PASSWORD")

project_id = os.getenv("PROJECT_ID")
region = os.getenv("MYSQL_LOCATION")
instance_id = "impactai-db"

connection_name = f"{project_id}:{region}:{instance_id}"


def initialise_db_engine() -> sqlalchemy.engine.base.Engine:
    """
    Initializes a connection pool for a Cloud SQL instance of MySQL.

    Uses the Cloud SQL Python Connector package.
    """
    try:

        def getconn() -> pymysql.connections.Connection:
            connector = Connector()
            return connector.connect(
                connection_name,
                "pymysql",
                user=db_user,
                password=db_password,
                db=db_name,
            )

        pool = sqlalchemy.create_engine(
            "mysql+pymysql://",
            creator=getconn,
        )

        return pool

    except Exception as e:
        logger.error(f"Error creating database engine: {str(e)}")
        raise


engine = initialise_db_engine()


def create_db_engine() -> sqlalchemy.engine.base.Engine:
    """
    Initializes a connection pool for a Cloud SQL instance of MySQL.

    Uses the Cloud SQL Python Connector package.
    """
    return engine


def verify_tag_id(engine, tag_id: int) -> bool:
    """Verify that a tag_id exists in the Taxonomy table."""
    try:
        with engine.connect() as connection:
            result = connection.execute(
                text("SELECT id, tag_label FROM Taxonomy WHERE id = :tag_id"),
                {"tag_id": tag_id},
            ).fetchone()
            if result:
                logger.info(f"Found tag: {result}")
                return True
            logger.warning(f"No tag found with id: {tag_id}")
            return False
    except Exception as e:
        logger.error(f"Error verifying tag_id: {str(e)}")
        return False
