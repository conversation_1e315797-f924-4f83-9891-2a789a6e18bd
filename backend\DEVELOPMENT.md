# Backend Development Setup

This guide explains how to run the backend service locally without <PERSON><PERSON> using Poetry for dependency management.

## Prerequisites

- Python 3.11 or higher
- Poetry (install from https://python-poetry.org/docs/#installation)
- MySQL database (local or remote)
- Redis server (local or remote)

## Quick Start

1. **Install Dependencies**
   ```bash
   cd backend
   poetry install
   ```

2. **Set up Environment Variables**
   Create a `.env` file in the backend directory with the following variables:
   ```bash
   # Database Configuration
   MYSQL_HOST=localhost
   MYSQL_USER=your_mysql_user
   MYSQL_PASSWORD=your_mysql_password
   MYSQL_DATABASE=your_database_name
   MYSQL_CORE_DATABASE=impactai-db

   # Redis Configuration
   REDIS_HOST=localhost
   REDIS_PORT=6379

   # Google API Configuration
   GOOGLE_API_KEY=your_google_api_key

   # JWT Configuration
   JWT_SECRET=your_jwt_secret_key

   # Email Configuration (Resend)
   RESEND_API_KEY=your_resend_api_key

   # Application Configuration
   DEBUG=true
   ```

3. **Start the Development Server**
   ```bash
   # Development mode with auto-reload
   poetry run dev
   
   # OR for production mode
   poetry run start
   ```

## Available Commands

- `poetry run dev` - Start development server with auto-reload on `127.0.0.1:8000`
- `poetry run start` - Start production server on `0.0.0.0:8000`
- `poetry run python main.py` - Alternative way to run the server
- `poetry run pytest` - Run tests

## API Documentation

Once the server is running, you can access the backend service endpoints. The service is configured in `.honcho.yml` and accessible via the service map in `infrastructure/service-map.sh`.

For local development, the backend service endpoints are:
- **API Documentation**: Backend service URL + `/docs`
- **Health Check**: Backend service URL + `/health`
- **Root Endpoint**: Backend service URL + `/`

These endpoints are defined in `services.yml` and can be accessed using the service map functions.

## Environment Variables

### Required Variables

- `MYSQL_HOST` - MySQL database host
- `MYSQL_USER` - MySQL username
- `MYSQL_PASSWORD` - MySQL password
- `MYSQL_DATABASE` - Main database name
- `MYSQL_CORE_DATABASE` - Core database name
- `REDIS_HOST` - Redis server host
- `REDIS_PORT` - Redis server port
- `GOOGLE_API_KEY` - Google Gemini API key
- `JWT_SECRET` - Secret key for JWT tokens

### Optional Variables

- `DEBUG` - Enable debug mode (default: false)
- `USE_SQL_PROXY` - Use Google Cloud SQL proxy (default: false)
- `CLOUDSQL_INSTANCE` - Cloud SQL instance connection string
- `RESEND_API_KEY` - Resend email service API key

## Development Features

- **Auto-reload**: Development server automatically reloads on code changes
- **Debug Mode**: Enhanced logging and error reporting in development
- **Interactive API Docs**: FastAPI automatically generates interactive documentation
- **Health Checks**: Built-in health check endpoint for monitoring

## Project Structure

```
backend/
├── main.py              # Application entry point
├── scripts.py           # Server startup scripts
├── pyproject.toml       # Poetry configuration
├── routers/            # API route handlers
├── services/           # Business logic services
├── models/             # Data models
├── database/           # Database configuration
├── middlewares/        # Custom middleware
├── utils/              # Utility functions
└── tests/              # Test files
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure MySQL is running and accessible
   - Verify database credentials in `.env` file
   - Check if the specified databases exist

2. **Redis Connection Errors**
   - Ensure Redis server is running
   - Verify Redis host and port configuration

3. **Port Already in Use**
   - Default port is 8000, change in `scripts.py` if needed
   - Kill existing processes using the port

4. **Missing Environment Variables**
   - Check that all required variables are set in `.env`
   - The application will log warnings for missing optional variables

### Logs

- Application logs are structured using `structlog`
- In development mode, logs include debug information
- Check console output for startup status and errors 