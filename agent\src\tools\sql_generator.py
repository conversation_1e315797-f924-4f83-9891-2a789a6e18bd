"""SQL query generation module for development economics research."""

from __future__ import annotations

import json
import logging
import time
import uuid
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import aiohttp
import pandas as pd
from pydantic import BaseModel
from sqlalchemy import text

from src.agent.config import DatasetSettings
from src.tools.base import Tool
from src.tools.entity_extractor import EntityMention, ExtractedEntities
from src.utils.db import create_db_engine
from src.utils.file_management import DatasetManager

# -----------------------------------------------------------------------------
# Logging & constants
# -----------------------------------------------------------------------------

logger = logging.getLogger("tool.sql_generator")
SQL_API_BASE_URL = "https://text2sql-din4qs2qka-uc.a.run.app"
MAX_VALIDATION_ITERATIONS = 5

_TOKEN_KEYS = ("prompt_tokens", "completion_tokens", "thoughts_token_count")


def _ensure_token_keys(md: Dict[str, Any] | None) -> Dict[str, int]:
    """Return a dict containing the three token keys (fill missing with 0)."""
    md = md or {}
    for k in _TOKEN_KEYS:
        md.setdefault(k, 0)
    return md

# -----------------------------------------------------------------------------
# Models (unchanged except default metadata)
# -----------------------------------------------------------------------------

class QueryResult(BaseModel):
    user_query: str
    dataset: Optional[str] = None
    row_count: int
    unique_papers: int
    paper_ids: Dict[str, Any]
    dict_rows: List[Dict[str, Any]]
    execution_time: float
    metadata: Dict[str, Any] = {}

    def model_post_init(self, __context: Any) -> None:  # noqa: D401, N802
        self.metadata.setdefault("prompt_tokens", 0)
        self.metadata.setdefault("completion_tokens", 0)
        self.metadata.setdefault("thoughts_token_count", 0)

    def __str__(self) -> str:
        """String representation of the query result."""
        base_info = {
            "user_query": self.metadata.get("sql_query", {}).get(
                "user_query", self.user_query
            ),
            "dataset": self.dataset,
            "row_count": self.row_count,
            "unique_papers": self.unique_papers,
            "execution_time": self.execution_time,
        }

        input_rag = json.dumps(base_info, indent=4)

        input_structured_data = json.dumps(
            {
                **base_info,
                "entities": "ExtractedEntities(user_query: str, country_codes: List[str], "
                "delivery_strategies: List[EntityMention], interventions: List[EntityMention], "
                "outcomes: List[EntityMention], sectors: List[EntityMention], "
                "target_populations: List[EntityMention], extended_interventions: List[EntityMention], "
                "extended_outcomes: List[EntityMention], extended_intervention_sectors: List[EntityMention], "
                "extended_intervention_target_populations: List[EntityMention], "
                "extended_outcome_sectors: List[EntityMention], "
                "extended_outcome_target_populations: List[EntityMention])",
            },
            indent=4,
        )

        return (
            f"Query executed successfully in {self.execution_time:.2f}s.\n"
            f"Found {self.row_count} data points across {self.unique_papers} unique papers.\n"
            f"Paper ids and figures dataset saved at {self.dataset}\n"
            f"Input for the RAG Searcher (if necessary):\n{input_rag}\n"
            f"Input for the Structured Data Organizer (if necessary):\n{input_structured_data}"
        )

    def to_dataframe(self) -> pd.DataFrame:
        """Convert query data to a pandas DataFrame."""
        return pd.DataFrame(self.dict_rows)

    def to_dict(self) -> Dict[str, Any]:
        """Convert query result to a dictionary."""
        return self.dict()

    def get_paper_combined_ids(self) -> List[str]:
        """Get list of unique paper IDs."""
        return list(self.paper_ids.keys())

class SQLQuery(BaseModel):
    user_query: str
    sql_query: str
    reduced_scope: bool = False
    metadata: Dict[str, Any] = {}

    def model_post_init(self, __context: Any) -> None:  # noqa: N802,D401
        self.metadata = _ensure_token_keys(self.metadata)

    def __str__(self) -> str:
        """String representation of the query."""
        output = json.dumps(
            {
                "user_query": self.user_query,
                "sql_query": self.sql_query,
                "reduced_scope": self.reduced_scope,
            },
            indent=4,
        )
        scope_note = " (with reduced scope)" if self.reduced_scope else ""
        return (
            f"We have just generated the SQL query{scope_note}.\n"
            f"Original Query: {self.user_query}\n"
            f"Output: SQLQuery(user_query={self.user_query}, sql_query={self.sql_query})"
            f"Input for the SQL Executor:\n{output}"
        )


@dataclass
class Text2SQLPayload:
    user_query: str
    country_codes: List[str]
    intervention_sectors: List[EntityMention]
    intervention_target_populations: List[EntityMention]
    outcome_sectors: List[EntityMention]
    outcome_target_populations: List[EntityMention]
    interventions: List[EntityMention]
    outcomes: List[EntityMention]

    def _em_to_dict(self, ems: List[EntityMention]):
        return [
            {
                "id": e.id,
                "label": e.label,
                "mention": e.mention,
                "short_label": e.short_label,
            }
            for e in ems
        ]

    def to_dict(self) -> Dict[str, Any]:
        base = {
            "user_query": self.user_query,
            "country_codes": self.country_codes,
        }
        for fld in [
            "interventions",
            "outcomes",
            "intervention_sectors",
            "intervention_target_populations",
            "outcome_sectors",
            "outcome_target_populations",
        ]:
            base[fld] = self._em_to_dict(getattr(self, fld))
        return base



class Text2SQLClient:
    def __init__(self, base_url: str = SQL_API_BASE_URL, session=None):
        self.base_url = base_url.rstrip("/")
        self.generate_endpoint = f"{self.base_url}/generate-sql/"
        self.correct_endpoint = f"{self.base_url}/correct-sql/"
        self.session = session
        self.owns_session = False

    async def _get_session(self):
        if self.session is not None and not self.session.closed:
            return self.session
        self.session = aiohttp.ClientSession()
        self.owns_session = True
        return self.session

    async def _post(self, endpoint: str, payload: Dict[str, Any]):
        sess = await self._get_session()
        async with sess.post(endpoint, json=payload, headers={"Content-Type": "application/json"}) as resp:
            resp.raise_for_status()
            data = await resp.json()
            data.setdefault("metadata", {})
            data["metadata"] = _ensure_token_keys(data["metadata"])
            return data

    async def generate_sql(self, payload: Text2SQLPayload) -> Dict[str, Any]:
        return await self._post(self.generate_endpoint, payload.to_dict())

    async def correct_sql(self, user_query: str, sql_query: str, entities: Dict[str, Any], error: str):
        payload = {
            "user_query": user_query,
            "sql_query": sql_query,
            "entities": entities,
            "error_message": error,
        }
        return await self._post(self.correct_endpoint, payload)

    async def cleanup(self):
        if self.owns_session and self.session and not self.session.closed:
            await self.session.close()
            self.session = None



# -----------------------------------------------------------------------------
# SQLGenerator tool
# -----------------------------------------------------------------------------


class SQLGenerator(Tool):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            name="sql_generator",
            description="Create SQL queries to find relevant research data and execute them.",
            func=self.generate,
            arguments=[("entities", "ExtractedEntities | Dict")],
            outputs=[("query_result", "QueryResult")],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.engine = create_db_engine()
        self.dataset_manager = DatasetManager(DatasetSettings())
        self.session = config.get("session")
        self.client = Text2SQLClient(session=self.session)
        self.logger = logging.getLogger(f"tool.{self.name}")


    # ------------------------------------------------------------------
    # Helper: validate SQL by executing, possibly correcting
    # ------------------------------------------------------------------

    def _extract_extended_entities(
        self, entities_dict: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], str]:
        """Extract and format extended entities with user-friendly message."""
        extended_entities_dict = {
            key: entities_dict.get(key, [])[:3]
            for key in [
                "extended_interventions",
                "extended_outcomes",
                "extended_intervention_sectors",
                "extended_outcome_sectors",
                "extended_intervention_target_populations",
                "extended_outcome_target_populations",
            ]
        }

        # Format extended entities for user-friendly display
        formatted_entities = []
        entity_mappings = {
            "extended_interventions": "Interventions",
            "extended_outcomes": "Outcomes",
            "extended_intervention_sectors": "Intervention sectors",
            "extended_outcome_sectors": "Outcome sectors",
        }

        for key, label in entity_mappings.items():
            if extended_entities_dict[key]:
                labels = [e["label"] for e in extended_entities_dict[key]]
                formatted_entities.append(f"{label}: {', '.join(labels)}")

        message = (
            "No data found with current search terms. Consider using these extended search terms: "
            + ", ".join(formatted_entities)
        )

        return extended_entities_dict, message

    async def _validate_query(
        self, user_query: str, sql_query: str, entities: Dict[str, Any]
    ) -> Tuple[str, bool, str, Dict[str, Any], Optional[List[Any]]]:
        """Validate and potentially correct the SQL query.

        Returns:
            Tuple containing:
            - Validated/corrected SQL query
            - Boolean indicating if corrections were made
            - Additional message about data availability or suggestions
            - Dictionary with extended entities that could be used (if no data found)
            - Query results (if successful) or None
        """
        current_sql_query = sql_query

        # First try the original query
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(current_sql_query))
                rows = result.fetchall()

                # If we have data, return the original query with results
                if len(rows) > 0:
                    return (
                        current_sql_query,
                        False,
                        "Query executed successfully.",
                        {},
                        rows,
                    )

                # If no data, check for extended entities
                logger.info(
                    "No data found with original query, trying extended entities"
                )
                entities_dict = entities.to_dict()

                has_extended_entities = any(
                    entities_dict.get(key, [])
                    for key in [
                        "extended_interventions",
                        "extended_outcomes",
                        "extended_intervention_sectors",
                        "extended_outcome_sectors",
                        "extended_intervention_target_populations",
                        "extended_outcome_target_populations",
                    ]
                )

                if has_extended_entities:
                    extended_entities_dict, message = self._extract_extended_entities(
                        entities_dict
                    )
                    return (
                        current_sql_query,
                        False,
                        message,
                        extended_entities_dict,
                        rows,
                    )

                return (
                    current_sql_query,
                    False,
                    "No data found in the database matching your criteria, and no extended entities available.",
                    {},
                    rows,
                )

        except Exception as e:
            error_msg = str(e)
            return (current_sql_query, True, "Query failed with error: " + error_msg, {}, None)

        return (sql_query, False, "No data found.", {}, None)

    def _convert_entity(
        self, entities: Union[Dict[str, Any], ExtractedEntities, str]
    ) -> ExtractedEntities:
        """Convert API response to ExtractedEntities object."""

        if isinstance(entities, ExtractedEntities):
            return entities

        if isinstance(entities, str):
            try:
                entities = json.loads(entities)
            except json.JSONDecodeError:
                raise ValueError(f"Failed to parse entities: {entities}")

        try:
            if "interventions" in entities and entities["interventions"]:
                _ = [
                    intervention.pop("closest_group", None)
                    for intervention in entities.get("interventions", [])
                ]
            if "outcomes" in entities and entities["outcomes"]:
                _ = [
                    outcome.pop("closest_group", None)
                    for outcome in entities.get("outcomes", [])
                ]
            # Convert snake_case API keys to kebab-case
            mapped_response = {
                "user_query": entities.get("user_query"),
                "country_codes": entities.get("country_codes", []),
                "interventions": [
                    EntityMention(**intervention)
                    for intervention in entities.get("interventions", [])
                ],
                "outcomes": [
                    EntityMention(**outcome) for outcome in entities.get("outcomes", [])
                ],
                "intervention_sectors": [
                    EntityMention(**sector)
                    for sector in entities.get("intervention_sectors", [])
                ],
                "intervention_target_populations": [
                    EntityMention(**population)
                    for population in entities.get(
                        "intervention_target_populations", []
                    )
                ],
                "outcome_sectors": [
                    EntityMention(**sector)
                    for sector in entities.get("outcome_sectors", [])
                ],
                "outcome_target_populations": [
                    EntityMention(**population)
                    for population in entities.get("outcome_target_populations", [])
                ],
            }
            return ExtractedEntities(**mapped_response)
        except Exception as e:
            if self.verbose:
                logger.error(f"Error converting API response: {e}")
            raise ValueError(f"Failed to convert API response: {e}")



    def _pre_process_fix_temporal(
        self, dict_rows: List[Dict[str, Any]], entities: ExtractedEntities
    ) -> List[Dict[str, Any]]:
        """Pre-process rows to map entity labels, short_labels, and definitions from extracted entities."""
        if not dict_rows:
            return dict_rows

        # Create lookup dictionaries for all entity types
        entity_lookup = {}

        # Add all entity types to lookup
        for field_name in entities._ENTITY_FIELDS:
            field_entities = getattr(entities, field_name, [])
            for entity in field_entities:
                entity_id = str(entity.id)
                entity_lookup[entity_id] = entity

        def update_tags(
            ids_str: str, labels_str: str, short_labels_str: str, definitions_str: str
        ) -> tuple:
            """Update tag strings based on entity lookup."""
            if not ids_str:
                return labels_str, short_labels_str, definitions_str

            ids = ids_str.split(";")
            labels = labels_str.split(";") if labels_str else [""] * len(ids)
            short_labels = (
                short_labels_str.split(";") if short_labels_str else [""] * len(ids)
            )
            definitions = (
                definitions_str.split(";") if definitions_str else [""] * len(ids)
            )

            # Ensure all lists have the same length
            max_len = len(ids)
            labels = labels + [""] * (max_len - len(labels))
            short_labels = short_labels + [""] * (max_len - len(short_labels))
            definitions = definitions + [""] * (max_len - len(definitions))

            updated_labels = []
            updated_short_labels = []
            updated_definitions = []

            for i, tag_id in enumerate(ids):
                tag_id = tag_id.strip()
                if tag_id in entity_lookup:
                    entity = entity_lookup[tag_id]
                    updated_labels.append(entity.label if entity.label else labels[i])
                    updated_short_labels.append(
                        entity.short_label if entity.short_label else short_labels[i]
                    )
                    updated_definitions.append(
                        entity.definition if entity.definition else definitions[i]
                    )
                else:
                    # Keep original values if no match found
                    updated_labels.append(labels[i])
                    updated_short_labels.append(short_labels[i])
                    updated_definitions.append(definitions[i])

            return (
                ";".join(updated_labels),
                ";".join(updated_short_labels),
                ";".join(updated_definitions),
            )

        # Process each row
        processed_rows = []
        for row in dict_rows:
            new_row = row.copy()

            # Update intervention tags
            if "intervention_tag_ids" in row:
                updated_labels, updated_short_labels, updated_definitions = update_tags(
                    row.get("intervention_tag_ids", ""),
                    row.get("intervention_tag_labels", ""),
                    row.get("intervention_tag_short_labels", ""),
                    row.get("intervention_tag_definitions", ""),
                )
                new_row["intervention_tag_labels"] = updated_labels
                new_row["intervention_tag_short_labels"] = updated_short_labels
                new_row["intervention_tag_definitions"] = updated_definitions

            # Update outcome tags
            if "outcome_tag_ids" in row:
                updated_labels, updated_short_labels, updated_definitions = update_tags(
                    row.get("outcome_tag_ids", ""),
                    row.get("outcome_tag_labels", ""),
                    row.get("outcome_tag_short_labels", ""),
                    row.get(
                        "outcome_tag_definition", ""
                    ),  # Note: singular 'definition'
                )
                new_row["outcome_tag_labels"] = updated_labels
                new_row["outcome_tag_short_labels"] = updated_short_labels
                new_row["outcome_tag_definition"] = (
                    updated_definitions  # Note: singular 'definition'
                )

            processed_rows.append(new_row)

        return processed_rows


    # ------------------------------------------------------------------
    # Public coroutine
    # ------------------------------------------------------------------

    async def generate(self, entities: Union[ExtractedEntities, Dict[str, Any]]):
        entities = self._convert_entity(entities)

        # 1) Build payload for Text2SQL
        payload = Text2SQLPayload(
            user_query=entities.user_query,
            country_codes=entities.country_codes,
            interventions=entities.interventions,
            outcomes=entities.outcomes,
            intervention_sectors=entities.intervention_sectors,
            intervention_target_populations=entities.intervention_target_populations,
            outcome_sectors=entities.outcome_sectors,
            outcome_target_populations=entities.outcome_target_populations,
        )

        # 2) Call generation API
        api_resp = await self.client.generate_sql(payload)
        sql_query = api_resp.get("sql_query", "")
        md = api_resp.get("metadata", {})

        # 3) Validate & maybe correct
        validated_query, was_corrected, msg, ext_entities, cached_rows = await self._validate_query(
            entities.user_query, sql_query, entities
        )

        # 4) Build SQLQuery object
        sql_query_obj = SQLQuery(
            user_query=entities.user_query,
            sql_query=validated_query,
            reduced_scope=False,
            metadata={
                **md,
                "was_corrected": was_corrected,
                "message": msg,
                "entities": entities.dict(),
                "extended_entities": ext_entities,
            },
        )

        # 5) Execute query (or use cached rows)
        return await self.execute_sql_query(sql_query_obj, cached_rows)

    # ------------------------------------------------------------------
    # Execute & post‑process
    # ------------------------------------------------------------------

    async def execute_sql_query(self, sql_query: SQLQuery, cached_rows: Optional[List[Any]] = None) -> QueryResult:
        start = time.time()
        sql = sql_query.sql_query
        if cached_rows is None:
            with self.engine.connect() as conn:
                rows = conn.execute(text(sql)).fetchall()
        else:
            rows = cached_rows
        

        dict_rows = [
            {
                k: v
                for k, v in row._mapping.items()
                if k not in ["intervention_start_date", "intervention_end_date"]
            }
            for row in rows
        ]

        # dict_rows = [dict(row._mapping) for row in rows]
        paper_ids = {row.get("paper_combined_id", str(i)): row.get("title", "") for i, row in enumerate(dict_rows)}

        # Pre-process rows to fix temporal mapping with extracted entities
        entities = self._convert_entity(sql_query.metadata.get("entities", {}))
        dict_rows = self._pre_process_fix_temporal(dict_rows, entities)


        # save dataset
        dataset_uri = await self.dataset_manager.save_dataset(dict_rows, f"datasets/{uuid.uuid4()}.json")

        result_md = {**sql_query.metadata}
        elapsed = time.time() - start
        return QueryResult(
            user_query=sql_query.user_query,
            dataset=dataset_uri,
            row_count=len(dict_rows),
            unique_papers=len(paper_ids),
            paper_ids=paper_ids,
            dict_rows=dict_rows,
            execution_time=elapsed,
            metadata=result_md,
        )

    # ------------------------------------------------------------------
    # Cleanup
    # ------------------------------------------------------------------

    async def cleanup(self):
        await self.client.cleanup()
