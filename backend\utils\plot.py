import json
from typing import Optional, Any
from database.models import Plot


def graceful_json_parse(data: Any) -> Optional[dict]:
    """
    Attempt to parse JSON data, returning None if parsing fails.
    If data is already a dictionary, return it as is.
    """
    if isinstance(data, dict):
        return data
    try:
        return json.loads(data)
    except json.JSONDecodeError:
        return None


def get_download_links(conversation_id: str, message_id: str) -> dict:
    """
    Generate download links for CSV and JSON formats.
    """
    return {
        "csv": f"/conversations/{conversation_id}/d/{message_id}?fmt=csv",
        "json": f"/conversations/{conversation_id}/d/{message_id}?fmt=json",
    }


def get_plot_for_message(
    conversation_id: str, message_id: str, message_plot: Plot
) -> dict:

    if message_plot.data is None:
        return None

    plot_title = message_plot.title
    plot_data = graceful_json_parse(message_plot.data)

    if plot_data is None:
        return None

    download_links = get_download_links(conversation_id, message_id)
    plot = {
        "title": plot_title,
        "data": plot_data,
        "download_links": download_links,
    }
    return plot
