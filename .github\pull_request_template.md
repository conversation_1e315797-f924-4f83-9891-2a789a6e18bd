## 🚀 Impact & Purpose
_Why is this PR important?_
Briefly describe what problem this PR solves or what value it adds.

---

## 📄 Overview
What does this PR do at a high level?
Summarize major goals or changes in 1–2 sentences.

---

## ✨ Key Features

- [ ] Feature/logic 1 (e.g., “Added fallback entity suggestion when no data is returned”)
- [ ] Feature/logic 2 (e.g., “Rewrote `agent_react.prompt` to support conversational flow”)
- [ ] Refactors or structural changes (e.g., renamed method X to Y)

---

## 🧪 Testing Instructions

### ✅ Manual Testing

Describe the manual testing steps you performed to verify the PR works as expected.

Command to run the code:
```bash
poetry run python ...
```

Expected Behavior:

Describe the expected behavior.


### 🗂️ Code Changes Summary

- `main.py`: Added `xxx` logic
- `agent_react.prompt`: Enhanced prompt for `xxx`
- ...

### 🧠 Review Notes

#### Reviewer Checklist
- [ ]  Code is readable and modular
- [ ]  New logic is documented or explained
- [ ]  PR tested and works as described
- [ ]  Comments or TODOs removed
- [ ]  Prompt or logic changes align with project goals

#### Questions or Follow-Up Suggestions

Add any open questions for reviewers or ideas for future improvements.

### 📌 Future Work (Optional)
- [ ]  Task 1
- [ ]  Task 2
- [ ]  Task 3
