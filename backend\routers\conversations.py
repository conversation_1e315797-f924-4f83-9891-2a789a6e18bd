import asyncio
import uuid
from collections import defaultdict
from typing import Optional
from datetime import datetime
import structlog
import pandas as pd
from fastapi import APIRouter, BackgroundTasks, Request
from fastapi.responses import PlainTextResponse, StreamingResponse
from pydantic import BaseModel
from services.agent import AgentService
from services.conversation import (
    add_message_feedback,
    add_message_plot,
    add_message_sources,
    clear_user_conversations,
    fetch_conversations,
    fetch_downloadable_plot_data,
    fetch_message,
    fetch_message_plot,
    fetch_message_sources,
    fetch_messages_by_conversation_id,
    fetch_plots_by_message_ids,
    fetch_sources_by_message_ids,
    gen_and_update_conversation_title,
    initialise_new_conversation,
    initialize_information_message,
    insert_conversation_message,
    update_message,
)
from services.paper import get_paper_abstracts_by_ids
from utils.measure import measure_async_time
from utils.plot import get_plot_for_message
from utils.text import sanitize_plot_text

router = APIRouter()

logger = structlog.get_logger(__name__)


class MessageBody(BaseModel):
    message: str


class FeedbackMessageBody(BaseModel):
    reaction: str
    feedbackText: Optional[str]
    selectedChips: Optional[str]


@router.get("/conversations")
@measure_async_time
async def get_conversations(request: Request):
    """Retrieve conversations for the authenticated user."""
    user_id = request.state.user_id

    conversations = await fetch_conversations(user_id=user_id)
    history = []
    for conversation in conversations:
        if conversation.title is None:
            continue

        history.append(
            {
                "id": conversation.id,
                "title": conversation.display_title(),
                "created_at": conversation.created_at,
            }
        )
    return {"success": True, "data": {"conversations": history}}


@router.post("/conversations/initialise")
@measure_async_time
async def post_conversation_initialise(request: Request):
    user_id = request.state.user_id

    conversation_id = str(uuid.uuid4())

    return {
        "success": True,
        "data": {
            "user_id": user_id,
            "conversation_id": conversation_id,
        },
    }


@router.post("/conversations/{conversation_id}")
@measure_async_time
async def post_conversation(
    request: Request,
    conversation_id: uuid.UUID,
    body: MessageBody,
    background_tasks: BackgroundTasks,
):
    user_id = request.state.user_id
    text = body.message

    await initialise_new_conversation(user_id=user_id, conversation_id=conversation_id)

    (information_message, data) = await asyncio.gather(
        initialize_information_message(
            conversation_id=conversation_id,
            query_values={"query": text},
        ),
        insert_conversation_message(
            conversation_id=conversation_id,
            text=text,
        ),
    )

    background_tasks.add_task(
        gen_and_update_conversation_title, conversation_id=conversation_id, text=text
    )

    return {
        "success": True,
        "data": {
            "message_id": data["message_id"],
            "information_message_id": information_message["message_id"],
        },
    }


@router.get("/conversations/{conversation_id}")
@measure_async_time
async def get_conversation(
    conversation_id: uuid.UUID,
):
    """Fetches the conversation details for a given conversation ID."""

    messages = await fetch_messages_by_conversation_id(
        conversation_id=conversation_id,
    )
    message_ids = [str(message.id) for message in messages]
    all_plots, all_sources = await asyncio.gather(
        fetch_plots_by_message_ids(message_ids),
        fetch_sources_by_message_ids(message_ids),
    )
    plots_map = {}
    for plot in all_plots:
        plots_map[plot.message_id] = plot

    sources_map = defaultdict(list)
    for idx, source in enumerate(all_sources):
        citation_data = (
            source.citation
            if isinstance(source.citation, dict)
            else {"text": source.citation}
        )
        formatted_source = {
            "position": source.position,
            "id": source.id,
            "title": source.title,
            "paper_id": source.paper_id,
            "short_paper_id": source.short_paper_id,
            "doi_url": source.doi_url,
            "citation": citation_data.get("text", source.citation),
            "journal_name": citation_data.get("journal_name"),
            "abstract": citation_data.get("abstract"),
            "country": citation_data.get("country"),
            "region": citation_data.get("region"),
            "income_group": citation_data.get("income_group"),
            "quality_score": citation_data.get("quality_score"),
            "quality_score_category": citation_data.get("quality_score_category"),
            "sector": citation_data.get("sector"),
            "intervention_name": citation_data.get("intervention_name"),
            "intervention_details": citation_data.get("intervention_details"),
            "outcome_name": citation_data.get("outcome_name"),
            "outcome_details": citation_data.get("outcome_details"),
        }
        sources_map[source.message_id].append(formatted_source)

    thread = []
    for message in messages:
        message_id = message.id
        has_plot = False
        has_sources = False
        if message.summary_configs:
            has_plot = message.summary_configs.get("has_plot_data", False)
            has_sources = message.summary_configs.get("has_sources", False)

        thread_message = {
            "id": message_id,
            "type": message.type,
            "author": message.author,
            "text": message.text,
            "created_at": message.created_at,
            "updated_at": message.updated_at,
            "has_plot": has_plot,
            "has_sources": has_sources,
        }
        if message.type == "information":
            if message_id in plots_map:
                message_plot = plots_map[message_id]
                thread_message["plot"] = get_plot_for_message(
                    conversation_id, message_id, message_plot
                )
            if message_id in sources_map:
                thread_message["sources"] = sources_map[message_id]
        thread.append(thread_message)

    return {"success": True, "data": {"messages": thread}}


@router.get("/conversations/{conversation_id}/d/{message_id}")
@measure_async_time
async def get_conversation_message_downloadable_data(
    conversation_id: uuid.UUID,
    message_id: uuid.UUID,
    fmt: str,
):
    """Fetches downloadable message data in the specified format (csv or json)."""

    if fmt not in ["csv", "json"]:
        return {
            "success": False,
            "data": {"message": "Invalid format. Supported formats are csv and json."},
        }

    downloadable_plot_data = await fetch_downloadable_plot_data(
        message_id=message_id,
    )

    try:
        media_type = "text/plain"
        filename = f"{conversation_id}_{message_id}.json"
        if fmt == "csv":
            media_type = "text/csv"
            filename = f"{conversation_id}_{message_id}.csv"
            df = pd.DataFrame(downloadable_plot_data)
            downloadable_plot_data = iter([df.to_csv(index=False)])
            return StreamingResponse(
                downloadable_plot_data,
                media_type=media_type,
                headers={"Content-Disposition": f"attachment; filename={filename}"},
            )
        return downloadable_plot_data
    except Exception as e:
        return {"success": False, "data": {"message": str(e)}}


@router.get("/conversations/{conversation_id}/messages/{message_id}")
@measure_async_time
async def get_conversation_message(
    message_id: uuid.UUID,
):
    """Fetches a specific message from a conversation."""

    message = await fetch_message(
        message_id=message_id,
    )
    if message is None:
        return {
            "success": False,
            "data": {"message": "Message not found."},
        }

    sources_data = await fetch_message_sources(
        message_id=message_id,
    )

    plot = await fetch_message_plot(
        message_id=message_id,
    )
    plot_data = None
    if plot:
        plot_data = plot.data_as_json()

    fetched_message = {
        "id": str(message.id),
        "conversation_id": str(message.conversation_id),
        "text": message.text,
        "author": message.author,
        "type": message.type,
        "created_at": message.created_at,
        "updated_at": message.updated_at,
        "plot": plot_data,
        "sources": sources_data,
    }
    return {"success": True, "data": fetched_message}


@router.post("/conversations/{conversation_id}/messages/{message_id}/feedback")
@measure_async_time
async def post_conversation_message_feedback(
    message_id: str,
    body: FeedbackMessageBody,
):
    """Submits feedback for a specific message in a conversation."""

    data = await add_message_feedback(
        message_id=message_id,
        reaction=body.reaction,
        message=body.feedbackText,
        tags=body.selectedChips,
    )

    return {"success": True, "data": data}


@router.get("/conversations/{conversation_id}/messages/{message_id}/summary")
@measure_async_time
async def get_conversation_message_summary(
    conversation_id: str,
    message_id: str,
    background_tasks: BackgroundTasks
):
    """Fetches the summary for a specific message in a conversation."""

    summary = await fetch_message(message_id=message_id)

    if summary.streaming_finished_at is not None:
        return PlainTextResponse(summary.text)

    query = summary.query_values["query"]

    agent_service = AgentService(conversation_id=conversation_id, message_id=message_id, query=query)

    async def finalize_summary():
        try:
            response = await agent_service.get_agent_response_data()
            if response is None:
                logger.error(f"get_agent_response_data responded with None: conversation_id={conversation_id} message_id={message_id}")
                return

            response_text = response.sanitized_summary()

            if response.has_plot_data():
                plot_data = response.plot_data()
                await add_message_plot(
                    message_id=message_id,
                    title="Plot title",
                    data=plot_data.to_json(),
                )

            if response.has_sources():
                sources_data = response.sources_data()
                paper_ids = [
                    source["paper_id"]
                    for source in sources_data
                    if source.get("paper_id") is not None
                ]

                if len(paper_ids) > 0:
                    paper_id_abstracts = await get_paper_abstracts_by_ids(paper_ids)
                    paper_id_abstract_dict = {
                        paper["id"]: paper["abstract_summary"] or paper["abstract"]
                        for paper in paper_id_abstracts
                    }
                else:
                    paper_id_abstract_dict = {}

                sources_with_abstracts = []
                for source in sources_data:
                    if source.get("paper_id") is not None:
                        paper_id = int(source["paper_id"])
                        if paper_id in paper_id_abstract_dict:
                            if "citation" not in source:
                                source["citation"] = {}
                            source["citation"]["abstract"] = paper_id_abstract_dict.get(
                                paper_id, ""
                            )
                    sources_with_abstracts.append(source)

                await add_message_sources(
                    message_id=message_id,
                    sources=sources_with_abstracts,
                )

            summary_configs = {
                "has_plot_data": response.has_plot_data(),
                "has_sources": response.has_sources(),
            }

            await update_message(
                message_id=message_id,
                updated_data={
                    "text": response_text,
                    "streaming_finished_at": datetime.now(),
                    "summary_configs": summary_configs,
                },
            )
        except Exception as e:
            logger.error("Failed to finalize summary", error=e)

    async def summary_streamer():
        try:
            count = 0
            async for data in await agent_service.generate_summary():
                if count < 1:
                    count += 1
                    background_tasks.add_task(
                        finalize_summary
                    )
                yield sanitize_plot_text(data)
        except Exception as e:
            logger.error("Error in summary_streamer", error=e)
            yield f"data: error An error occured while streaming. Please retry.\n\n"

    return StreamingResponse(
        summary_streamer(),
        media_type="text/event-stream"
    )

@router.get("/conversations/{conversation_id}/messages/{message_id}/plot")
@measure_async_time
async def get_conversation_message_plot(
    conversation_id: str,
    message_id: str,
):
    """Fetches the plot data for a specific message in a conversation."""

    data = await fetch_message_plot(
        message_id=message_id,
    )
    plot_data = None
    if data:
        download_links = {
            "csv": f"/conversations/{conversation_id}/d/{message_id}?fmt=csv",
            "json": f"/conversations/{conversation_id}/d/{message_id}?fmt=json",
        }
        plot_data = {
            "id": str(data.id),
            "title": data.title,
            "data": data.data_as_json(),
            "download_links": download_links,
        }
    return {"success": True, "data": plot_data}


@router.get("/conversations/{conversation_id}/messages/{message_id}/sources")
@measure_async_time
async def get_conversation_message_sources(
    message_id: str,
):
    """Fetches the sources for a specific message in a conversation."""

    sources_data = await fetch_message_sources(
        message_id=message_id,
    )
    sources = []
    for source in sources_data:
        citation_data = (
            source.citation
            if isinstance(source.citation, dict)
            else {"text": source.citation}
        )

        sources.append(
            {
                "id": source.id,
                "position": source.position,
                "paper_id": source.paper_id,
                "short_paper_id": source.short_paper_id,
                "title": source.title,
                "doi_url": source.doi_url,
                "citation": citation_data.get("text", source.citation),
                "journal_name": citation_data.get("journal_name"),
                "abstract": citation_data.get("abstract"),
                "country": citation_data.get("country"),
                "region": citation_data.get("region"),
                "income_group": citation_data.get("income_group"),
                "quality_score": citation_data.get("quality_score"),
                "quality_score_category": citation_data.get("quality_score_category"),
                "sector": citation_data.get("sector"),
                "intervention_name": citation_data.get("intervention_name"),
                "intervention_details": citation_data.get("intervention_details"),
                "outcome_name": citation_data.get("outcome_name"),
                "outcome_details": citation_data.get("outcome_details"),
            }
        )
    return {"success": True, "data": {"sources": sources}}


@router.delete("/conversations/clear")
async def clear_conversations(request: Request):
    """Clears all conversations for the authenticated user."""
    user_id = request.state.user_id

    await clear_user_conversations(user_id)

    return {"success": True, "message": "All conversations have been cleared."}
