import { PlotDataInfo, FilterState, FilterOptions, FilterField } from "../../types/ConversationTypes";

export const resolvePlotData = (rawPlotData: any): PlotDataInfo | null => {
    if (rawPlotData && rawPlotData.data && rawPlotData.data.data) {
        return rawPlotData.data.data;
    } else if (rawPlotData && rawPlotData.data) {
        return rawPlotData.data;
    } else if (rawPlotData && rawPlotData.flat_effect_sizes) {
        return rawPlotData;
    }
    return null;
};

export const extractFilterOptionsFromData = (data: PlotDataInfo): FilterOptions => {
    const years = new Set<number>();
    const sectors = new Set<string>();
    const countries = new Set<string>();
    const regions = new Set<string>();
    const incomeGroups = new Set<string>();
    const qualityScoreCategories = new Set<string>();

    if (data?.flat_effect_sizes) {
        data.flat_effect_sizes.forEach(plotItem => {
            if (plotItem.year) years.add(plotItem.year);
            if (plotItem.intervention_sectors) {
                plotItem.intervention_sectors.split(';').forEach(sector => sectors.add(sector.trim()));
            }
            if (plotItem.outcome_sector) {
                plotItem.outcome_sector.split(';').forEach(sector => sectors.add(sector.trim()));
            }
            if (plotItem.country_name) countries.add(plotItem.country_name);
            if (plotItem.region) regions.add(plotItem.region);
            if (plotItem.income_group) incomeGroups.add(plotItem.income_group);
            if (plotItem.quality_score_category) {
                qualityScoreCategories.add(plotItem.quality_score_category.trim());
            }
        });
    }

    const sortedYears = Array.from(years).sort((a, b) => a - b);

    const yearRange: [number, number] = sortedYears.length > 1
        ? [sortedYears[0], sortedYears[sortedYears.length - 1]]
        : [1990, 2025];

    return {
        years: {
            type: 'slider',
            label: 'Year',
            range: yearRange,
        } as FilterField,
        sectors: {
            type: 'chips',
            label: 'Sectors',
            options: Array.from(sectors).sort(),
        } as FilterField,
        countries: {
            type: 'dropdown',
            label: 'Geography',
            options: Array.from(countries).sort(),
        } as FilterField,
        tabs: {
            General: ['years', 'sectors', 'countries'],
            Intervention: [],
            Outcome: [],
        },
    };
};

export const getActiveFiltersCount = (activeFilters: FilterState, filterOptions: FilterOptions): number => {
    let count = 0;

    const yearsConfig = filterOptions.years as FilterField;
    if (activeFilters?.years && yearsConfig?.range && yearsConfig.range.length === 2) {
        const minYear = yearsConfig.range[0];
        const maxYear = yearsConfig.range[1];
        if (activeFilters.years[0] !== minYear || activeFilters.years[1] !== maxYear) {
            count += 1;
        }
    }

    const filterKeys = ['sectors', 'countries', 'regions', 'incomeGroups', 'qualityScoreCategories'];
    filterKeys.forEach(key => {
        if (Array.isArray(activeFilters?.[key]) && activeFilters?.[key]?.length > 0) {
            count += activeFilters[key].length;
        }
    });

    return count;
};