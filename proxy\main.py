from fastapi import FastAPI, HTTPException
import requests
from pydantic import BaseModel

app = FastAPI()

ENTITY_API_URL = "http://10.128.0.2:8086/entity-linking"


class UserTextRequest(BaseModel):
    user_text: str


@app.get("/")
def get_root():
    return {"success": True}


@app.post("/entity-linking")
async def proxy_entity_linking(request: UserTextRequest):
    try:
        response = requests.post(
            ENTITY_API_URL,
            json={"user_text": request.user_text},
            headers={"Content-Type": "application/json"},
            timeout=600,
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=500, detail=f"Error forwarding request: {str(e)}"
        )


@app.get("/health")
async def health_check():
    return {"status": "healthy"}
