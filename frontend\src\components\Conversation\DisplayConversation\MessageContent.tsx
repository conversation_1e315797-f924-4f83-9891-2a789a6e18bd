import React from "react";
import { <PERSON>, Button } from "@mui/material";
import { Message } from "../../../types/ConversationTypes";
import UserAnswerDisplay from "../User/UserAnswerDisplay";
import SummarySection from "../Summary/SummarySection";
import ThumbsFeedback from "../Feedback/ThumbsFeedback";
import TravelExploreIcon from '@mui/icons-material/TravelExplore';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import { RefObject } from "react";

interface MessageContentProps {
    msg: Message;
    index: number;
    lastSystemMessageId: string | null;
    hoveredMessageId: string | null;
    setHoveredMessageId: (id: string | null) => void;
    informationMessageId: string | null;
    displaySystemLoader: boolean;
    streamingEnded: boolean;
    summaryStreamedText: string;
    openRightSection: "sources" | "charts" | null;
    activeInfoMessageIdForPanel: string | null;
    isTwoColumnLayoutWithPanelOpen: boolean;
    localConversationId: string;
    handleViewOnPlotClickedInternal: (payload: any) => void;
    handleViewOnSourceClickedInternal: (payload: any) => void;
    handleOpenSources: (id: string, fromSourcesButton: boolean) => void;
    handleOpenCharts: (id: string, payload: any) => void;
    setDisplaySystemLoader: (flag: boolean) => void;
    setStreamingEnded: (flag: boolean) => void;
    setSummaryStreamedText: (text: string) => void;
    setFullMessageInfoFetched: (msg: Message | null) => void;
    activeQuestionId: string | null;
    lastMessageRef: RefObject<HTMLDivElement>;
    buttonStyle: any;
    theme: any;
    initialConversationMessages: Message[];
}

export const MessageContent: React.FC<MessageContentProps> = ({
    msg,
    index,
    lastSystemMessageId,
    hoveredMessageId,
    setHoveredMessageId,
    informationMessageId,
    displaySystemLoader,
    streamingEnded,
    summaryStreamedText,
    openRightSection,
    activeInfoMessageIdForPanel,
    isTwoColumnLayoutWithPanelOpen,
    localConversationId,
    handleViewOnPlotClickedInternal,
    handleViewOnSourceClickedInternal,
    handleOpenSources,
    handleOpenCharts,
    setDisplaySystemLoader,
    setStreamingEnded,
    setSummaryStreamedText,
    setFullMessageInfoFetched,
    activeQuestionId,
    lastMessageRef,
    buttonStyle,
    theme,
    initialConversationMessages,
}) => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;
    const isCurrentUserMessage = msg.author === 'user' && msg.type === 'answer' && msg?.text && msg?.text.length > 0;
    const displaySourcesButton = msg?.has_sources || false;
    const displayChartsButton = msg?.has_plot || false;
    const shouldFlexStart = msg?.text && msg?.text.length >= 100;
    const isLastSystemMsg = msg.id === lastSystemMessageId;
    const isCurrentInfoMsg = 'system-loading' === msg.id;

    let feedbackText = "";
    let isFeedbackVisible = false;
    let isSourcesChartsVisible = false;

    const showOnHover = msg.type === 'information' && msg.author === 'system' && !!msg?.text && (displaySourcesButton || displayChartsButton || !!msg?.text) && msg.id === hoveredMessageId;

    if (!informationMessageId) {
        feedbackText = msg?.text || "";
        isFeedbackVisible = (isLastSystemMsg && !!msg?.text && !displaySystemLoader) || showOnHover;
        isSourcesChartsVisible = (isLastSystemMsg && !!msg?.text && !displaySystemLoader && (displaySourcesButton || displayChartsButton)) || showOnHover;
    }
    else if (isCurrentInfoMsg) {
        feedbackText = streamingEnded && !displaySystemLoader && !!summaryStreamedText ? summaryStreamedText : "";
        isFeedbackVisible = (streamingEnded && !!summaryStreamedText) || showOnHover;
        isSourcesChartsVisible = (streamingEnded && !!summaryStreamedText && (displaySourcesButton || displayChartsButton)) || showOnHover;
    }
    else if (msg.type === 'information' && msg.author === 'system' && !!msg?.text) {
        feedbackText = msg?.text || "";
        isFeedbackVisible = showOnHover;
        isSourcesChartsVisible = (displaySourcesButton || displayChartsButton) && showOnHover;
    }

    const showSourcesButton = displaySourcesButton && (openRightSection !== 'sources' || activeInfoMessageIdForPanel !== msg.id);
    const showChartsButton = displayChartsButton && (openRightSection !== 'charts' || activeInfoMessageIdForPanel !== msg.id);

    return (
        <Box
            key={msg.id}
            id={msg.id || ''}
            ref={index === initialConversationMessages.length - 1 && activeQuestionId ? lastMessageRef : null}
            className={`message-wrapper ${msg.id === activeQuestionId ? 'active-message' : ''}`}
            sx={{
                display: 'flex',
                alignItems: 'flex-start',
                flexDirection: 'column',
                justifyContent: shouldFlexStart ? 'flex-start' : 'center',
            }}
        >

            {(isCurrentUserMessage) && (
                <Box sx={{ width: '100%' }}>
                    <UserAnswerDisplay
                        key={msg.id}
                        answer={msg}
                        isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                    />
                    <Box sx={{ height: '40px' }} />
                </Box>
            )}
            {msg.type === 'information' && msg.author === 'system' && (
                <Box
                    onMouseEnter={() => setHoveredMessageId(msg.id)}
                    onMouseLeave={() => setHoveredMessageId(null)}
                    sx={{ width: '100%' }}
                    className="system-response"
                >
                    <SummarySection
                        key={msg.id}
                        messageId={index === initialConversationMessages.length - 1 && informationMessageId ? informationMessageId : msg.id}
                        summary={msg}
                        conversationId={localConversationId}
                        informationMessageId={informationMessageId}
                        onViewOnPlotClicked={handleViewOnPlotClickedInternal}
                        onViewOnSourceClicked={handleViewOnSourceClickedInternal}
                        setDisplaySystemLoader={setDisplaySystemLoader}
                        setStreamingEnded={setStreamingEnded}
                        setSummaryStreamedText={setSummaryStreamedText}
                        setFullMessageInfoFetched={setFullMessageInfoFetched}
                    />
                    {((showSourcesButton || showChartsButton) || !!feedbackText) ? (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0 }}>
                            {(showSourcesButton || showChartsButton) && (
                                <Box sx={{ display: 'flex', gap: isMobile ? '8px' : '16px', paddingLeft: isMobileOrTablet ? '0px' : '48px', opacity: isSourcesChartsVisible ? 1 : 0, pointerEvents: isSourcesChartsVisible ? 'auto' : 'none', transition: 'opacity 0.2s ease-in-out' }}>
                                    {showSourcesButton && (
                                        <Button
                                            variant="text"
                                            size="small"
                                            startIcon={<TravelExploreIcon />}
                                            onClick={() => handleOpenSources(msg.id, true)}
                                            sx={{
                                                display: 'inline-flex',
                                                padding: '4px 10px',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                borderRadius: '8px',
                                                border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                                                background: '#FFF',
                                                color: theme.palette.primary.main,
                                                ...buttonStyle,
                                            }}
                                        >
                                            Sources
                                        </Button>
                                    )}
                                    {showChartsButton && (
                                        <Button
                                            variant="text"
                                            size="small"
                                            startIcon={<ShowChartIcon />}
                                            onClick={() => handleOpenCharts(msg.id, null)}
                                            sx={{
                                                display: 'inline-flex',
                                                padding: '4px 10px',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                borderRadius: '8px',
                                                border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
                                                background: '#FFF',
                                                color: theme.palette.primary.main,
                                                ...buttonStyle,
                                            }}
                                        >
                                            Charts
                                        </Button>
                                    )}
                                </Box>
                            )}
                            {!!feedbackText && (
                                <Box sx={{ marginLeft: 'auto' }}>
                                    <ThumbsFeedback
                                        conversationId={localConversationId}
                                        messageId={msg.id}
                                        visible={isFeedbackVisible}
                                        textToCopy={feedbackText}
                                    />
                                </Box>
                            )}
                        </Box>
                    ) : null}
                </Box>
            )}
        </Box>
    );
};