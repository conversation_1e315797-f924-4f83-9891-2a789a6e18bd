# Infrastructure Scripts

This directory contains shell scripts for managing ImpactAI platform deployments, database operations, and environment configuration.

## Quick Start

### Prerequisites
- Google Cloud SDK (`gcloud`) authenticated
- Docker or <PERSON>dman for container builds
- MySQL client for database operations (local sync only)

### Common Commands

```bash
# Deploy services
TARGET=development SERVICE=backend ./deploy-service.sh
TARGET=production SERVICE=agent ./deploy-service.sh --dry-run

# Database operations
./sync-local-database.sh                    # Download and import to local MySQL
./sync-local-database.sh --skip-import      # Download only
./sync-local-database.sh --skip-download    # Import existing file
./sync-production-database.sh --dry-run                      # Validate prod→dev sync

# Environment setup
./fetch-service-key.sh                                       # Get service account for local dev
TARGET=development SERVICE=backend ./check-env.sh            # Fetch environment config
```

## Scripts Overview

| Script | Purpose | Key Features |
|--------|---------|--------------|
| `deploy-service.sh` | Deploy services to Cloud Run | Image building, environment config, VPC setup |
| `check-env.sh` | Fetch secrets from Secret Manager | YAML conversion, environment validation |
| `fetch-service-key.sh` | Download service account keys | Local development setup |
| `sync-production-database.sh` | Sync prod→dev database | Safe export/import, validation |
| `sync-local-database.sh` | Sync dev→local MySQL | Multi-mode, safety checks, progress tracking |

## Environment Variables

### Deployment Scripts
```bash
export TARGET=development    # or testing, production
export SERVICE=backend       # or agent, website
```

### Local Database Sync (`.env` file)
```env
MYSQL_HOST=localhost
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_CORE_DATABASE=impactai-silver-db  # optional
```

## Safety Features

- **Dry-run mode** - All scripts support `--dry-run` for validation
- **Error handling** - Strict error checking with automatic cleanup
- **Host validation** - Database scripts only allow localhost connections
- **Confirmation prompts** - Warns before destructive operations
- **Comprehensive logging** - Color-coded output with detailed progress

## Library Architecture

The `lib/` directory contains shared modules:
- `common.sh` - Core utilities, error handling, validation
- `gcloud.sh` - Google Cloud operations and authentication  
- `deploy.sh` - Cloud Run deployment functions
- `logging.sh` - Color-coded logging system
- `colors.sh` - Terminal color constants
- `container.sh` - Docker/Podman detection

## Help and Documentation

Each script includes comprehensive help:
```bash
./script-name.sh --help
```

For detailed documentation, see `docs/infrastructure.md`.

## Integration

Scripts are integrated with the main Makefile:
```bash
make deploy-backend    # deploy-service.sh
make sync-local-db     # sync-local-database.sh
make fetch-keys        # fetch-service-key.sh
```

## Troubleshooting

### Authentication
```bash
gcloud auth list
gcloud auth application-default login
```

### Common Issues
- **Permission denied**: Check `gcloud auth` and IAM roles
- **MySQL connection**: Verify local MySQL service is running
- **Missing files**: Ensure `.env` file exists for database sync
- **Environment errors**: Validate `TARGET` and `SERVICE` variables

### Exit Codes
- `0` - Success
- `1` - General error
- `2` - Authentication error  
- `3` - Permission error
- `130` - User interruption

For additional help, see the comprehensive documentation in `docs/infrastructure.md`. 