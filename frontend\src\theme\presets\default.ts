import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#002244',
      dark: '#124473',
      light: '#43648B',
      contrast: '#FFFFFF',
      hover: '#002244',
      selected: '#002244',
      focus: '#002244',
      focusVisible: '#002244',
      outlinedBorder: '#002244'
    },
    secondary: {
      main: '#009FDA',
      dark: '#007CB3',
      light: '#32BAEA',
      contrast: '#FFFFFF',
      hover: '#009FDA',
      selected: '#009FDA',
      focus: '#009FDA',
      focusVisible: '#009FDA',
      outlinedBorder: '#009FDA'
    },
    error: {
      main: '#C52816',
      dark: '#C62828',
      light: '#EF5350',
      contrast: '#FFFFFF',
      hover: '#D32F2F',
      selected: '#D32F2F',
      focusVisible: '#D32F2F',
      outlinedBorder: '#D32F2F'
    },
    warning: {
      main: '#CC8500',
      dark: '#EF6C00',
      light: '#FF9800',
      contrast: '#FFFFFF',
      hover: '#EF6C00',
      selected: '#EF6C00',
      focusVisible: '#EF6C00',
      outlinedBorder: '#EF6C00'
    },
    info: {
      main: '#0071BC',
      dark: '#01579B',
      light: '#03A9F4',
      contrast: '#FFFFFF',
      hover: '#0288D1',
      selected: '#0288D1',
      focusVisible: '#0288D1',
      outlinedBorder: '#0288D1'
    },
    success: {
      main: '#3A8604',
      dark: '#1B5E20',
      light: '#4CAF50',
      contrast: '#FFFFFF',
      hover: '#2E7D32',
      selected: '#2E7D32',
      focusVisible: '#2E7D32',
      outlinedBorder: '#2E7D32'
    }
  },
});

export default theme;
