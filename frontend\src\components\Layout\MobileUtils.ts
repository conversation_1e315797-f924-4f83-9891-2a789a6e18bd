import { createContext, useContext } from 'react';

interface MobileContextData {
  isMobile: boolean;
  isTablet: boolean;
}

export const MobileContext = createContext<MobileContextData>({
  isMobile: false,
  isTablet: false
});

export const useIsMobile = () => {
    const contextValue = useContext(MobileContext);
    return contextValue.isMobile;
};

export const useIsTablet = () => {
    const { isTablet } = useContext(MobileContext);
    return isTablet;
};
