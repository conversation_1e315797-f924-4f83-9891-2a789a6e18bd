.pairs {
  cursor: pointer;
}

.overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  box-shadow: 5px 5px 5px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 34, 68, 0.1);
}

.close-button {
  position: absolute;
  right: 0;
  top: -14px;
  background-color: transparent;
  outline: 0;
  margin: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  line-height: 1.75;
  text-transform: uppercase;
  border-radius: 4px;
  border: 1px solid rgb(0 34 68 / 20%);
  color: rgba(0, 34, 68, 1);
  border-radius: 4px;
  display: -webkit-box;
}

.pair {
  opacity: 1;
  transition: all 0.2s;
}

.hovered {
  opacity: 0.1;
  transition: all 0.2s;
}
