import { invertName } from "../../../../utils/text";

export const formatAuthorsMLA = (authorsRaw: string, maxLength = 80) => {
    if (!authorsRaw) return '';
    const sanitized = authorsRaw.replace(/\s*\[ORCID:[^\]]+\]/g, '');
    const authors = sanitized.split(';').map(a => a.trim()).filter(Boolean);
    if (authors.length === 0) return '';
    let result = '';
    if (authors.length === 1) {
        result = invertName(authors[0]);
    } else if (authors.length === 2) {
        result = `${invertName(authors[0])}, and ${authors[1]}`;
    } else {
        result = `${invertName(authors[0])}, et al.`;
    }
    if (result.length > maxLength) {
        let truncated = result.slice(0, maxLength - 1);
        const lastSpace = truncated.lastIndexOf(' ');
        if (lastSpace > 0) truncated = truncated.slice(0, lastSpace);
        result = truncated + '…';
    }
    return result;
};

export const generateLink = (url: string | undefined): string | undefined => {
    if (!url) return undefined;
    if (url.startsWith('http')) {
        return url;
    } else {
        return `https://doi.org/${url}`;
    }
};

export const capitalizeWords = (str: string) => {
    if (!str) return str;
    const lowerConjunctions = ['and', 'of'];
    return str.split(/\s+/).map((word, idx) => {
        if (lowerConjunctions.includes(word.toLowerCase()) && idx !== 0) {
            return word.toLowerCase();
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    }).join(' ');
};

export const getYearFromCitation = (citation: string | null | undefined): number | null => {
    if (!citation) {
        return null;
    }
    const match = citation.match(/\((\d{4})\)/);
    return match ? parseInt(match[1], 10) : null;
};