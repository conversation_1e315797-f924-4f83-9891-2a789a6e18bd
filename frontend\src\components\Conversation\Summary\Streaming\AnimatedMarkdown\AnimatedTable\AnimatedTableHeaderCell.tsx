import React, { useEffect } from 'react';
import LinkComponent from '../../LinkComponent';
import { replaceTags } from '../../Utils';
import { Source } from "../../../../../../types/ConversationTypes";

interface AnimatedTableHeaderCellProps {
  children: React.ReactNode;
  onComplete: () => void;
  messageId?: string;
  sources?: Source[];
  plotData?: any;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
}

const AnimatedTableHeaderCell: React.FC<AnimatedTableHeaderCellProps> = ({
  children,
  onComplete,
  messageId,
  sources,
  plotData,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
}) => {
  useEffect(() => {
    onComplete();
  }, [onComplete]);

  const renderChildrenWithLinks = (childNodes: React.ReactNode) => {
    return React.Children.map(childNodes, (child) => {
      if (typeof child === 'string') {
        return <span dangerouslySetInnerHTML={{ __html: replaceTags(child, sources, plotData) }} />;
      }
      if (React.isValidElement(child) && child.type === 'a') {
        return (
          <LinkComponent
            {...child.props}
            messageId={messageId || ''}
            onViewOnPlotClicked={onViewOnPlotClicked}
            onViewOnSourceClicked={onViewOnSourceClicked}
            plotData={plotData}
          />
        );
      }
      if (React.isValidElement(child) && child.props && child.props.children) {
        return React.cloneElement(child, {
          children: renderChildrenWithLinks(child.props.children)
        });
      }
      return child;
    });
  };

  return <th>{renderChildrenWithLinks(children)}</th>;
};

export default AnimatedTableHeaderCell;