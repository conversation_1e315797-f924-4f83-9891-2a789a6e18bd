.outlined-button {
  border: 2px solid #007bff; /* Blue outline by default */
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 16px;
  background-color: transparent;
  color: #007bff; /* Blue text color */
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.outlined-button:hover {
  background-color: #007bff; /* Blue background on hover */
  color: #fff; /* White text color on hover */
}
