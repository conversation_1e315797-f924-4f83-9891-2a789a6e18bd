import asyncio
import json
import uuid

from utils.filesystem import get_file_path
from utils.requests import post_json

data = None
with open(get_file_path("mock-agent-responses.json"), "r") as f:
    data = json.load(f)

queries = [item["response"]["context"]["query"] for item in data]


async def fetch_response(conversation_id: str, query: str):
    response = await post_json(
        "https://agent.impact-ai-dev.app/execute",
        {"conversation_id": conversation_id, "query": query},
        timeout=300,
    )
    return response


async def generate():
    mocks = []
    mocks = await asyncio.gather(
        *[fetch_response(str(uuid.uuid4()), query) for query in queries]
    )
    with open(get_file_path("mock-agent-responses-new.json"), "w") as f:
        f.write(json.dumps(mocks, indent=4))
    return mocks


asyncio.run(generate())
