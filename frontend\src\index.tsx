import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App';
import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';

const rootElement = document.getElementById('root');
if (!rootElement) {
    console.error('Element not found');
} else {
    const root = createRoot(rootElement);
    root.render(
        <App />
    );
}
