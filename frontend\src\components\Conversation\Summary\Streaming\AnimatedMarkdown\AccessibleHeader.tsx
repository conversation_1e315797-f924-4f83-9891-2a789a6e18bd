import React from "react";
import { Typography } from "@mui/material";

interface AccessibleHeaderProps {
    as: string;
    currentHeaderLevelRef: React.MutableRefObject<number>;
    children: React.ReactNode;
}

const AccessibleHeader: React.FC<AccessibleHeaderProps> = ({ as, currentHeaderLevelRef, children }) => {
    const getSemanticTag = () => {
        const receivedLevel = parseInt(as.toString().charAt(1), 10);
        const lastLevel = currentHeaderLevelRef.current;
        const effectiveReceivedLevel = receivedLevel <= 3 ? 3 : receivedLevel;
        if (lastLevel === 0) {
            currentHeaderLevelRef.current = 3;
            return 'h3';
        }

        if (effectiveReceivedLevel > lastLevel + 1) {
            const correctedLevel = `h${lastLevel + 1}`;
            currentHeaderLevelRef.current = lastLevel + 1;
            return correctedLevel;
        }

        currentHeaderLevelRef.current = effectiveReceivedLevel;
        return `h${effectiveReceivedLevel}`;
    };

    const HeaderTag = getSemanticTag();

    const fontSize = HeaderTag === 'h3' ? "20px" : (HeaderTag === 'h4' ? "18px" : "16px");
    const fontWeight = HeaderTag === 'h3' ? 700 : 600;
    const lineHeight = HeaderTag === 'h3' ? "28px" : (HeaderTag === 'h4' ? "24px" : "22px");

    return (
        <Typography
            variant={HeaderTag as 'h1' | 'h2' | 'h3' | 'h4' | 'h5'}
            component={HeaderTag}
            sx={{
                textAlign: "left",
                fontSize: fontSize,
                fontWeight: fontWeight,
                lineHeight: lineHeight,
                mt: "1em",
                mb: "1em"
            }}
        >
            {children}
        </Typography>
    );
};

export default AccessibleHeader;