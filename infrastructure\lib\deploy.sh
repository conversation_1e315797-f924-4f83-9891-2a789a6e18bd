#!/bin/bash

# Source common functions if not already loaded
if ! declare -f require_env_var >/dev/null 2>&1; then
    source "$(dirname "${BASH_SOURCE[0]}")/common.sh"
fi

# Default configuration
readonly DEFAULT_PROJECT_ID="impactai-430615"
readonly DEFAULT_REGION="us-east1"

# Function to validate deployment environment variables
validate_deployment_environment() {
    log_info "Validating deployment environment..."
    
    require_env_var TARGET "TARGET" || exit 1
    require_env_var SERVICE "SERVICE" || exit 1
    
    validate_enum "$TARGET" "TARGET" "experimental" "development" "testing" "production" || exit 1
    
    log_success "Deployment environment validated"
}

# Function to get database instance name based on target
get_database_instance() {
    local target="$1"
    
    if [[ "$target" == "production" || "$target" == "testing" ]]; then
        echo "impactai-db-production"
    else
        echo "impactai-db"
    fi
}

# Function to build and push container image
build_and_push_image() {
    local service_dir="$1"
    local image_tag="$2"
    local dry_run="${3:-false}"
    
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would build and push image: $image_tag"
        log_info "[DRY RUN] Source directory: $service_dir"
        log_success "[DRY RUN] Image build validation completed"
        return 0
    fi
    
    log_info "Building and pushing container image..."
    log_info "Image tag: $image_tag"
    log_info "Source directory: $service_dir"
    
    if gcloud builds submit "$service_dir" --tag "$image_tag" --quiet; then
        log_success "Image built and pushed successfully"
        return 0
    else
        log_error "Image build failed"
        return 1
    fi
}

# Function to deploy to Cloud Run with standard configuration
deploy_cloud_run_service() {
    local service_name="$1"
    local image_tag="$2"
    local config_file="$3"
    local dry_run="${4:-false}"
    
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would deploy Cloud Run service: $service_name"
        log_info "[DRY RUN] Image: $image_tag"
        log_info "[DRY RUN] Config file: $config_file"
        log_success "[DRY RUN] Deployment validation completed"
        return 0
    fi
    
    log_info "Deploying Cloud Run service: $service_name"
    
    local deploy_cmd="gcloud run deploy $service_name \
        --image $image_tag \
        --region $DEFAULT_REGION \
        --platform managed \
        --allow-unauthenticated \
        --quiet"
    
    # Add environment variables if config file exists
    if [[ -f "$config_file" ]]; then
        deploy_cmd="$deploy_cmd --env-vars-file=$config_file"
    fi
    
    if eval "$deploy_cmd"; then
        log_success "Service deployed successfully"
        return 0
    else
        log_error "Service deployment failed"
        return 1
    fi
}

# Function to deploy backend service with database connection
deploy_backend_service() {
    local service_name="$1"
    local image_tag="$2"
    local config_file="$3"
    local target="$4"
    local dry_run="${5:-false}"
    
    local database_instance
    database_instance=$(get_database_instance "$target")
    local cloud_sql_instance="$DEFAULT_PROJECT_ID:$DEFAULT_REGION:$database_instance"
    local vpc_connector="projects/$DEFAULT_PROJECT_ID/locations/$DEFAULT_REGION/connectors/impact-ai-vpc-connector"
    
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would deploy backend service: $service_name"
        log_info "[DRY RUN] Database instance: $database_instance"
        log_info "[DRY RUN] Cloud SQL instance: $cloud_sql_instance"
        log_success "[DRY RUN] Backend deployment validation completed"
        return 0
    fi
    
    log_info "Deploying backend service: $service_name"
    log_info "Database instance: $database_instance"
    
    local deploy_cmd="gcloud run deploy $service_name \
        --image $image_tag \
        --region $DEFAULT_REGION \
        --platform managed \
        --port=8000 \
        --min-instances=1 \
        --cpu-boost \
        --memory=8Gi \
        --cpu=4 \
        --concurrency=10 \
        --timeout=300 \
        --allow-unauthenticated \
        --vpc-connector $vpc_connector \
        --vpc-egress private-ranges-only \
        --set-cloudsql-instances=$cloud_sql_instance \
        --project $DEFAULT_PROJECT_ID \
        --quiet"
    
    # Add environment variables if config file exists
    if [[ -f "$config_file" ]]; then
        deploy_cmd="$deploy_cmd --env-vars-file=$config_file"
    fi
    
    if eval "$deploy_cmd"; then
        log_success "Backend service deployed successfully"
        return 0
    else
        log_error "Backend service deployment failed"
        return 1
    fi
}

# Function to deploy frontend service
deploy_frontend_service() {
    local service_name="$1"
    local image_tag="$2"
    local target="$3"
    local dry_run="${4:-false}"
    
    if [ "$dry_run" = true ]; then
        log_info "[DRY RUN] Would deploy frontend service: $service_name"
        log_info "[DRY RUN] Target environment: $target"
        log_success "[DRY RUN] Frontend deployment validation completed"
        return 0
    fi
    
    log_info "Deploying frontend service: $service_name"
    
    if gcloud run deploy "$service_name" \
        --image "$image_tag" \
        --region "$DEFAULT_REGION" \
        --platform managed \
        --allow-unauthenticated \
        --min-instances 1 \
        --cpu-boost \
        --memory 2048Mi \
        --cpu 2 \
        --port 80 \
        --set-env-vars "TARGET=$target" \
        --set-env-vars "NODE_ENV=production" \
        --quiet; then
        log_success "Frontend service deployed successfully"
        return 0
    else
        log_error "Frontend service deployment failed"
        return 1
    fi
}

# Function to create git-based image tag (branch-commit format)
create_image_tag() {
    local service_name="$1"
    
    # Get current git branch name, fallback to 'unknown' if not available
    local branch_name=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    # Get short commit hash, fallback to timestamp if not available
    local commit_hash=$(git rev-parse --short HEAD 2>/dev/null || date +%Y%m%d%H%M%S)
    
    # Clean branch name (replace slashes and other special chars with dashes)
    local clean_branch_name=$(echo "$branch_name" | sed 's/[^a-zA-Z0-9._-]/-/g')
    
    echo "gcr.io/$DEFAULT_PROJECT_ID/$service_name:$clean_branch_name-$commit_hash"
}