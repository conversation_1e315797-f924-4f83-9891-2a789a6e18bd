import { Source, PlotData } from "../../../../types/ConversationTypes";

type SummaryText = string | string[] | object;

export const formatSummaryText = (summaryText: SummaryText) => {
    if (typeof summaryText === "string") {
        return summaryText;
    } else if (Array.isArray(summaryText)) {
        return summaryText.join(",");
    } else if (typeof summaryText === "object") {
        return JSON.stringify(summaryText, null, 2);
    }
    return "";
};

/**
 * Processes text to replace tags with special Markdown links.
 */
export const replaceTags = (text: string, sources: Source[], plotData: PlotData[]) => {
    // Source tag processing, e.g., [C584] or [O507, S869]
    const regexForSources = /\[\s*([A-Za-z]+\d+(?:\s*,\s*[A-Za-z]+\d+)*)\s*\]/g;
    text = text.replace(regexForSources, (_, shortPaperIdGroup) => {
        const shortPaperIds = shortPaperIdGroup.split(',').map(x => x.trim());
        const combinedQueryParams = new URLSearchParams();
        const uniqueProcessedPaperIds = new Set<string>();
        shortPaperIds.forEach(shortPaper => {
            if (!uniqueProcessedPaperIds.has(shortPaper)) {
                combinedQueryParams.append('paper_id', shortPaper);
                uniqueProcessedPaperIds.add(shortPaper);
            }
        });
        if (combinedQueryParams.toString()) {
            return `[](?${combinedQueryParams.toString()})`;
        } else {
            return '';
        }
    });

    // Plot tag processing, e.g., [intervention=123, outcome=456] (order doesn't matter, spaces allowed)
    const finalRegexForPlots = /\[\s*(intervention\s*=\s*\d+\s*,\s*outcome\s*=\s*\d+|outcome\s*=\s*\d+\s*,\s*intervention\s*=\s*\d+)\s*\]/g;

    text = text.replace(finalRegexForPlots, (_, plotParamsGroup) => {
        const params = plotParamsGroup.split(',').map(x => x.trim());
        const plotQueryParams = new URLSearchParams();

        params.forEach(param => {
            const [key, value] = param.split('=').map(s => s.trim());
            if (key && value) {
                plotQueryParams.append(key, value);
            }
        });

        const result = plotQueryParams.toString();
        return result ? `[](?${result})` : '';
    });

    return text;
};

export const checkForIntroduction = (text: string) => {
    return /### Introduction/.test(text);
};

export const capitalizeFirstLetter = (text: string) => {
    return text ? text.charAt(0).toUpperCase() + text.slice(1) : text;
};

export const NormalizeSpaces = (text) =>
    text.replace(/[^\S\r\n]+/g, ' ');
