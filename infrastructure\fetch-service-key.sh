#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Fetch service account key from Google Cloud Secret Manager"
            echo "Description:"
            echo "  This script fetches the compute service account key from Secret Manager"
            echo "  and saves it to agent/.secrets/service-account-key.json for local development."
            echo ""
            echo "Notes:"
            echo "  - Creates agent/.secrets directory if it doesn't exist"
            echo "  - Overwrites existing service account key file"
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly AGENT_DATA_DIR="agent/.secrets"
readonly SERVICE_ACCOUNT_FILE="$AGENT_DATA_DIR/service-account-key.json"
readonly SECRET_NAME="impactai-compute-service-account"

# Function to create secrets directory
create_secrets_directory() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would create directory: $AGENT_DATA_DIR"
        log_success "[DRY RUN] Directory creation validation completed"
        return 0
    fi
    
    log_info "Creating secrets directory..."
    
    if mkdir -p "$AGENT_DATA_DIR"; then
        log_success "Secrets directory created: $AGENT_DATA_DIR"
        return 0
    else
        log_error "Failed to create secrets directory: $AGENT_DATA_DIR"
        return 1
    fi
}

# Function to fetch service account key
fetch_service_account_key() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would fetch secret: $SECRET_NAME"
        log_info "[DRY RUN] Would save to: $SERVICE_ACCOUNT_FILE"
        log_success "[DRY RUN] Service account key fetch validation completed"
        return 0
    fi
    
    log_info "Fetching service account key from Secret Manager..."
    log_info "Secret name: $SECRET_NAME"
    log_info "Output file: $SERVICE_ACCOUNT_FILE"
    
    if gcloud secrets versions access latest --secret="$SECRET_NAME" > "$SERVICE_ACCOUNT_FILE"; then
        log_success "Service account key fetched successfully"
        return 0
    else
        log_error "Failed to fetch service account key"
        log_info "Please verify that:"
        log_info "  - The secret '$SECRET_NAME' exists in Google Cloud Secret Manager"
        log_info "  - You have the necessary permissions to access it"
        return 1
    fi
}

# Function to validate service account key file
validate_service_account_key() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would validate service account key file"
        log_success "[DRY RUN] Service account key validation completed"
        return 0
    fi
    
    if [[ ! -f "$SERVICE_ACCOUNT_FILE" ]]; then
        log_error "Service account key file was not created: $SERVICE_ACCOUNT_FILE"
        return 1
    fi
    
    local file_size
    file_size=$(wc -c < "$SERVICE_ACCOUNT_FILE")
    
    if [ "$file_size" -eq 0 ]; then
        log_error "Service account key file is empty: $SERVICE_ACCOUNT_FILE"
        return 1
    fi
    
    # Basic JSON validation
    if ! python3 -m json.tool "$SERVICE_ACCOUNT_FILE" >/dev/null 2>&1; then
        log_error "Service account key file is not valid JSON: $SERVICE_ACCOUNT_FILE"
        return 1
    fi
    
    log_success "Service account key file validated: $file_size bytes"
    return 0
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        log_info "Starting service account key fetch validation (DRY RUN MODE)"
    else
        log_info "Starting service account key fetch from Google Cloud Secret Manager"
    fi
    
    # Pre-flight checks
    check_gcloud_auth
    
    # Create secrets directory
    if ! create_secrets_directory; then
        exit 1
    fi
    
    # Fetch service account key
    if ! fetch_service_account_key; then
        exit 1
    fi
    
    # Validate the fetched key
    if ! validate_service_account_key; then
        exit 1
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_success "Service account key fetch validation completed successfully!"
    else
        log_success "Service account key fetch completed successfully!"
        log_info "Key saved to: $SERVICE_ACCOUNT_FILE"
    fi
}

# Run main function
main "$@"
