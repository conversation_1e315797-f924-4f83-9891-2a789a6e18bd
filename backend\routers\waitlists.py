import structlog
from pydantic import BaseModel
from fastapi import APIRouter

from services.waitlist import add_to_waitlist

router = APIRouter()
logger = structlog.get_logger()


class WaitlistBody(BaseModel):
    email: str
    organization: str


@router.post("/waitlist")
async def post_waitlist(body: WaitlistBody):
    """Stores waitlist users."""
    try:

        await add_to_waitlist(
            email=body.email,
            organization=body.organization,
        )

        return {"success": True}
    except Exception as e:
        logger.error(
            "Failed to store waitlist.",
            error=str(e),
        )
        return {"success": False}
