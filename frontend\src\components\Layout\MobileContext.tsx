import { useTheme, useMediaQuery } from '@mui/material';
import { MobileContext } from './MobileUtils';

export const MobileProvider = ({ children }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

    return (
        <MobileContext.Provider value={{ isMobile, isTablet }}>
            {children}
        </MobileContext.Provider>
    );
};
