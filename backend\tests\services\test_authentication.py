import pytest
from unittest.mock import AsyncMock, Mock, patch
import jwt
import bcrypt
from fastapi import status
from services.authentication import (
    TokenData,
    AuthenticationError,
    jwt_encode,
    jwt_decode,
    fetch_user_by_email,
    create_user,
    fetch_user_by_id,
    authenticate,
    secret,
    algorithm,
)
from database.models import User


@pytest.fixture
def sample_user():
    return User(
        id="123",
        email="<EMAIL>",
        password_hash=bcrypt.hashpw(
            "password123".encode("utf-8"), bcrypt.gensalt()
        ).decode("utf-8"),
    )


@pytest.fixture
def mock_session():
    session = AsyncMock()
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock()
    session.add = Mock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    return session


def test_token_data_serialization():
    token_data = TokenData(user_id="123")
    serialized = token_data.to_json()
    deserialized = TokenData.from_json(serialized)
    assert deserialized.user_id == "123"


def test_jwt_encode_decode():
    data = {"user_id": "123"}
    token = jwt_encode(data)
    decoded = jwt_decode(token)
    assert decoded["user_id"] == "123"


def test_jwt_decode_expired():
    # Create an expired token
    token = jwt.encode({"exp": 0, "user_id": "123"}, secret, algorithm=algorithm)

    with pytest.raises(AuthenticationError) as exc_info:
        jwt_decode(token)
    assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert "Token has expired" in exc_info.value.detail


def test_jwt_decode_invalid():
    with pytest.raises(AuthenticationError) as exc_info:
        jwt_decode("invalid.token.string")
    assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
    assert "Invalid token" in exc_info.value.detail


@pytest.mark.asyncio
async def test_fetch_user_by_email(mock_session, sample_user):
    # Mock the database query result
    mock_result = Mock()
    mock_result.scalars.return_value.first.return_value = sample_user
    mock_session.execute.return_value = mock_result

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        user = await fetch_user_by_email("<EMAIL>")

        assert user == sample_user
        mock_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_create_user_new(mock_session):
    # Mock no existing user
    mock_result = Mock()
    mock_result.scalars.return_value.first.return_value = None
    mock_session.execute = AsyncMock(return_value=mock_result)

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        email = "<EMAIL>"
        password = "password123"

        user = await create_user(email, password)

        assert user.email == email
        assert bcrypt.checkpw(
            password.encode("utf-8"), user.password_hash.encode("utf-8")
        )
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_create_user_existing(mock_session, sample_user):
    mock_result = Mock()
    mock_result.scalars.return_value.first.return_value = sample_user
    mock_session.execute = AsyncMock(return_value=mock_result)

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        try:
            await create_user("<EMAIL>", "password123")
        except AuthenticationError as e:
            assert e.status_code == status.HTTP_401_UNAUTHORIZED
            assert "User already exists" in e.detail


@pytest.mark.asyncio
async def test_fetch_user_by_id(mock_session, sample_user):
    mock_result = Mock()
    mock_result.scalars.return_value.first.return_value = sample_user
    mock_session.execute = AsyncMock(return_value=mock_result)

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        user = await fetch_user_by_id("123")

        assert user == sample_user
        mock_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_authenticate_success(mock_session, sample_user):
    # Mock existing user
    mock_result = Mock()

    mock_result.scalars.return_value.first.return_value = sample_user
    mock_session.execute = AsyncMock(return_value=mock_result)

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        user = await authenticate("<EMAIL>", "password123")
        assert user == sample_user


@pytest.mark.asyncio
async def test_authenticate_user_not_found(mock_session):
    # Mock no user found
    mock_result = AsyncMock()
    mock_result.scalars.return_value.first.return_value = None
    mock_session.execute.return_value = mock_result

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        with pytest.raises(AuthenticationError) as exc_info:
            await authenticate("<EMAIL>", "password123")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid credentials" in exc_info.value.detail


@pytest.mark.asyncio
async def test_authenticate_wrong_password(mock_session, sample_user):
    # Mock existing user
    mock_result = Mock()
    mock_result.scalars.return_value.first.return_value = sample_user
    mock_session.execute = AsyncMock(return_value=mock_result)

    with patch(
        "services.authentication.get_userdata_db_session", return_value=mock_session
    ):
        with pytest.raises(AuthenticationError) as exc_info:
            await authenticate("<EMAIL>", "wrongpassword")

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid password" in exc_info.value.detail
