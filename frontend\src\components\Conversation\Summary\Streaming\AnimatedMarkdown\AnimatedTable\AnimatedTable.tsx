import React, { useEffect, useCallback, useState, useRef } from 'react';
import LinkComponent from '../../LinkComponent';

import AnimatedTableHeaderCell from './AnimatedTableHeaderCell';
import AnimatedTableCell from './AnimatedTableCell';

function getActualComponentType(type: React.ElementType): React.ElementType {
  if (typeof type === 'function') {
    if ((type as any)._payload && (type as any)._payload.value) {
      return getActualComponentType((type as any)._payload.value);
    }
    if ((type as any).type) {
      return getActualComponentType((type as any).type);
    }
    if ((type as any).WrappedComponent) {
      return getActualComponentType((type as any).WrappedComponent);
    }
  }
  return type;
}


interface AnimatedTableProps {
  children: React.ReactNode;
  onComplete: () => void;
  messageId?: string;
  plotData?: any;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
}

const AnimatedTable: React.FC<AnimatedTableProps> = ({
  children,
  onComplete,
  messageId,
  plotData,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
}) => {
  const [completedCells, setCompletedCells] = useState(0);
  const totalCellsRef = useRef(0);

  const countCells = useCallback((nodes: React.ReactNode): number => {
    let count = 0;
    React.Children.forEach(nodes, (node) => {
      if (React.isValidElement(node)) {
        const actualType = getActualComponentType(node.type);

        if (actualType === 'th' || actualType === 'td' || actualType === AnimatedTableHeaderCell || actualType === AnimatedTableCell) {
          count++;
        }
        if (node.props && node.props.children) {
          count += countCells(node.props.children);
        }
      }
    });
    return count;
  }, []);

  useEffect(() => {
    totalCellsRef.current = countCells(children);
    setCompletedCells(0);
  }, [children, countCells]);

  const handleCellRendered = useCallback(() => {
    setCompletedCells(prev => {
      return prev + 1;
    });
  }, []);

  useEffect(() => {
    if (totalCellsRef.current > 0 && completedCells === totalCellsRef.current) {
      onComplete();
    }
  }, [completedCells, totalCellsRef, onComplete]);

  const renderCellContent = useCallback((cellChildren: React.ReactNode) => {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        {React.Children.map(cellChildren, (child, idx) => {
          if (React.isValidElement(child)) {
            const { type, props } = child;

            if (type === 'a') {
              return (
                <LinkComponent
                  key={child.key || idx}
                  {...props}
                  messageId={messageId || ''}
                  onViewOnPlotClicked={onViewOnPlotClicked}
                  onViewOnSourceClicked={onViewOnSourceClicked}
                  plotData={plotData}
                >
                  {React.Children.count(props.children) === 0 ? '[Link]' : renderCellContent(props.children)}
                </LinkComponent>
              );
            } else if (type === 'em' || type === 'strong' || (typeof type === 'function' && (type.displayName === 'AnimatedEm' || type.displayName === 'AnimatedStrong' || type.name === 'AnimatedEm' || type.name === 'AnimatedStrong'))) {
              return React.cloneElement(child, {
                key: child.key || idx,
                children: renderCellContent(props.children)
              });
            }
            if (props?.children) {
              return React.cloneElement(child, {
                key: child.key || idx,
                children: renderCellContent(props.children)
              });
            }
            return React.cloneElement(child, { key: child.key || idx });

          } else if (typeof child === 'string') {
            return child;
          }
          return child;
        })}
      </div>
    );
  }, [messageId, onViewOnPlotClicked, onViewOnSourceClicked, plotData]);


  const renderTableStructure = useCallback((nodes: React.ReactNode): React.ReactNode => {
    return React.Children.map(nodes, (node, index) => {
      if (!React.isValidElement(node)) {
        return node;
      }

      const { type, props } = node;
      const actualComponentType = getActualComponentType(type);

      if (typeof actualComponentType === 'string') {
        switch (actualComponentType) {
          case 'thead':
          case 'tbody':
          case 'tr':
            const HtmlTag = actualComponentType as keyof JSX.IntrinsicElements;
            return (
              <HtmlTag key={node.key || index}>
                {renderTableStructure(props.children)}
              </HtmlTag>
            );
          case 'th':
          case 'td':
            useEffect(() => {
              handleCellRendered();
            }, []);
            const CellHtmlTag = actualComponentType as keyof JSX.IntrinsicElements;
            return (
              <CellHtmlTag key={node.key || index}>
                {renderCellContent(props.children)}
              </CellHtmlTag>
            );
          default:
            return node;
        }
      } else if (actualComponentType === AnimatedTableHeaderCell || actualComponentType === AnimatedTableCell) {
        useEffect(() => {
          handleCellRendered();
        }, []);
        return React.cloneElement(node, {
          key: node.key || index,
          children: renderCellContent(props.children),
        });
      } else {
        return node;
      }
    });
  }, [renderCellContent, handleCellRendered]);

  return (
    <table>
      {renderTableStructure(children)}
    </table>
  );
};

export default AnimatedTable;