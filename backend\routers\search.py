import structlog
from pydantic import BaseModel
from fastapi import APIRouter, Depends
from services.search import SearchService
from models.search import EntryType

router = APIRouter()
logger = structlog.get_logger()


search_service = SearchService()


@router.get("/search/chips")
async def get_search_chips():
    """Performs chip search."""
    try:
        results = await search_service.get_chips()
        return {"success": True, "data": {"entries": results}}
    except ValueError as e:
        logger.error("Failed to fetch entries.", error=e)
        return {"success": False, "error": "Failed to fetch entries."}
