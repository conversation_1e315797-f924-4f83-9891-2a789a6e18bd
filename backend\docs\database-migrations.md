# Database Migration Quick Reference

## Make Commands

| Command | Description | Example |
|---------|-------------|---------|
| `make db-current` | Show current migration state | `make db-current` |
| `make db-history` | Show all migration history | `make db-history` |
| `make db-create` | Create new migration (interactive) | `make db-create` |
| `make db-migrate` | Apply all pending migrations | `make db-migrate` |
| `make db-downgrade` | Rollback one migration | `make db-downgrade` |
| `make db-downgrade-to REV=id` | Rollback to specific revision | `make db-downgrade-to REV=abc123` |
| `make db-reset` | Reset database (interactive) | `make db-reset` |

## Direct Alembic Commands

For advanced usage, you can also use Alembic directly:

```bash
# Check current revision
poetry run alembic current

# Create migration manually
poetry run alembic revision --autogenerate -m "Add user profile"

# Upgrade to latest
poetry run alembic upgrade head

# Downgrade by 1 step
poetry run alembic downgrade -1

# Downgrade to specific revision
poetry run alembic downgrade abc123

# Show history
poetry run alembic history --verbose

# Show SQL without running
poetry run alembic upgrade head --sql

# Mark current state as migrated (without running)
poetry run alembic stamp head
```

## Workflow Examples

### Adding a New Field to User Model

1. Edit `database/models.py`:
   ```python
   class User(TimestampMixin, Base):
       # ... existing fields ...
       profile_picture = Column(String(255), nullable=True)
   ```

2. Create migration:
   ```bash
   make db-create
   # Enter: "Add profile picture to user model"
   ```

3. Review generated migration in `alembic/versions/`

4. Apply migration:
   ```bash
   make db-migrate
   ```

### Rolling Back Changes

```bash
# Check what you're rolling back
make db-history

# Rollback one step
make db-downgrade

# Or rollback to specific point
make db-downgrade-to REV=previous_revision_id

# Verify state
make db-current
```

### Emergency Database Reset

⚠️ **WARNING: This will destroy all data!**

```bash
make db-reset
# Confirm when prompted
```

## Environment Setup

Ensure these environment variables are set:

```bash
export MYSQL_HOST=localhost
export MYSQL_DATABASE=your_db_name
export MYSQL_USER=your_username
export MYSQL_PASSWORD=your_password
```

## Files and Directories

- `alembic/` - Alembic configuration and migration files
- `alembic/versions/` - Individual migration files
- `alembic/env.py` - Alembic environment configuration
- `alembic.ini` - Alembic settings
- `database/models.py` - SQLAlchemy model definitions
- `database/userdata.py` - Database connection setup

## Troubleshooting

### Common Issues

**"Target database is not up to date"**
```bash
make db-current  # Check current state
make db-migrate  # Apply pending migrations
```

**"Revision not found"**
```bash
make db-history  # Find correct revision ID
```

**"Cannot drop index: needed in foreign key constraint"**
- This happens when Alembic tries to drop indexes required by foreign key constraints
- **Solution**: Delete the problematic migration and create an empty sync migration
- **Steps**:
  ```bash
  # 1. Delete the problematic migration file from alembic/versions/
  rm alembic/versions/problematic_migration_id.py
  
  # 2. Create an empty sync migration
  poetry run alembic revision -m "Sync models with database"
  
  # 3. Apply the empty migration
  make db-migrate
  ```
- For development databases, consider using `make db-reset` as a last resort

### Getting Help

```bash
# Show all available commands
make help

# Alembic help
poetry run alembic --help
``` 