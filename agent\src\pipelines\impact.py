import logging
from typing import Dict, Any, List, Tuple

from src.pipelines.inscope import InScopePipeline
from src.tools.manager import ToolManager

logger = logging.getLogger(__name__)


class ImpactPipeline(InScopePipeline):
    """Pipeline for causal impact queries exploring whether and how X affects Y."""

    def __init__(
        self,
        tool_manager: ToolManager,
        config: Dict[str, Any] = None,
        name: str = "impact_pipeline",
        description: str = "Pipeline for causal impact queries exploring treatment effects and statistically significant changes due to interventions",
        intent: str = "causal impact",
        steps: List[str] = None,
        arguments: List[Tuple[str, str]] = None,
        outputs: str = "FinalAnswer(text: str, sources: List[str], metadata: Dict[str, Any])",
    ):
        """Initialize the implementation pipeline."""
        super().__init__(
            tool_manager=tool_manager,
            config=config,
            name=name,
            description=description,
            intent=intent,
            steps=steps,
            arguments=arguments,
            outputs=outputs,
        )

    def get_steps(self) -> List[str]:
        """Get the steps for impact analysis."""
        return self.steps

    async def _execute_step(self, step_name: str, **kwargs):
        """Execute a pipeline step with error handling and tracking."""
        try:
            self.current_step += 1
            if self.verbose:
                logger.info(f"Step {self.current_step}: Executing {step_name}")

            # Start tracking the step
            self._track_step_start(step_name, kwargs)

            result = await self.tool_manager.execute_with_cache(step_name, **kwargs)
            self.results[step_name] = result

            # Track successful completion
            output_summary = {"type": type(result).__name__}
            if hasattr(result, "row_count"):
                output_summary["row_count"] = result.row_count
            if hasattr(result, "text"):
                output_summary["text_length"] = len(result.text)

            self._track_step_end(success=True, output_data=output_summary)

            return result

        except Exception as e:
            if self.verbose:
                logger.error(f"Error in {step_name}: {e}")

            # Track the error
            self._track_step_end(success=False, error_message=str(e))
            raise
