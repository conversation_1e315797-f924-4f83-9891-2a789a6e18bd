import React, { useState, useEffect } from "react";
import * as d3 from "d3";
import {
  Box,
  Typography,
  useTheme,
  Slider,
  Select,
  MenuItem,
  ToggleButtonGroup,
  Chip,
  Stack,
  OutlinedInput,
  ListItemText,
  Checkbox,
} from "@mui/material";

interface MatrixProps {
  plotData: any;
  effectSizes: any; // This should be the processed data for effect sizes
  matrixData: any; // This should be the processed data for the matrix
  studiesData: any;
  selectedOutcome: string;
  interventionsByOutcome: any[];
  selectedIntervention: string;
  hoveredPair: { intervention: string; outcome: string } | undefined;
  hoveredIntervention: { intervention: string } | undefined;
  hoveredOutcome: { outcome: string } | undefined;
  onComparisonOutcomeClicked: (outcome: string) => void;
  onComparisonInterventionClicked: (intervention: string) => void;
  onPairClicked: (pair: { intervention: string; outcome: string }) => void;
}

const Matrix: React.FC<MatrixProps> = ({
  plotData,
  effectSizes,
  matrixData,
  studiesData,
  selectedOutcome,
  interventionsByOutcome,
  selectedIntervention,
  hoveredPair,
  onComparisonOutcomeClicked,
  onComparisonInterventionClicked,
}: MatrixProps) => {
  const theme = useTheme();

  



  function uniqueByProperty<T>(array: T[], accessor: (item: T) => string) {
    const seen: { [key: string]: boolean } = {};
    return array.filter((item) => {
      const key = accessor(item);
      if (seen[key]) {
        return false;
      }
      seen[key] = true;
      return true;
    });
  }

  let outcomeKeys = [];
  let interventionKeys = [];
  let sectors = {};

  
  let [activeOutcomes, setActiveOutcomes] = useState([]);
  let [activeInterventions, setActiveInterventions] = useState([]);
  let [activeSectors, setActiveSectors] = useState([]);
  let [hovered, setHovered] = useState(null);

  // const outcomeHierarchy = plotData?.flat_effect_sizes?.reduce(
  const outcomeHierarchy = effectSizes?.reduce(
    (acc: any, item: any) => {
      const outcomeTagStrings = [item.outcome_tag_short_labels];
      const interventionTagStrings = Array.isArray(item.intervention_tag_short_labels) ? item.intervention_tag_short_labels : [item.intervention_tag_short_labels];

      outcomeTagStrings.forEach((outcomeKey: string, i: number) => {
        interventionTagStrings.forEach((interventionKey: string, j: number) => {
          outcomeKeys.push(outcomeKey);
          interventionKeys.push(interventionKey);

          const key = `${outcomeKey}|${interventionKey}`;

          if (!acc[key]) {
            acc[key] = {
              outcomeKey: outcomeKey,
              outcomeTag: item.outcome_tags_with_levels[i],
              outcomeTags: item.outcome_tags_with_levels,
              outcomeLabel: item.outcome_tag_short_labels,
              interventionKey: interventionKey,
              interventionTag: item.intervention_tags_with_levels[j],
              interventionTags: item.intervention_tags_with_levels,
              interventionLabel: item.intervention_tag_short_labels,
              effectSizes: [],
              parentOutcome: i > 0 ? outcomeTagStrings[i - 1] : null,
              parentIntervention: j > 0 ? interventionTagStrings[j - 1] : null,
              outcomeHasChildren: i < item.outcome_tags_with_levels.length - 1,
              interventionHasChildren:
                j < item.intervention_tags_with_levels.length - 1,
              outcomeSectors: item.outcome_sectors,
              interventionSectors: item.intervention_sectors,
              allSectors: item.all_sectors,
            };
          }

          acc[key].effectSizes.push(item);
        });
      });

      return acc;
    },
    {}
  );
console.log("OUTCOME HIERARCHY", outcomeHierarchy);
  outcomeKeys = Array.from(new Set(outcomeKeys)).sort();
  interventionKeys = Array.from(new Set(interventionKeys)).sort();

  const data = Object.entries(outcomeHierarchy).map(([_, value]) => ({
    ...value,
    meanEffectSize: d3.mean(value.effectSizes, (d) => d.cohen_d),
    effectSizesCount: value.effectSizes.length,
  }));
  console.log(
    data.sort(
      (a, b) =>
        a.outcomeKey.localeCompare(b.outcomeKey) ||
        a.interventionKey.localeCompare(b.interventionKey)
    ),
    outcomeKeys,
    interventionKeys,
    activeOutcomes,
    activeInterventions
  );

  const cScale = d3
    .scaleLinear()
    .domain([-0.6, -0.4, -0.2, 0, 0.2, 0.4, 0.6])
    // .domain([-0.8, -0.5, -0.2, 0, 0.2, 0.5, 0.8])

    .range([
      "#8aaf5f",
      "#afc183",
      "#c7d6ae",
      "#ffffff",
      "#a7c6e1",
      "#7aabd2",
      "#4e8fc0",
    ])
    .clamp(true);

  function onOutcomeClicked(outcomeKey: string) {
    if (activeOutcomes.includes(outcomeKey)) {
      // setActiveOutcomes(activeOutcomes.filter((d) => d !== outcomeKey));
      setActiveOutcomes(
        activeOutcomes.filter((d) => !d.startsWith(outcomeKey))
      );
    } else {
      setActiveOutcomes([...activeOutcomes, outcomeKey]);
    }
  }

  function onInterventionClicked(interventionKey: string) {
    if (activeInterventions.includes(interventionKey)) {
      setActiveInterventions(
        activeInterventions.filter((d) => !d.startsWith(interventionKey))
      );
    } else {
      setActiveInterventions([...activeInterventions, interventionKey]);
    }
  }

  console.log(
    "INTERVENTIONS SORTED",
    data
      .filter(
        (d) =>
          (d.parentOutcome === null ||
            activeOutcomes.includes(d.parentOutcome)) &&
          (d.parentIntervention === null ||
            activeInterventions.includes(d.parentIntervention))
      )
      .sort(
        (a, b) =>
          a.interventionKey.localeCompare(b.interventionKey) ||
          a.outcomeKey.localeCompare(b.outcomeKey)
      )
  );
  const filteredData = data
    .filter(
      (d) =>
        activeSectors.length === 0 ||
        (d.allSectors && d.allSectors.includes(activeSectors[0]))
    )
    .sort(
      (a, b) =>
        a.interventionKey.localeCompare(b.interventionKey) ||
        a.outcomeKey.localeCompare(b.outcomeKey)
  );
  
  effectSizes.forEach((item: any) => {
    item.all_sectors?.forEach((sector: string) => {
      if (!sectors[sector]) {
        sectors[sector] = 0;
      }
      sectors[sector]++;
    });
  });
  console.log("SECTORS", sectors);

  const uniqueInterventions = Array.from(
    new Set(
      filteredData.reduce((acc, cur) => {
        if (!acc.some((d) => d.interventionKey === cur.interventionKey)) {
          acc.push(cur);
        }
        return acc;
      }, [])
    )
  );
  const uniqueOutcomes = Array.from(
    new Set(
      filteredData.reduce((acc, cur) => {
        if (!acc.some((d) => d.outcomeKey === cur.outcomeKey)) {
          acc.push(cur);
        }
        return acc;
      }, [])
    )
  );

  console.log("UNIQUE INTERVENTIONS", uniqueInterventions);
  console.log("UNIQUE OUTCOMES", uniqueOutcomes);
  console.log(
    "GROUPED DATA",
    d3.groups(
      filteredData,
      (d) => d.outcomeKey,
      (d) => d.interventionKey
    )
  );
  console.log("FILTERED DATA", filteredData);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        borderRadius: "8px",
        border: `none`,
        backgroundColor: `${theme.palette.background.default}`,
        overflow: "hidden",
        width: "100%",
        fontSize: "12px",
        maxWidth: "100%",
        [theme.breakpoints.down("sm")]: {
          padding: "8px",
        },
      }}
    >
      <div>
        <div className="sectors">
          {/* <h4>Sectors</h4> */}
          {Object.entries(sectors).map(([key, value]) => (
            <div key={key} style={{ margin: "4px" }}>
              <Chip
                label={key}
                // variant={
                //   filteredData.filter((d) => {
                //     return d.allSectors && d.allSectors.includes(key);
                //   }).length === 0
                //     ? "disabled"
                //     : "outlined"
                // }
                color={activeSectors.includes(key) ? "success" : "default"}
                onClick={() => {
                  if (activeSectors.includes(key)) {
                    setActiveSectors([]);
                  } else {
                    setActiveSectors([key]);
                  }
                }}
              />
            </div>
          ))}
        </div>
        <div>
          <div className="row" style={{ display: "flex" }}>
            <div style={{ width: 200 }}></div>
            {uniqueInterventions.map((item) => (
              <div
                key={item.interventionKey}
                style={{
                  width: 70,
                  // overflow: "hidden",
                  height: 50,
                }}
              >
                <div style={{whiteSpace: 'nowrap'}}>{item.interventionLabel}</div>
              </div>
            ))}
          </div>
          <div>
            {uniqueOutcomes.sort((a, b) => b.effectSizes.length - a.effectSizes.length).map((item, j) => (
              <div
                className="row"
                style={{ display: "flex" }}
                key={item.outcomeKey}
              >
                <div
                  key={item.outcomeKey}
                  style={{
                    width: 200,
                    textAlign: "right",
                    paddingRight: "10px",
                    paddingLeft: item.outcomeTag.level * 12 + "px",
                  }}
                >
                  <div>{item.outcomeLabel}</div>
                </div>
                {uniqueInterventions.map((intervention, i) => {
                  const match = filteredData.find(
                    (d) =>
                      d.outcomeKey === item.outcomeKey &&
                      d.interventionKey === intervention.interventionKey
                  );
                  return (
                    <div
                      key={i}
                      style={{
                        width: 30,
                        height: 30,
                        backgroundColor: match
                          // ? "transparent"
                          ? cScale(match.meanEffectSize)
                          : "transparent",
                      }}
                      onMouseOver={() => setHovered(`${i}_${j}`)}
                      onMouseOut={() => setHovered(null)}
                    >
                      <div
                        key={intervention.interventionKey}
                        style={{ fontSize: "12px", textAlign: "center", cursor: "pointer" }}
                      >
                        {/* {match ? match.meanEffectSize.toFixed(2) : ""}
                        {match ? "(" + match.effectSizes.length + ")" : ""} */}
                        <svg width="30" height="30">
                          <text fontWeight={hovered === `${i}_${j}` ? "bold" : "normal"} x="15" y="15" fill="black" textAnchor="middle" dominantBaseline="middle">{item.effectSizes.length}</text>
                          {/* <circle cx="15" cy="15" r={Math.sqrt(item.effectSizes.length)} stroke="black" fill={cScale(match.meanEffectSize)} /> */}
                        </svg>
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    </Box>
  );
};

export default Matrix;
