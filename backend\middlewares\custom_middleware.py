from fastapi import Request, status

from fastapi.responses import JSONResponse
from services.authentication import jwt_decode, TokenData, AuthenticationError
import logging

logger = logging.getLogger(__name__)


def get_auth_token_data(request: Request) -> TokenData:
    auth_header = request.headers.get("Authorization")

    if not auth_header or not auth_header.startswith("Bearer "):
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid Authorization header",
        )
    token = auth_header.split(" ")[1]
    if not token:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid token",
        )
    try:
        payload = jwt_decode(token)
        return TokenData(**payload)
    except Exception:
        raise AuthenticationError(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Expired or Invalid token"
        )


def request_requires_auth(request: Request):
    if request.method not in ["GET", "POST", "DELETE", "PUT"]:
        return False

    # Check if the route is /conversations/*
    return "/conversations" in request.url.path


async def handle_authentication_token(request: Request, call_next):
    try:
        if request_requires_auth(request):
            token_data = get_auth_token_data(request)
            user_id = token_data.user_id

            request.state.user_id = user_id

        response = await call_next(request)
        return response

    except AuthenticationError as e:
        return JSONResponse(status_code=e.status_code, content=e.detail)
