from sqlalchemy import insert
from datetime import datetime
from database.models import Waitlist
from database.userdata import get_userdata_db_session
from utils.measure import measure_async_time


@measure_async_time
async def add_to_waitlist(email: str, organization: str):
    async with get_userdata_db_session() as session:
        stmt = insert(Waitlist).values(
            email=email,
            organization=organization,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        await session.execute(stmt)
        await session.commit()
