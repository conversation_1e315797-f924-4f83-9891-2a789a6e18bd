import React, { useState, useCallback } from 'react';
import AnimatedParagraph from "./AnimatedParagraph";
import { Source } from "../../../../../types/ConversationTypes";

interface AnimatedListProps {
  children: React.ReactNode;
  onComplete: () => void;
  messageId?: string;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  sources?: Source[];
  plotData?: any;
}

const AnimatedList = React.memo(({
  children,
  onComplete,
  messageId,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  sources,
  plotData

}: AnimatedListProps) => {
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const childrenArray = React.Children.toArray(children);

  const handleItemAnimated = useCallback((index: number) => {
    if (index === currentItemIndex) {
      if (index === childrenArray.length - 1) {
        onComplete();
      } else {
        setCurrentItemIndex(index + 1);
      }
    }
  }, [childrenArray.length, currentItemIndex, onComplete]);

  return (
    <>
      {childrenArray.map((child, index) => {
        const content = child.type === "li" ? child.props.children : child;
        return (
          <li
            key={index}
            style={{
              opacity: index <= currentItemIndex ? 1 : 0,
              display: 'list-item',
            }}
          >
            {index <= currentItemIndex && (
              <AnimatedParagraph
                onComplete={() => handleItemAnimated(index)}
                messageId={messageId}
                onViewOnPlotClicked={onViewOnPlotClicked}
                onViewOnSourceClicked={onViewOnSourceClicked}
                sources={sources}
                plotData={plotData}
              >
                {content}
              </AnimatedParagraph>
            )}
          </li>
        );
      })}
    </>
  );
});

export default AnimatedList;