import React, { useState, useEffect, useRef, useCallback } from "react";
import { Paper, Box } from "@mui/material";
import Image from "next/image";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

interface TooltipState {
    tooltip: { shadow: boolean; active: boolean };
    graph: { shadow: boolean; active: boolean };
    source1: { shadow: boolean; active: boolean };
    source2: { shadow: boolean; active: boolean };
    source3: { shadow: boolean; active: boolean };
    [key: string]: { shadow: boolean; active: boolean } | undefined;
}
const Section1 = () => {
    const theme = useTheme();
    const [tooltipState, setTooltipState] = useState<TooltipState>({
        tooltip: { shadow: false, active: false },
        graph: { shadow: false, active: false },
        source1: { shadow: false, active: false },
        source2: { shadow: false, active: false },
        source3: { shadow: false, active: false },
    });

    const sectionRef = useRef(null);
    const imageWrapperRef = useRef<HTMLDivElement | null>(null);
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();

    const getReferenceWidth = () => imageWrapperRef.current?.offsetWidth || 0;
    const getReferenceHeight = () => imageWrapperRef.current?.offsetHeight || 0;

    const calculatePosition = useCallback(
        (referenceWidth: number, positionPercentage: number, elementWidthPercentage: number) => {
            const position = (referenceWidth * positionPercentage) / 100;
            const elementWidth = (referenceWidth * elementWidthPercentage) / 100;
            return { position, elementWidth };
        },
        []
    );

    const getGraphPosition = useCallback(() => {
        const referenceWidth = getReferenceWidth();
        if (isMobile) {
            return {
                right: calculatePosition(referenceWidth, 0, 33.6).position,
                top: calculatePosition(referenceWidth, 0, 45.7).position,
                width: calculatePosition(referenceWidth, -5, 33.6).elementWidth,
                height: calculatePosition(referenceWidth, 35, 45.7).elementWidth,
            };
        }
        if (isTablet) {
            return {
                right: calculatePosition(referenceWidth, -10, 33.6).position,
                top: calculatePosition(referenceWidth, 5, 45.7).position,
                width: calculatePosition(referenceWidth, -5, 33.6).elementWidth,
                height: calculatePosition(referenceWidth, 35, 45.7).elementWidth,
            };
        }
        return {
            right: calculatePosition(referenceWidth, -16, 33.6).position,
            top: calculatePosition(referenceWidth, 7, 45.7).position,
            width: calculatePosition(referenceWidth, -5, 33.6).elementWidth,
            height: calculatePosition(referenceWidth, 35, 45.7).elementWidth,
        };
    }, [calculatePosition, getReferenceWidth]);

    const getTooltipPosition = useCallback(() => {
        const referenceWidth = getReferenceWidth();
        if (isMobile) {
            return {
                left: "83%",
                top: -20,
                transform: "translateX(-50%)",
                width: calculatePosition(referenceWidth, 9, 14.4).elementWidth,
                height: calculatePosition(referenceWidth, 17, 9).elementWidth,
            };
        }
        return {
            left: calculatePosition(referenceWidth, -7, 14.4).position,
            top: calculatePosition(referenceWidth, -2, 9).position,
            width: calculatePosition(referenceWidth, 9, 14.4).elementWidth,
            height: calculatePosition(referenceWidth, 17, 9).elementWidth,
        };
    }, [calculatePosition, getReferenceWidth, isMobile]);

    const getSourcePosition = useCallback((sourceIndex: number) => {
        const referenceWidth = getReferenceWidth();
        const referenceHeight = getReferenceHeight();
        const sourceHeight = referenceHeight * 9.4 / 100;
        const topPositions = [73, 73 + 9.4, 73 + (9.4 * 2)];
        const leftPositions = [5, 5, 5];

        return {
            top: calculatePosition(referenceHeight, topPositions[sourceIndex], 0).position,
            left: calculatePosition(referenceWidth, leftPositions[sourceIndex], 0).position,
            width: calculatePosition(referenceWidth, 0, 21.1).elementWidth,
            height: sourceHeight,
        };
    }, [calculatePosition, getReferenceWidth, getReferenceHeight]);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setTooltipState(() => ({
                        tooltip: { shadow: true, active: false },
                        graph: { shadow: true, active: false },
                        source1: { shadow: true, active: false },
                        source2: { shadow: false, active: false },
                        source3: { shadow: false, active: false },
                    }));

                    setTimeout(() => {
                        setTooltipState((prevState) => ({
                            ...prevState,
                            tooltip: { ...prevState.tooltip, active: true },
                            graph: { ...prevState.graph, active: true },
                            source1: { ...prevState.source1, active: true },
                        }));

                        setTimeout(() => {
                            setTooltipState((prevState) => ({
                                ...prevState,
                                source2: { shadow: true, active: true },
                            }));
                        }, 500);

                        setTimeout(() => {
                            setTooltipState((prevState) => ({
                                ...prevState,
                                source3: { shadow: true, active: true },
                            }));
                        }, 1000);
                    }, 100);
                }
            },
            { threshold: 0.5 }
        );

        if (sectionRef.current) observer.observe(sectionRef.current);
        return () => observer.disconnect();
    }, []);

    // Recalculate positions on window resize
    useEffect(() => {
        const handleResize = () => setTooltipState((prev) => ({ ...prev }));
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <Paper ref={sectionRef}
            sx={{
                display: "flex",
                justifyContent: "center",
                flexDirection: "column",
                alignItems: "center",
                padding: "0px",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                position: 'relative'
            }}>
            {/* Main Image Wrapper */}
            <Box ref={imageWrapperRef} sx={{
                display: "flex",
                position: "relative",
                width: isMobile ? "342px" : isTablet ? "612.037px" : "986px",
                height: isMobile ? "211.582px" : isTablet ? "378.644px" : "610px",
                boxShadow: (isTablet || isMobile) ? "none" : "0px -70px 250px -20px #E8F0FC",
                background: (isTablet || isMobile) ? "none" : theme.palette.background.default,
                borderRadius: "32px",
                mt: isMobile ? "29.94px" : isTablet ? "85.67px" : "124px",
            }}>
                <Image
                    src={isMobile ? "/images/home/<USER>" : isTablet ? "/images/home/<USER>" : "/images/home/<USER>"}
                    fill alt="Product interface displaying core features" style={{ borderRadius: "16px", objectFit: "cover" }} priority />

                {!isTablet && (
                    <>
                        {/* Tooltip Shadow */}
                        {tooltipState.tooltip.shadow && (
                            <Box sx={{
                                position: "absolute",
                                ...getTooltipPosition(),
                                opacity: tooltipState.tooltip.active ? 0 : 1,
                                transition: "opacity 0.5s ease-in-out",
                            }}>
                                <Image src="/images/home/<USER>" alt="View Graph Tooltip" width={getTooltipPosition().width} height={getTooltipPosition().height} />
                            </Box>
                        )}

                        {/* Tooltip Active */}
                        {tooltipState.tooltip.active && (
                            <Box sx={{
                                position: "absolute",
                                ...getTooltipPosition(),
                                opacity: tooltipState.tooltip.active ? 1 : 0,
                                transition: "opacity 0.5s ease-in-out",
                            }}>
                                <Image src="/images/home/<USER>" alt="Active View Graph Button" width={getTooltipPosition().width} height={getTooltipPosition().height} />
                            </Box>
                        )}

                        {/* Graph Shadow */}
                        {tooltipState.graph.shadow && (
                            <Box sx={{
                                position: "absolute",
                                ...getGraphPosition(),
                                opacity: tooltipState.graph.active ? 0 : 1,
                                transition: "opacity 0.5s ease-in-out",
                            }}>
                                <Image src="/images/home/<USER>" alt="Graph Tooltip Shadow" width={getGraphPosition().width} height={getGraphPosition().height} />
                            </Box>
                        )}

                        {/* Graph Active */}
                        {tooltipState.graph.active && (
                            <Box sx={{
                                position: "absolute",
                                ...getGraphPosition(),
                                opacity: tooltipState.graph.active ? 1 : 0,
                                transition: "opacity 0.5s ease-in-out",
                            }}>
                                <Image src="/images/home/<USER>" alt="Graph Tooltip Shadow" width={getGraphPosition().width} height={getGraphPosition().height} />
                            </Box>
                        )}

                        {/* Source 1, 2 and 3 Shadow */}
                        {
                            ["source1", "source2", "source3"].map((sourceKey, index) => (
                                tooltipState[sourceKey]?.shadow && (
                                    <Box
                                        key={`${sourceKey}-shadow`}
                                        sx={{
                                            position: "absolute",
                                            ...getSourcePosition(index),
                                            opacity: tooltipState[sourceKey]?.active ? 1 : 0,
                                        }}
                                    >
                                        <Image
                                            src={`/images/home/<USER>"source", "source_")}.svg`}
                                            alt={`${sourceKey} Shadow`}
                                            width={getSourcePosition(index).width}
                                            height={getSourcePosition(index).height}
                                        />
                                    </Box>
                                )
                            ))
                        }

                        {/* Source 1, 2 and 3 Active */}
                        {
                            ["source1", "source2", "source3"].map((sourceKey, index) => (
                                tooltipState[sourceKey]?.active && (
                                    <Box
                                        key={`${sourceKey}-active`}
                                        sx={{
                                            position: "absolute",
                                            ...getSourcePosition(index),
                                            opacity: tooltipState[sourceKey]?.active ? 1 : 0,
                                        }}
                                    >
                                        <Image
                                            src={`/images/home/<USER>"source", "source_")}.png`}
                                            alt={`${sourceKey} Active`}
                                            width={getSourcePosition(index).width}
                                            height={getSourcePosition(index).height}
                                        />
                                    </Box>
                                )
                            ))
                        }
                    </>
                )}
            </Box>
        </Paper>
    );
};

export default Section1;
