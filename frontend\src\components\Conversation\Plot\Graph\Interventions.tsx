import "./Interventions.css";

const Interventions = ({
  interventions,
  onInterventionPointerOver,
  onInterventionPointerOut,
}) => {
  return (
    <div className="container">
      <h4>Interventions</h4>
      <div className="inner">
        {interventions.map((intervention) => (
          <button
            onPointerOver={() => onInterventionPointerOver(intervention.intervention_id)}
            onPointerOut={() => onInterventionPointerOut()}
            className={`intervention ${
              interventions.length === 1 ? "single" : ""
            }`}
          >
            {intervention.intervention_tag_short_labels}
          </button>
        ))}
      </div>
    </div>
  );
};

export default Interventions;
