# Argilla Setup

Data annotation platform for methodology quality assessment.

## Quick Start

### Local Development
```bash
cd agent/src/agent/eval/argilla
docker-compose -f docker-compose.local.yaml up -d
# Access: http://localhost:6900 (argilla/12345678)
```

### Cloud Deployment
```bash
cd agent/src/agent/eval/argilla
cp env.example .env  # Edit with your values
./setup_argilla_sql.sh
./deploy_argilla_service.sh
```

## Testing

```bash
cd agent
poetry run python tests/test_argilla_upload.py
```

The testing script:
- Connects to **cloud Argilla deployment** if `CLOUD_ARGILLA_API_URL` is set, otherwise falls back to **local** (`http://localhost:6900`)
- Deletes any existing `methodology-annotation-test` dataset
- Creates a fresh dataset with text field and label questions (positive/negative/neutral)
- Uploads 2 sample methodology texts for annotation
- Provides the dashboard URL to access the dataset

**Note**: The script automatically detects whether to use cloud or local based on your environment variables.

## Configuration

Environment variables are loaded from the local `.env` file in this directory:

### GCP Project Configuration
- `PROJECT_ID`: Your Google Cloud Project ID
- `REGION`: GCP region for resources

### Cloud SQL Database Configuration
- `INSTANCE_NAME`: Cloud SQL instance name
- `DB_NAME`: Database name within the instance
- `DB_USER`: Database user for Argilla
- `DB_PASSWORD`: Strong password for database access

### Argilla User Configuration
- `OWNER_USERNAME`: Owner user (full access to all workspaces)
- `OWNER_PASSWORD`: Owner user password
- `OWNER_API_KEY`: Owner user API key
- `ADMIN_USERNAME`: Admin user (manages datasets and users)
- `ADMIN_PASSWORD`: Admin user password
- `ADMIN_API_KEY`: Admin user API key
- `ANNOTATOR_USERNAME`: Annotator user (can annotate datasets)
- `ANNOTATOR_PASSWORD`: Annotator user password
- `ANNOTATOR_API_KEY`: Annotator user API key

### Generated Configuration
- `ARGILLA_DATABASE_URL`: Database connection URL (generated by setup script)
- `CLOUD_ARGILLA_API_URL`: Argilla server URL (generated by Cloud Run deployment)

## File Structure

- `docker-compose.local.yaml`: Local development stack
- `setup_argilla_sql.sh`: Cloud SQL database setup
- `deploy_argilla_service.sh`: Cloud Run deployment
- `env.example`: Environment template
- `.env`: Local configuration (create from env.example)

## Resources

- [Argilla Docs](https://docs.argilla.io/)
- [Google Cloud Run](https://cloud.google.com/run/docs)
