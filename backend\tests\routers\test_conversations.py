from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch

import pytest
from fastapi import BackgroundTasks
from fastapi.responses import PlainTextResponse, StreamingResponse
from routers.conversations import get_conversation_message_summary

# Sample test data - Updated to match the implementation expectation
SAMPLE_SOURCES = [
    {
        "paper_id": 1,
        "citation": {"text": "Citation 1", "journal_name": "Journal 1"},
        "title": "Paper 1",
    },
    {
        "paper_id": 2,
        "citation": {"text": "Citation 2", "journal_name": "Journal 2"},
        "title": "Paper 2",
    },
]

# Updated to include abstract_summary field that the implementation expects
SAMPLE_ABSTRACTS = [
    {
        "id": 1,
        "abstract": "This is abstract for paper 1",
        "abstract_summary": "Summary for paper 1",
    },
    {
        "id": 2,
        "abstract": "This is abstract for paper 2",
        "abstract_summary": None,  # This will fall back to abstract
    },
]


@pytest.fixture
def mock_message():
    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}
    return message


@pytest.fixture
def mock_response_with_sources():
    response = Mock()
    response.has_sources.return_value = True
    response.sources_data.return_value = SAMPLE_SOURCES
    response.has_plot_data.return_value = False
    response.sanitized_summary.return_value = "Test response text"
    return response


@pytest.fixture
def mock_response_no_sources():
    response = Mock()
    response.has_sources.return_value = False
    response.has_plot_data.return_value = False
    response.sanitized_summary.return_value = "Test response text"
    return response


@pytest.mark.asyncio
async def test_get_conversation_message_summary_with_abstracts(
    mock_message, mock_response_with_sources
):
    """Test successful abstract integration in message summary"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    # Track what gets passed to add_message_sources
    captured_sources = {}

    async def capture_add_message_sources(message_id, sources):
        captured_sources["sources"] = sources

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=mock_response_with_sources)

    with patch("routers.conversations.fetch_message", return_value=mock_message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids",
        return_value=SAMPLE_ABSTRACTS,
    ), patch(
        "routers.conversations.add_message_sources",
        side_effect=capture_add_message_sources,
    ), patch(
        "routers.conversations.update_message"
    ), patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')

        # Verify abstracts were correctly added to sources
        assert "sources" in captured_sources
        sources = captured_sources["sources"]
        assert len(sources) == 2
        assert sources[0]["citation"]["abstract"] == "Summary for paper 1"
        assert sources[1]["citation"]["abstract"] == "This is abstract for paper 2"

        # Verify original citation data is preserved
        assert sources[0]["citation"]["text"] == "Citation 1"
        assert sources[0]["citation"]["journal_name"] == "Journal 1"


@pytest.mark.asyncio
async def test_get_conversation_message_summary_no_sources(
    mock_message, mock_response_no_sources
):
    """Test that abstract service is not called when no sources are present"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=mock_response_no_sources)

    with patch("routers.conversations.fetch_message", return_value=mock_message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids"
    ) as mock_get_abstracts, patch(
        "routers.conversations.add_message_sources"
    ) as mock_add_sources, patch(
        "routers.conversations.update_message"
    ), patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')

        # Verify abstract service was not called
        mock_get_abstracts.assert_not_called()
        mock_add_sources.assert_not_called()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_empty_paper_ids():
    """Test handling of sources with empty or missing paper_ids"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Sources with None paper_ids (to match current code expectation)
    sources_no_paper_ids = [
        {"paper_id": None, "citation": {"text": "Citation 1"}, "title": "Paper 1"},
        {"paper_id": None, "citation": {"text": "Citation 2"}, "title": "Paper 2"},
    ]

    response = Mock()
    response.has_sources.return_value = True
    response.sources_data.return_value = sources_no_paper_ids
    response.has_plot_data.return_value = False
    response.sanitized_summary.return_value = "Test response text"

    captured_sources = {}

    async def capture_add_message_sources(message_id, sources):
        captured_sources["sources"] = sources

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=response)

    with patch("routers.conversations.fetch_message", return_value=message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids", return_value=[]
    ), patch(
        "routers.conversations.add_message_sources",
        side_effect=capture_add_message_sources,
    ), patch(
        "routers.conversations.update_message"
    ), patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')

        # Verify sources are still processed even without valid paper_ids
        assert "sources" in captured_sources
        sources = captured_sources["sources"]
        assert len(sources) == 2


@pytest.mark.asyncio
async def test_get_conversation_message_summary_partial_abstracts():
    """Test handling when only some paper_ids have abstracts"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    sources_multiple_ids = [
        {"paper_id": 1, "citation": {"text": "Citation 1"}, "title": "Paper 1"},
        {"paper_id": 2, "citation": {"text": "Citation 2"}, "title": "Paper 2"},
        {"paper_id": 3, "citation": {"text": "Citation 3"}, "title": "Paper 3"},
    ]

    # Only abstracts for paper_ids 1 and 3 (updated format)
    partial_abstracts = [
        {"id": 1, "abstract": "Abstract for paper 1", "abstract_summary": "Summary 1"},
        {"id": 3, "abstract": "Abstract for paper 3", "abstract_summary": None},
    ]

    response = Mock()
    response.has_sources.return_value = True
    response.sources_data.return_value = sources_multiple_ids
    response.has_plot_data.return_value = False
    response.sanitized_summary.return_value = "Test response text"

    captured_sources = {}

    async def capture_add_message_sources(message_id, sources):
        captured_sources["sources"] = sources

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=response)

    with patch("routers.conversations.fetch_message", return_value=message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids",
        return_value=partial_abstracts,
    ), patch(
        "routers.conversations.add_message_sources",
        side_effect=capture_add_message_sources,
    ), patch(
        "routers.conversations.update_message"
    ), patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')

        # Verify only matching paper_ids got abstracts
        sources = captured_sources["sources"]
        assert len(sources) == 3
        assert sources[0]["citation"]["abstract"] == "Summary 1"
        assert "abstract" not in sources[1]["citation"]  # paper_id 2 has no abstract
        assert sources[2]["citation"]["abstract"] == "Abstract for paper 3"


@pytest.mark.asyncio
async def test_get_conversation_message_summary_abstract_service_error(
    mock_message, mock_response_with_sources
):
    """Test handling when abstract service raises an exception"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=mock_response_with_sources)

    with patch("routers.conversations.fetch_message", return_value=mock_message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids",
        side_effect=Exception("Database error"),
    ), patch(
        "routers.conversations.add_message_sources"
    ), patch(
        "routers.conversations.update_message"
    ), patch(
        "routers.conversations.add_message_plot"
    ):

        # Should raise exception from the abstract service
        with pytest.raises(Exception, match="Database error"):
            await get_conversation_message_summary(conversation_id, message_id)


def test_paper_id_extraction_from_sources():
    """Test extraction of paper_ids from various source formats"""
    sources_various_formats = [
        {"paper_id": 1, "title": "Paper 1"},
        {"paper_id": "2", "title": "Paper 2"},  # string ID
        {"title": "Paper 3"},  # missing paper_id
        {"paper_id": None, "title": "Paper 4"},  # null paper_id
        {"paper_id": 5, "title": "Paper 5"},
    ]

    # Extract paper_ids as done in the actual code (updated logic)
    paper_ids = [
        source["paper_id"]
        for source in sources_various_formats
        if source.get("paper_id") is not None
    ]

    # Should get [1, "2", 5] - including string but excluding None and missing
    assert paper_ids == [1, "2", 5]


def test_citation_structure_preservation():
    """Test that abstract addition preserves existing citation structure"""
    source_with_complex_citation = {
        "paper_id": 1,
        "citation": {
            "text": "Original citation",
            "journal_name": "Test Journal",
            "doi": "10.1234/test",
            "year": 2023,
            "authors": ["Author 1", "Author 2"],
        },
        "title": "Test Paper",
    }

    abstract_data = {"abstract": "Test abstract"}

    # Simulate the citation update logic from the router
    source_with_complex_citation["citation"]["abstract"] = abstract_data["abstract"]

    citation = source_with_complex_citation["citation"]
    assert citation["abstract"] == "Test abstract"
    assert citation["text"] == "Original citation"
    assert citation["journal_name"] == "Test Journal"
    assert citation["doi"] == "10.1234/test"
    assert citation["year"] == 2023
    assert citation["authors"] == ["Author 1", "Author 2"]


def test_sources_without_citation_field():
    """Test handling of sources that don't have citation field"""
    source_no_citation = {"paper_id": 1, "title": "Test Paper"}

    # This should not cause an error - the code checks 'if citation in source'
    if "citation" in source_no_citation:
        source_no_citation["citation"]["abstract"] = "test abstract"

    # Should remain unchanged
    assert "citation" not in source_no_citation


@pytest.mark.asyncio
async def test_get_conversation_message_summary_with_source_filtering():
    """Test that conversation message summary properly handles source filtering in sanitized summaries"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock sources that will be used for filtering
    sources_data = [
        {"paper_id": 1, "short_paper_id": "A123", "citation": {"text": "Citation 1"}, "title": "Paper 1"},
        {"paper_id": 2, "short_paper_id": "B456", "citation": {"text": "Citation 2"}, "title": "Paper 2"},
    ]

    # Mock response with source references - some valid, some invalid
    response = Mock()
    response.has_sources.return_value = True
    response.sources_data.return_value = sources_data
    response.has_plot_data.return_value = False
    
    # Mock sanitized_summary to return text with source filtering applied
    response.sanitized_summary.return_value = "Research shows evidence, but [C999] was removed due to filtering."
    
    # Track captured sources
    captured_sources = {}

    async def capture_add_message_sources(message_id, sources):
        captured_sources["sources"] = sources

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=response)

    with patch("routers.conversations.fetch_message", return_value=message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids", return_value=[]
    ), patch(
        "routers.conversations.add_message_sources",
        side_effect=capture_add_message_sources,
    ), patch(
        "routers.conversations.update_message"
    ) as mock_update_message, patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')
        
        # Verify that sanitized_summary was called (which includes source filtering)
        response.sanitized_summary.assert_called_once()
        
        # Verify that update_message was called with the sanitized summary
        mock_update_message.assert_called_once()
        call_args = mock_update_message.call_args[1] if mock_update_message.call_args.kwargs else mock_update_message.call_args[0][1]
        assert "Research shows evidence, but [C999] was removed due to filtering." in str(call_args)


@pytest.mark.asyncio
async def test_get_conversation_message_summary_source_filtering_empty_sources():
    """Test source filtering behavior when no sources are available"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock response with no sources
    response = Mock()
    response.has_sources.return_value = False
    response.has_plot_data.return_value = False
    
    # When no sources are available, source filtering should remove all source references
    response.sanitized_summary.return_value = "Research shows evidence, but all source references were removed."

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=response)

    with patch("routers.conversations.fetch_message", return_value=message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids"
    ) as mock_get_abstracts, patch(
        "routers.conversations.add_message_sources"
    ) as mock_add_sources, patch(
        "routers.conversations.update_message"
    ) as mock_update_message, patch(
        "routers.conversations.add_message_plot"
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')
        
        # Verify that sanitized_summary was called
        response.sanitized_summary.assert_called_once()
        
        # Verify that abstract service was not called since no sources
        mock_get_abstracts.assert_not_called()
        mock_add_sources.assert_not_called()
        
        # Verify that update_message was called with the sanitized summary
        mock_update_message.assert_called_once()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_source_filtering_with_data():
    """Test source filtering when sources are present and data is available"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock sources that match available data
    sources_data = [
        {"paper_id": 1, "short_paper_id": "A123", "citation": {"text": "Citation 1"}, "title": "Paper 1"},
        {"paper_id": 2, "short_paper_id": "B456", "citation": {"text": "Citation 2"}, "title": "Paper 2"},
    ]

    # Mock response with sources and plot data
    response = Mock()
    response.has_sources.return_value = True
    response.sources_data.return_value = sources_data
    response.has_plot_data.return_value = True
    
    # Mock plot data
    mock_plot_data = Mock()
    mock_plot_data.to_dict.return_value = {"type": "plot", "data": "mock_data"}
    response.plot_data.return_value = mock_plot_data
    
    # Mock sanitized summary with source filtering applied
    response.sanitized_summary.return_value = "Research shows [A123] and [B456] support this finding."

    # Track captured sources and plot data
    captured_sources = {}
    captured_plot_data = {}

    async def capture_add_message_sources(message_id, sources):
        captured_sources["sources"] = sources
        
    async def capture_add_message_plot(message_id, title, data):
        captured_plot_data["plot_data"] = data
        captured_plot_data["title"] = title

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.execute = AsyncMock(return_value=response)

    with patch("routers.conversations.fetch_message", return_value=message), patch(
        "routers.conversations.AgentService", return_value=mock_agent_instance
    ), patch(
        "routers.conversations.get_paper_abstracts_by_ids", return_value=[]
    ), patch(
        "routers.conversations.add_message_sources",
        side_effect=capture_add_message_sources,
    ), patch(
        "routers.conversations.update_message"
    ) as mock_update_message, patch(
        "routers.conversations.add_message_plot",
        side_effect=capture_add_message_plot,
    ):

        result = await get_conversation_message_summary(conversation_id, message_id)

        # Verify the result is a PlainTextResponse
        assert hasattr(result, 'body')
        
        # Verify that sanitized_summary was called
        response.sanitized_summary.assert_called_once()
        
        # Verify that sources were processed
        assert "sources" in captured_sources
        assert len(captured_sources["sources"]) == 2
        
        # Verify that plot data was processed
        assert "plot_data" in captured_plot_data
        
        # Verify that update_message was called with the sanitized summary
        mock_update_message.assert_called_once()
        call_args = mock_update_message.call_args[1] if mock_update_message.call_args.kwargs else mock_update_message.call_args[0][1]
        assert "Research shows [A123] and [B456] support this finding." in str(call_args)


def test_source_filtering_integration_with_abstracts():
    """Test that source filtering works correctly when integrated with abstract processing"""
    # Mock sources with paper_ids for abstract lookup
    sources_with_abstracts = [
        {"paper_id": 1, "short_paper_id": "A123", "citation": {"text": "Citation 1"}, "title": "Paper 1"},
        {"paper_id": 2, "short_paper_id": "B456", "citation": {"text": "Citation 2"}, "title": "Paper 2"},
    ]

    # Mock abstract data
    abstract_data = [
        {"id": 1, "abstract": "Abstract 1", "abstract_summary": "Summary 1"},
        {"id": 2, "abstract": "Abstract 2", "abstract_summary": "Summary 2"},
    ]

    # Create a map from paper_id to abstract for testing
    abstract_map = {item["id"]: item for item in abstract_data}

    # Simulate the abstract integration logic
    for source in sources_with_abstracts:
        paper_id = source["paper_id"]
        if paper_id in abstract_map:
            abstract_entry = abstract_map[paper_id]
            source["citation"]["abstract"] = abstract_entry.get("abstract_summary") or abstract_entry.get("abstract")

    # Verify that sources now have abstracts
    assert sources_with_abstracts[0]["citation"]["abstract"] == "Summary 1"
    assert sources_with_abstracts[1]["citation"]["abstract"] == "Summary 2"

    # Verify that short_paper_id is still available for source filtering
    assert sources_with_abstracts[0]["short_paper_id"] == "A123"
    assert sources_with_abstracts[1]["short_paper_id"] == "B456"


@pytest.mark.asyncio
async def test_get_conversation_message_summary_stream_based_false():
    """Test that stream_based=False returns PlainTextResponse"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.get_summary = AsyncMock(return_value="Test summary text")

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance):

        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks, stream_based=False
        )

        # Verify the result is a PlainTextResponse
        assert isinstance(result, PlainTextResponse)
        mock_agent_instance.get_summary.assert_called_once()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_stream_based_true():
    """Test that stream_based=True returns StreamingResponse"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock the async generator for streaming
    async def mock_generate_summary():
        yield "chunk1"
        yield "chunk2"
        yield "chunk3"

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.generate_summary = AsyncMock(return_value=mock_generate_summary())

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance), \
         patch("routers.conversations.sanitize_plot_text", side_effect=lambda x: x):

        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks, stream_based=True
        )

        # Verify the result is a StreamingResponse
        assert isinstance(result, StreamingResponse)
        assert result.media_type == "text/event-stream"
        mock_agent_instance.generate_summary.assert_called_once()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_default_stream_based():
    """Test that stream_based defaults to False (PlainTextResponse)"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.get_summary = AsyncMock(return_value="Test summary text")

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance):

        # Call without stream_based parameter (should default to False)
        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks
        )

        # Verify the result is a PlainTextResponse (default behavior)
        assert isinstance(result, PlainTextResponse)
        mock_agent_instance.get_summary.assert_called_once()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_streaming_finished_bypasses_stream_based():
    """Test that streaming_finished_at bypasses stream_based parameter"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = "2023-01-01T00:00:00"  # Already finished
    message.text = "Cached summary text"

    # Create async mock for agent service (should not be called)
    mock_agent_instance = Mock()
    mock_agent_instance.get_summary = AsyncMock()
    mock_agent_instance.generate_summary = AsyncMock()

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance):

        # Test with stream_based=True, but should still return PlainTextResponse
        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks, stream_based=True
        )

        # Verify the result is a PlainTextResponse with cached text
        assert isinstance(result, PlainTextResponse)
        # Agent service methods should not be called since streaming is finished
        mock_agent_instance.get_summary.assert_not_called()
        mock_agent_instance.generate_summary.assert_not_called()


@pytest.mark.asyncio
async def test_get_conversation_message_summary_streaming_with_background_task():
    """Test that streaming mode properly triggers background task for finalization"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock the async generator for streaming
    async def mock_generate_summary():
        yield "First chunk"
        yield "Second chunk"

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.generate_summary = AsyncMock(return_value=mock_generate_summary())
    mock_agent_instance.get_agent_response_data = AsyncMock()

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance), \
         patch("routers.conversations.sanitize_plot_text", side_effect=lambda x: x):

        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks, stream_based=True
        )

        # Verify the result is a StreamingResponse
        assert isinstance(result, StreamingResponse)

        # Consume the stream to trigger background task
        chunks = []
        async for chunk in result.body_iterator:
            chunks.append(chunk)

        # Should have received both chunks
        assert "First chunk" in chunks
        assert "Second chunk" in chunks

        # Verify that background task queue is not empty (finalize_summary was added)
        assert len(background_tasks.tasks) > 0


@pytest.mark.asyncio
async def test_get_conversation_message_summary_streaming_error_handling():
    """Test error handling in streaming mode"""
    conversation_id = "test-conv-id"
    message_id = "test-msg-id"
    background_tasks = BackgroundTasks()

    message = Mock()
    message.streaming_finished_at = None
    message.query_values = {"query": "test query"}

    # Mock the async generator that raises an exception
    async def mock_generate_summary_error():
        yield "First chunk"
        raise Exception("Stream error")

    # Create async mock for agent service
    mock_agent_instance = Mock()
    mock_agent_instance.generate_summary = AsyncMock(return_value=mock_generate_summary_error())

    with patch("routers.conversations.fetch_message", return_value=message), \
         patch("routers.conversations.AgentService", return_value=mock_agent_instance), \
         patch("routers.conversations.sanitize_plot_text", side_effect=lambda x: x):

        result = await get_conversation_message_summary(
            conversation_id, message_id, background_tasks, stream_based=True
        )

        # Verify the result is a StreamingResponse
        assert isinstance(result, StreamingResponse)

        # Consume the stream and check error handling
        chunks = []
        async for chunk in result.body_iterator:
            chunks.append(chunk)

        # Should have received first chunk and error message
        assert any("First chunk" in str(chunk) for chunk in chunks)
        assert any("error An error occured while streaming" in str(chunk) for chunk in chunks)
