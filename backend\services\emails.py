import os
from services.files import FilesService
from utils.requests import post_json
from dotenv import load_dotenv

load_dotenv()

files_service = FilesService()

FORGOT_PASSWORD_EMAIL_TEMPLATE = files_service.load_forgot_passsword_email_template()

RESEND_API_URL = "https://api.resend.com/emails"
RESEND_API_KEY = os.getenv("RESEND_API_KEY", "re_123456789")
FROM_EMAIL = "ImpactAI <<EMAIL>>"


async def send_resend_email(to_email: str, subject: str, html_content: str):
    headers = {
        "Authorization": f"Bearer {RESEND_API_KEY}",
        "Content-Type": "application/json",
    }

    data = {
        "from": FROM_EMAIL,
        "to": [to_email],
        "subject": subject,
        "html": html_content,
    }
    await post_json(url=RESEND_API_URL, body=data, headers=headers, timeout=10.0)


def template_var_injection(template: str, data: dict) -> str:
    """Inject data into a template string."""
    for key, value in data.items():
        template = template.replace(f"{{{{{key}}}}}", value)
    return template


async def send_password_reset(email: str, token: str):
    """Send password reset email."""

    subject = "Password Reset Request"
    cta_link = f"https://impact-ai-dev.app/auth?token={token}"
    body = template_var_injection(
        FORGOT_PASSWORD_EMAIL_TEMPLATE, {"cta_link": cta_link}
    )

    await send_resend_email(to_email=email, subject=subject, html_content=body)
