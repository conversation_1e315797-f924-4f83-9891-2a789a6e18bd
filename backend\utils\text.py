import re

def remove_non_matching_sources(text: str, source_ids: list[str] = None, remove_all_if_empty: bool = False) -> str:
    """
    Removes non-matching sources from the text.
    Example 1:
        text = 'some text [J123] some [J456, P123] and more'
        source_ids = ['J123', 'J456']
        return 'some text [J123] some [J456] and more'

    Example 2:
        text = 'some text [J123] some [J456, P123] and more'
        source_ids = ['J123', 'J456', 'P123']
        return 'some text [J123] some [J456] and more'

    Example 3:
        text = 'some text [J123] some [J456, P123] and more'
        source_ids = ['J456', 'P123']
        return 'some text some [J456] and more'

    Example 4:
        text = 'some text [J123] some [J456, P123] and more'
        source_ids = []
        return 'some text some and more'
    """

    pattern_match = r'\[(\w+)\]'
    matches = re.findall(pattern_match, text)
    for match in matches:
        if match not in source_ids:
            text = re.sub(pattern_match, '', text)
    return text


def remove_non_matching_plot_pairs(text: str, pairs: list[list[int]] = None, remove_all_if_empty: bool = False) -> str:
    """
    Removes non-matching plot pairs from the text.
    Example:
        text = 'some text [intervention=123, outcome=456] some text [intervention=789, outcome=1012]'
        pairs = [[123,456], [789,1011]] # intervention=123, outcome=456 and intervention=789, outcome=1011
        return 'some text [intervention=123, outcome=456] some text ' # it should remove the second pair
    
    Args:
        text: The text to process
        pairs: List of [intervention_id, outcome_id] pairs to keep
        remove_all_if_empty: If True, removes all intervention-outcome pairs when pairs is empty
    """

    if pairs is None:
        pairs = []

    # If no pairs and remove_all_if_empty is False, return original text
    if len(pairs) == 0 and not remove_all_if_empty:
        return text

    pattern_match = r'\[intervention=(\d+), outcome=(\d+)\]'
    matches = re.findall(pattern_match, text)
    
    for match in matches:
        # Convert string match to list of integers for comparison
        match_list = [int(match[0]), int(match[1])]
        
        # If no pairs are provided and remove_all_if_empty is True, remove all
        # If pairs are provided, only remove non-matching pairs
        if len(pairs) == 0 or match_list not in pairs:
            text = re.sub(r'\[intervention={}, outcome={}\]'.format(match[0], match[1]), '', text)
    
    return text


def sanitize_plot_text(text: str) -> str:
    """
    Handles the text and replaces them according to the following rules:
    # check if the format is any of the following and replace with "".
    # 1. some text [intervention=123, outcome=ALL] => some text
    # 2. some text [intervention=ALL, outcome=1234] => some text
    # 3. some text 0.2 [intervention=123, outcome=456] => some text 0.2 [intervention=123, outcome=456]
    # 4. some text (0.2) [intervention=123, outcome=456] => some text 0.2 [intervention=123, outcome=456]
    # 5. some text (0.2, 4.5) [intervention=123, outcome=456] => some text 0.2, 4.5 [intervention=123, outcome=456]
    # 6. some text [intervention=123] => some text
    # 7. some text [outcome=123] => some text
    """

    # Pattern to match [intervention=X, outcome=Y] where either X or Y is "ALL"
    # This handles rules 1 and 2
    pattern_all = r"\[intervention=(?:ALL|\d+),\s*outcome=(?:ALL|\d+)\]"

    # Check if either intervention or outcome is "ALL" and remove the entire bracket
    def should_remove_bracket(match):
        bracket_content = match.group(0)
        return "ALL" in bracket_content

    # Remove brackets that contain "ALL" (rules 1 and 2)
    text = re.sub(
        pattern_all, lambda m: "" if should_remove_bracket(m) else m.group(0), text
    )

    # Pattern to match brackets with only intervention or only outcome (rules 6 and 7)
    pattern_single = r"\[(?:intervention=\d+|outcome=\d+)\]"

    # Remove brackets that contain only intervention or only outcome
    text = re.sub(pattern_single, "", text)

    # Pattern to match parentheses with numbers: (0.2) or (0.2, 4.5)
    # This handles rules 4 and 5
    pattern_parentheses = r"\(([0-9.]+(?:,\s*[0-9.]+)*)\)"

    # Replace parentheses with just the content (remove the parentheses)
    text = re.sub(pattern_parentheses, r"\1", text)

    return text
