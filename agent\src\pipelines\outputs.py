"""Shared output models for pipelines."""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel


class PipelineResponse(BaseModel):
    """Standardized pipeline response."""

    intent: str
    follow_up_question: bool = False
    thought: str
    user_query: str
    observation: str
    pipeline_info: Dict[str, Any]
    answer: Optional[str] = None


class PipelineInfo(BaseModel):
    """Pipeline execution information."""

    name: str
    status: str
    step_completed: Optional[str] = None
    step_failed: Optional[str] = None
    steps_completed: Optional[List[str]] = None
    data_points: Optional[int] = None
    papers_analyzed: Optional[int] = None
    extended_entities: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


def create_pipeline_response(
    intent: str,
    status: str,
    user_query: str,
    observation: str,
    pipeline_name: str,
    thought: str = "",
    answer: Optional[str] = None,
    **kwargs,
) -> Dict[str, Any]:
    """Create a standardized pipeline response."""

    pipeline_info = PipelineInfo(name=pipeline_name, status=status, **kwargs)

    response = PipelineResponse(
        intent=intent,
        thought=thought,
        user_query=user_query,
        observation=observation,
        pipeline_info=pipeline_info.dict(exclude_none=True),
        answer=answer,
    )

    return response.dict()
