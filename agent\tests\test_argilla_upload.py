import argilla as rg
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv("src/agent/eval/argilla/.env")

# Check if env vars are loaded
api_url = os.getenv("CLOUD_ARGILLA_API_URL", "http://localhost:6900")
api_key = os.getenv("OWNER_API_KEY", "argilla.apikey")

print(f"API URL: {api_url}")
print(f"API Key: {api_key[:10]}..." if api_key else "API Key: NOT SET")

# Connect to Argilla
client = rg.Argilla(api_url=api_url, api_key=api_key)

# Delete existing dataset
try:
    dataset_to_delete = client.datasets(name="methodology-annotation-test")
    dataset_deleted = dataset_to_delete.delete()
    print("✅ Dataset deleted successfully!")
except Exception as e:
    print(f"Could not delete dataset: {e}")

# Create new dataset
settings = rg.Settings(
    fields=[rg.TextField(name="text")],
    questions=[
        rg.LabelQuestion(name="label", labels=["positive", "negative", "neutral"])
    ],
)

dataset = rg.Dataset(
    name="methodology-annotation-test", workspace="admin", settings=settings
)
dataset.create()
print("✅ Created new dataset")

# Upload small test records
records = [
    rg.Record(fields={"text": "Randomized controlled trials are the gold standard."}),
    rg.Record(fields={"text": "Instrumental variables help with endogeneity."}),
]

dataset.records.log(records)
print("✅ Uploaded 2 test records!")
