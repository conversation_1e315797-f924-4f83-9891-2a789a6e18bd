import React, { RefObject } from "react";
import { Box } from "@mui/material";
import { Message, FilterState } from "../../../types/ConversationTypes";
import { MessageContent } from './MessageContent';
import { RightSidePanel } from './RightSidePanel';
import PromptSection from "../../PromptControl/PromptSection";
import useScrollbarWidth from "../../../hooks/useScrollbarWidth";
import { PROMPT_LABEL } from "../../../utils/labels";

interface SingleColumnLayoutProps {
    isMobileOrTablet: boolean;
    conversationWrapperScrollRef: RefObject<HTMLDivElement>;
    initialConversationMessages: Message[];
    handleUserPrompt: (e: any) => void;
    displaySystemLoader: boolean;
    loadingMessages: boolean;
    lastSystemMessageId: string | null;
    hoveredMessageId: string | null;
    setHoveredMessageId: (id: string | null) => void;
    informationMessageId: string | null;
    streamingEnded: boolean;
    summaryStreamedText: string;
    openRightSection: "sources" | "charts" | null;
    activeInfoMessageIdForPanel: string | null;
    isTwoColumnLayoutWithPanelOpen: boolean;
    localConversationId: string;
    handleViewOnPlotClickedInternal: (payload: any) => void;
    handleViewOnSourceClickedInternal: (payload: any) => void;
    handleOpenSources: (id: string, fromSourcesButton: boolean) => void;
    handleOpenCharts: (id: string, payload: any) => void;
    setDisplaySystemLoader: (flag: boolean) => void;
    setStreamingEnded: (flag: boolean) => void;
    setSummaryStreamedText: (text: string) => void;
    setFullMessageInfoFetched: (msg: Message | null) => void;
    activeQuestionId: string | null;
    lastMessageRef: RefObject<HTMLDivElement>;
    buttonStyle: any;
    theme: any;
    handleCloseRightSection: () => void;
    panelSources: any[];
    selectedStudy: string;
    activeSourcePaperIds: string[];
    activeSourceMessageId: string | null;
    setActiveSourcePaperIds: (ids: string[]) => void;
    setActiveSourceMessageId: (id: string | null) => void;
    handleFiltersChange: (messageId: string, filters: FilterState, type: 'sources' | 'plot') => void;
    handleResetFilters: () => void;
    activeFiltersCount: number;
    activePlotDetails: any;
    setSelectedStudy: (id: string) => void;
}

export const SingleColumnLayout: React.FC<SingleColumnLayoutProps> = ({
    isMobileOrTablet,
    conversationWrapperScrollRef,
    initialConversationMessages,
    handleUserPrompt,
    displaySystemLoader,
    loadingMessages,
    lastSystemMessageId,
    hoveredMessageId,
    setHoveredMessageId,
    informationMessageId,
    streamingEnded,
    summaryStreamedText,
    openRightSection,
    activeInfoMessageIdForPanel,
    isTwoColumnLayoutWithPanelOpen,
    localConversationId,
    handleViewOnPlotClickedInternal,
    handleViewOnSourceClickedInternal,
    handleOpenSources,
    handleOpenCharts,
    setDisplaySystemLoader,
    setStreamingEnded,
    setSummaryStreamedText,
    setFullMessageInfoFetched,
    activeQuestionId,
    lastMessageRef,
    buttonStyle,
    theme,
    handleCloseRightSection,
    panelSources,
    selectedStudy,
    activeSourcePaperIds,
    activeSourceMessageId,
    setActiveSourcePaperIds,
    setActiveSourceMessageId,
    handleFiltersChange,
    handleResetFilters,
    activeFiltersCount,
    activePlotDetails,
    setSelectedStudy,
}) => {
    const scrollbarWidth = useScrollbarWidth();
    return (
        <Box
            ref={conversationWrapperScrollRef}
            className='conversation-wrapper'
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
                height: `100%`,
                px: isMobileOrTablet ? "0px" : "48px",
                overflowY: 'auto',
                paddingRight: scrollbarWidth > 0 ? `${scrollbarWidth}px` : 0,
                pt: isMobileOrTablet ? "40px" : "0px",
                pb: isMobileOrTablet ? "84px" : "0px",
                transition: "padding-top 0.3s ease",
            }}
        >
            <Box
                className="presentation-wrapper"
                sx={{
                    width: {
                        xs: '90%',
                        sm: '90%',
                        md: '70%',
                        lg: '70%',
                    },
                    maxWidth: '800px',
                }}
            >
                <Box id="chat-history">
                    {initialConversationMessages.map((msg, index) => (
                        <Box className="messages-wrapper" key={msg.id}
                            sx={{
                                mb: index === initialConversationMessages.length - 1 ? 4 : 1,
                                width: '100%'
                            }}
                        >
                            <MessageContent
                                msg={msg}
                                index={index}
                                lastSystemMessageId={lastSystemMessageId}
                                hoveredMessageId={hoveredMessageId}
                                setHoveredMessageId={setHoveredMessageId}
                                informationMessageId={informationMessageId}
                                displaySystemLoader={displaySystemLoader}
                                streamingEnded={streamingEnded}
                                summaryStreamedText={summaryStreamedText}
                                openRightSection={openRightSection}
                                activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                                isTwoColumnLayoutWithPanelOpen={isTwoColumnLayoutWithPanelOpen}
                                localConversationId={localConversationId}
                                handleViewOnPlotClickedInternal={handleViewOnPlotClickedInternal}
                                handleViewOnSourceClickedInternal={handleViewOnSourceClickedInternal}
                                handleOpenSources={handleOpenSources}
                                handleOpenCharts={handleOpenCharts}
                                setDisplaySystemLoader={setDisplaySystemLoader}
                                setStreamingEnded={setStreamingEnded}
                                setSummaryStreamedText={setSummaryStreamedText}
                                setFullMessageInfoFetched={setFullMessageInfoFetched}
                                activeQuestionId={activeQuestionId}
                                lastMessageRef={lastMessageRef}
                                buttonStyle={buttonStyle}
                                theme={theme}
                                initialConversationMessages={initialConversationMessages}
                            />
                            {isMobileOrTablet && openRightSection && activeInfoMessageIdForPanel === msg.id && (
                                <Box sx={{ mt: 2 }}>
                                    <RightSidePanel
                                        openRightSection={openRightSection}
                                        activeInfoMessageIdForPanel={activeInfoMessageIdForPanel}
                                        handleCloseRightSection={handleCloseRightSection}
                                        panelSources={panelSources}
                                        selectedStudy={selectedStudy}
                                        activeSourcePaperIds={activeSourcePaperIds}
                                        activeSourceMessageId={activeSourceMessageId}
                                        setActiveSourcePaperIds={setActiveSourcePaperIds}
                                        setActiveSourceMessageId={setActiveSourceMessageId}
                                        handleFiltersChange={handleFiltersChange}
                                        handleResetFilters={handleResetFilters}
                                        activeFiltersCount={activeFiltersCount}
                                        activePlotDetails={activePlotDetails}
                                        onSelectStudy={setSelectedStudy}
                                        initialConversationMessages={initialConversationMessages}
                                    />
                                </Box>
                            )}
                        </Box>
                    ))}
                </Box>
                {!window.location.href.includes('impact-ai-prod.app') && (
                    <Box className="prompt-wrapper">
                        <PromptSection
                            centeredContent={false}
                            handleChange={handleUserPrompt}
                            queryLabelText={PROMPT_LABEL}
                            isLoading={displaySystemLoader || loadingMessages}
                            dataTags={{}}
                            selectedTag={''}
                            onCloseDropdown={() => { }}
                            chipClickCount={() => { }}
                        />
                    </Box>
                )}
            </Box>
        </Box>
    );
};