import "./LabelBox.css";
import { LabelProps, Label } from "./ForestPlotJWT.types";
import * as d3 from "d3";

const maxLabelLength = 28;

const getLabel = (label: string) =>
  label.length > maxLabelLength + 6
    ? `${label.slice(0, maxLabelLength)}…`
    : label;

const c = d3.scaleLinear().domain([1, 10]).range(["#f5f9fe", "#163661"]);

const LabelBox = ({
  onLabelPointerOver,
  onLabelPointerOut,
  isHovered,
  isNothingHovered,
  pairHeight,
  pairMarginBottom,
  label,
  x,
}: LabelProps) => {
  const handlePointerOver = (label: Label) => onLabelPointerOver(label.label);

  return (
    <g
      className="label-box"
      onPointerOver={() => handlePointerOver(label)}
      onPointerOut={onLabelPointerOut}
    >
      <foreignObject
        x={x}
        y={label.y}
        height={pairHeight + pairMarginBottom}
        width={140}
      >
        <div
          className={isNothingHovered || isHovered ? "" : "dimmed"}
          style={{
            backgroundColor: c(
              label.links.map((d) => d.data.length).flat().length
            ),
          }}
        >
          {getLabel(label.label)}
        </div>
      </foreignObject>
    </g>
  );
};

export default LabelBox;
