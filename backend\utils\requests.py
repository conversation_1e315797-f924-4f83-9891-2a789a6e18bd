import httpx
import asyncio



async def post_json(url: str, body, headers: dict = None, timeout: float = 300.0):
    """
    Asynchronously posts JSON data to the specified URL with custom options.

    Args:
        url (str): The target URL
        body: The JSON-serializable data to send
        headers (dict, optional): Custom headers to include
        timeout (float, optional): Request timeout in seconds

    Returns:
        The JSON response from the server
    """
    default_headers = {"Content-Type": "application/json"}
    if headers:
        default_headers.update(headers)

    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.post(url, json=body, headers=default_headers)
        response.raise_for_status()
        return response.json()

async def get_text(url: str, params: dict = None, timeout: float = 300.0):
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(url, params=params)
        response.raise_for_status()
        return response.text

async def get_json(url: str, params: dict = None, timeout: float = 300.0):
    """
    Asynchronously sends a GET request to the specified URL and returns the JSON response.

    Args:
        url (str): The target URL to send the GET request to
        params (dict, optional): Query parameters to include in the request

    Returns:
        The JSON response from the server

    Raises:
        httpx.HTTPStatusError: If the request fails with an HTTP error
        httpx.RequestError: If there's a network-related error
    """
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(url, params=params)
        response.raise_for_status()  # Raises an exception for 4xx/5xx responses
        return response.json()


async def get_stream_events(url: str, params: dict = None, headers: dict = None, timeout: float = 300.0):
    """
    Asynchronously connects to a server-sent events (SSE) endpoint and yields each event as it arrives.
    Retries up to 3 times with exponential backoff on connection issues.

    Args:
        url (str): The target URL to connect to (should return 'text/event-stream')
        params (dict, optional): Query parameters to include in the request
        headers (dict, optional): Custom headers to include
        timeout (float, optional): Request timeout in seconds

    Yields:
        str: The raw event data as it arrives

    Raises:
        httpx.HTTPStatusError: If the request fails with an HTTP error after retries
        httpx.RequestError: If there's a network-related error after retries
    """
    default_headers = {"Accept": "text/event-stream"}
    if headers:
        default_headers.update(headers)

    max_retries = 3
    for attempt in range(1, max_retries + 1):
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                async with client.stream("GET", url, params=params, headers=default_headers) as response:
                    response.raise_for_status()
                    async for chunk in response.aiter_bytes():
                        if chunk.strip() == b"":
                            continue
                        yield chunk.decode()
            break  # If successful, break out of the retry loop
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            if attempt == max_retries:
                raise  # Re-raise the last exception if out of retries
            backoff = 2 ** (attempt - 1)
            await asyncio.sleep(backoff)
