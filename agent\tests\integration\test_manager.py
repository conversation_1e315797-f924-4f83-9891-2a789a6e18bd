from src.agent.main import Agent
import asyncio
from pathlib import Path
import sys
import os

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


async def main():
    # Initialize config and agent

    # Set up config
    config = {
        "model_name": "gemini-2.0-flash-001",
        "temperature": 0.1,
        "max_tokens": 8192,
        "max_iterations": 10,
        "verbose": True,
        "bucket_name": "scihub-papers-processed",
        "use_gcp": True,
        "google_api_key": os.environ.get("GOOGLE_API_KEY"),
    }

    agent = Agent(config)

    # Process a query
    query = "what are different ways to implement cash transfer programs"
    await agent.execute(query)

    print("\nCache state after execution:")
    print("dict_rows defined:", hasattr(agent.tool_manager.cache, "dict_rows"))
    print("dict_rows value:", getattr(agent.tool_manager.cache, "dict_rows", None))

    # Get tool data
    tool_data = agent.tool_manager.get_tool_data()

    # Print the tool data for inspection
    print("\nTool Data Contents:")
    for key, value in tool_data.items():
        print("-" * 100)
        print(f"\n{key}:")
        print(value)


if __name__ == "__main__":
    asyncio.run(main())
