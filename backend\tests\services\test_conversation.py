import pytest
from uuid import UUID, uuid4
from datetime import datetime
from unittest.mock import Async<PERSON><PERSON>, Mo<PERSON>, patch
from sqlalchemy import Update
from services.conversation import (
    clear_user_conversations,
    fetch_conversations,
    initialise_new_conversation,
    update_conversation_title,
    insert_conversation_message,
)
from database.models import Conversation


@pytest.fixture
def mock_session():
    session = AsyncMock()
    # Properly configure async context manager
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock(return_value=None)
    # Pre-configure execute and commit as AsyncMock to avoid runtime warnings
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    return session


@pytest.fixture
def sample_user_id():
    return UUID("12345678-1234-5678-1234-************")


@pytest.fixture
def sample_conversation_id():
    return UUID("*************-8765-4321-************")


@pytest.mark.asyncio
async def test_clear_user_conversations(mock_session, sample_user_id):
    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ):
        await clear_user_conversations(sample_user_id)

        # Verify the update query was executed
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

        # Get the update query that was executed
        update_query = mock_session.execute.call_args[0][0]
        compiled_update_query = update_query.compile()
        assert isinstance(update_query, Update)
        assert str(sample_user_id), str(compiled_update_query.params["user_id_1"])
        assert "deleted_at" in str(compiled_update_query)


@pytest.mark.asyncio
async def test_fetch_conversations(mock_session, sample_user_id):
    # Create mock conversations
    mock_conversations = [
        Conversation(
            id=str(uuid4()), user_id=str(sample_user_id), created_at=datetime.now()
        ),
        Conversation(
            id=str(uuid4()), user_id=str(sample_user_id), created_at=datetime.now()
        ),
    ]

    # Mock the query execution result
    mock_result = Mock()
    mock_result.scalars.return_value.all.return_value = mock_conversations
    mock_session.execute.return_value = mock_result

    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ):
        result = await fetch_conversations(sample_user_id)

        assert result == mock_conversations
        mock_session.execute.assert_called_once()

        # Verify query conditions
        query = mock_session.execute.call_args[0][0]
        assert str(sample_user_id) == query.compile().params["user_id_1"]
        assert "deleted_at is null" in str(query).lower()
        assert "order by" in str(query).lower()


@pytest.mark.asyncio
async def test_initialise_new_conversation(
    mock_session, sample_user_id, sample_conversation_id
):
    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ):
        await initialise_new_conversation(sample_user_id, sample_conversation_id)

        # Verify the insert query was executed
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

        # Get the insert query that was executed
        insert_query = mock_session.execute.call_args[0][0]
        query_params = insert_query.compile().params
        assert str(sample_user_id) in str(query_params)
        assert str(sample_conversation_id) in str(query_params)
        assert "IGNORE" in str(insert_query)


@pytest.mark.asyncio
async def test_update_conversation_title(mock_session, sample_conversation_id):
    test_title = "Test Conversation"

    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ):
        await update_conversation_title(sample_conversation_id, test_title)

        # Verify the update query was executed
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

        # Get the update query that was executed
        update_query = mock_session.execute.call_args[0][0]
        update_params = update_query.compile().params
        assert str(sample_conversation_id) in str(update_params)
        assert test_title in str(update_params)
        assert "title IS NULL" in str(update_query)


@pytest.mark.asyncio
async def test_insert_conversation_message(mock_session, sample_conversation_id):
    test_text = "Test message"
    test_author = "user"
    test_type = "answer"

    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ), patch(
        "services.conversation.uuid.uuid4",
        return_value=UUID("12345678-1234-5678-1234-************"),
    ):

        result = await insert_conversation_message(
            sample_conversation_id, text=test_text, author=test_author, type=test_type
        )

        # Verify the insert query was executed
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

        # Get the insert query that was executed
        insert_query = mock_session.execute.call_args[0][0]
        insert_params = insert_query.compile().params

        assert str(sample_conversation_id) in str(insert_params)
        assert test_text in str(insert_params)
        assert test_author in str(insert_params)
        assert test_type in str(insert_params)

        # Verify the returned message_id
        assert result == {"message_id": "12345678-1234-5678-1234-************"}


@pytest.mark.asyncio
async def test_insert_conversation_message_default_values(
    mock_session, sample_conversation_id
):
    with patch(
        "services.conversation.get_userdata_db_session", return_value=mock_session
    ), patch(
        "services.conversation.uuid.uuid4",
        return_value=UUID("12345678-1234-5678-1234-************"),
    ):

        result = await insert_conversation_message(
            sample_conversation_id, text="some text"
        )

        # Get the insert query that was executed
        insert_query = mock_session.execute.call_args[0][0]
        insert_params = insert_query.compile().params

        assert "some text" == insert_params.get("text")
        assert "user" == insert_params.get("author")
        assert "answer" == insert_params.get("type")
