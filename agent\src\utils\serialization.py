"""Utility functions for data serialization and logging."""

from typing import Any


def safe_serialize_for_logging(obj: Any) -> Any:
    """Safely serialize objects for JSON logging by converting non-serializable objects to strings."""
    if obj is None or isinstance(obj, (str, int, float, bool)):
        return obj
    elif isinstance(obj, (list, tuple)):
        return [safe_serialize_for_logging(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: safe_serialize_for_logging(value) for key, value in obj.items()}
    elif hasattr(obj, 'model_dump'):  # Pydantic models
        try:
            return obj.model_dump()
        except Exception:
            return str(obj)
    elif hasattr(obj, 'to_dict'):  # Objects with to_dict method
        try:
            return obj.to_dict()
        except Exception:
            return str(obj)
    elif hasattr(obj, '__dict__'):  # General objects with __dict__
        try:
            return safe_serialize_for_logging(obj.__dict__)
        except Exception:
            return str(obj)
    else:
        # For everything else, convert to string representation
        return str(obj)
