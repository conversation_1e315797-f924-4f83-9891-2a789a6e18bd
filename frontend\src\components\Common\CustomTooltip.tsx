import React, { ReactNode, useState, useEffect, useCallback, useRef } from 'react';
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';

interface CustomTooltipProps {
  children: ReactNode;
  content: ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'button' | 'overline' | 'inherit';
  fontSize?: string | number;
  disableOnScroll?: boolean;
  scrollContainer?: string;
  placement?: TooltipProps['placement'];
  arrow?: boolean;
  PopperProps?: TooltipProps['PopperProps'];
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  children,
  content,
  variant = 'caption',
  fontSize = '12px',
  disableOnScroll = false,
  scrollContainer = 'sidenav-infinite-scroll',
  placement = 'right',
  arrow = false,
  PopperProps,
  ...rest
}) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [tooltipKey, setTooltipKey] = useState(0);
  const scrollTimeoutRef = useRef<number | null>(null);
  const isScrollingRef = useRef(false);
  const autoCloseTimeoutRef = useRef<number | null>(null);

  const SCROLL_DEBOUNCE_DELAY = 100;   // wait 100ms after last scroll
  const AUTO_CLOSE_DELAY = 1000;   // 1 second after opening

  const handleScroll = useCallback(() => {
    if (!disableOnScroll) return;

    isScrollingRef.current = true;
    setOpen(false);

    // Force tooltip to unmount instantly by changing key
    setTooltipKey(prev => prev + 1);

    // Clear auto-close timeout if scrolling
    if (autoCloseTimeoutRef.current) {
      window.clearTimeout(autoCloseTimeoutRef.current);
      autoCloseTimeoutRef.current = null;
    }

    if (scrollTimeoutRef.current) {
      window.clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      isScrollingRef.current = false;
    }, SCROLL_DEBOUNCE_DELAY);
  }, [disableOnScroll]);

  useEffect(() => {
    if (!disableOnScroll) return;

    const scrollEl = document.getElementById(scrollContainer);
    const targets: (Window | HTMLElement)[] = [window];
    if (scrollEl) targets.push(scrollEl);

    targets.forEach(t => t.addEventListener('scroll', handleScroll, { passive: true }));
    return () => {
      targets.forEach(t => t.removeEventListener('scroll', handleScroll));
      if (scrollTimeoutRef.current) window.clearTimeout(scrollTimeoutRef.current);
      if (autoCloseTimeoutRef.current) window.clearTimeout(autoCloseTimeoutRef.current);
    };
  }, [disableOnScroll, scrollContainer, handleScroll]);

  const handleTooltipClose = () => {
    setOpen(false);
    if (autoCloseTimeoutRef.current) {
      window.clearTimeout(autoCloseTimeoutRef.current);
      autoCloseTimeoutRef.current = null;
    }
  };

  const handleTooltipOpen = () => {
    if (!isScrollingRef.current) {
      setOpen(true);
      // Clear any previous auto-close timeout
      if (autoCloseTimeoutRef.current) {
        window.clearTimeout(autoCloseTimeoutRef.current);
      }
      // Set auto-close timeout
      autoCloseTimeoutRef.current = window.setTimeout(() => {
        setOpen(false);
        autoCloseTimeoutRef.current = null;
      }, AUTO_CLOSE_DELAY);
    }
  };

  const renderChildren = () => {
    if (React.isValidElement(children)) {
      return React.cloneElement(children);
    }
    return <Box component="span">{children}</Box>;
  };

  return (
    <Tooltip
      key={tooltipKey}
      {...rest}
      open={open}
      onClose={handleTooltipClose}
      onOpen={handleTooltipOpen}
      placement={placement}
      arrow={arrow}
      PopperProps={{
        sx: {
          zIndex: theme.zIndex.tooltip + 100,
          ...PopperProps?.sx
        },
        ...PopperProps
      }}
      slotProps={{
        tooltip: {
          sx: {
            borderRadius: '4px',
            backgroundColor: theme.palette.grey[800],
            border: 'none',
            maxWidth: 200,
          },
        },
      }}
      title={
        <Typography
          variant={variant}
          sx={{
            color: theme.palette.common.white,
            border: 'none',
            whiteSpace: 'normal',
            fontSize: fontSize,
            fontFamily: "Roboto",
          }}
        >
          {content}
        </Typography>
      }
    >
      {renderChildren()}
    </Tooltip>
  );
};

export default CustomTooltip;