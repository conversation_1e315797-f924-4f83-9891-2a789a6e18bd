import { useEffect } from 'react';

interface DynamicMetaProps {
    title: string;
    description: string;
}

const useDynamicMeta = ({ title, description }: DynamicMetaProps) => {
    useEffect(() => {
        const originalTitle = document.title;
        const originalDescription = document.querySelector('meta[name="description"]')?.getAttribute('content');

        document.title = title;

        let metaDescriptionTag = document.querySelector('meta[name="description"]');
        if (!metaDescriptionTag) {
            metaDescriptionTag = document.createElement('meta');
            metaDescriptionTag.setAttribute('name', 'description');
            document.head.appendChild(metaDescriptionTag);
        }
        metaDescriptionTag.setAttribute('content', description);

        return () => {
            document.title = originalTitle;
            if (originalDescription) {
                metaDescriptionTag?.setAttribute('content', originalDescription);
            } else {
                metaDescriptionTag?.remove();
            }
        };
    }, [title, description]);
};

export default useDynamicMeta;