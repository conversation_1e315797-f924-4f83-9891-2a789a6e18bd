import React, { useState } from 'react';
import { Tabs, Tab, Box } from '@mui/material';

const TabsContainer = ({ tabs, renderTabContent }) => {
    const [value, setValue] = useState(0);

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    return (
        <Box>
            <Tabs value={value} onChange={handleChange} aria-label="main tabs">
                {tabs.map((tab, index) => (
                    <Tab key={index} label={tab.label} />
                ))}
            </Tabs>
            <Box mt={2}>
                {renderTabContent(value)}
            </Box>
        </Box>
    );
};

export default TabsContainer;
