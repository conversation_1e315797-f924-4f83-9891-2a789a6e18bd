"use client";

import { ThemeProvider } from "@mui/material/styles";

import theme from "./theme";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import { CssBaseline, Box } from "@mui/material";
import Head from "next/head";
import "./globals.css";
import { MobileProvider } from './components/MobileContext';
import { useMediaQuery } from "@mui/material";
import DynamicPageTitle from './components/DynamicPageTitle';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const isMobile = useMediaQuery("(max-width:600px)");
  const isTablet = useMediaQuery("(max-width:960px)");
  const mainPadding = isMobile ? "16px" : isTablet ? "40px" : "40px";
  return (
    <html lang="en">
      <Head>
        <DynamicPageTitle 
          title="Impact AI - Tranform Evidence into Action" 
          description="ImpactAI is revolutionizing global development and empowering development practitioners with a GenAI-powered tool, delivering causal research insights in seconds."
        />
        <meta name="description" content="Welcome to My App" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <body>
        <MobileProvider>
          <ThemeProvider theme={theme}>
            <CssBaseline />
            <Navbar />
            <main style={{ width: '100%', minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ flex: 1, display: "flex", flexDirection: "column", padding: mainPadding }}>
                {children}
              </Box>
              <Footer />
            </main>
          </ThemeProvider>
        </MobileProvider>
      </body>
    </html>
  );
}