#!/bin/bash

# Source logging if not already loaded
if ! declare -f log_info >/dev/null 2>&1; then
    source "$(dirname "${BASH_SOURCE[0]}")/logging.sh"
fi

# Function to check and set container engine
check_container_engine() {
    log_info "Checking available container engines..."
    
    if command -v docker &> /dev/null; then
        CONTAINER_ENGINE="docker"
        log_success "Using Docker as container engine"
    elif command -v podman &> /dev/null; then
        CONTAINER_ENGINE="podman"
        log_success "Using <PERSON><PERSON> as container engine"
    else
        log_error "Neither <PERSON><PERSON> nor <PERSON><PERSON> is installed"
        log_info "Please install <PERSON><PERSON> or <PERSON><PERSON> to proceed"
        exit 1
    fi
    
    export CONTAINER_ENGINE
} 