import React from "react";
import { Paper, Box, Typography } from "@mui/material";
import Image from "next/image";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const Section2 = () => {
    const theme = useTheme();
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: isMobile ? "5px" : isTablet ? "40px" : "77px",
            }}
        >
            {/* Header Section */}
            <Box sx={{ textAlign: "center" }}>
                <Typography variant={isMobile ? "h3" : isTablet ? "h2" : "h2"}>
                    Why ImpactAI?
                </Typography>
                <Typography
                    variant={isMobile ? "body2" : isTablet ? "body2" : "body1"}
                    sx={{
                        width: isMobile ? "100%" : isTablet ? "100%" : "439px",
                        margin: 'auto',
                        textAlign: "center",
                        color: theme.palette.text.secondary,
                        fontSize: isMobile ? "12px" : isTablet ? "14px" : "16px"
                    }}
                >
                    Normalize insights to identify impact. We are on a mission to scale the adoption of causal evidence and empower the development community.
                </Typography>
            </Box>

            {/* Main Image Section */}
            <Box
                sx={{
                    position: "relative",
                    width: "100%",
                    height: isMobile ? "124px" : isTablet ? "200px" : "389px",
                    borderRadius: "32px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    mt: "0px",
                }}
            >
                <Image
                    src="/images/home/<USER>"
                    alt="Family carrying water in a rural town"
                    fill={true}
                    style={{ borderRadius: "16px", objectFit: 'contain' }}
                />
            </Box>
        </Paper>
    );
};

export default Section2;
