import pytest
from unittest.mock import Mock, patch
import uuid
from services.cache import (
    Cache,
    create_cache_key,
    initialize_redis_connection_pool,
    destroy_redis_connection_pool,
    get_redis_connection,
    get_redis_pubsub,
)


@pytest.fixture
def mock_redis():
    with patch("services.cache.Redis") as mock:
        redis_instance = Mock()
        mock.return_value = redis_instance
        yield redis_instance


@pytest.fixture
def cache(mock_redis):
    return Cache()


def test_create_cache_key():
    prefix = "test"
    text = "sample_text"
    expected_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, text)
    expected_key = f"{prefix}:{expected_uuid}"

    assert create_cache_key(prefix, text) == expected_key


def test_initialize_redis_connection_pool():
    with patch("services.cache.ConnectionPool") as mock_pool:
        initialize_redis_connection_pool()
        mock_pool.assert_called_once_with(
            host="redis", port=6379, decode_responses=True
        )


def test_destroy_redis_connection_pool():
    with patch("services.cache._connection_pool") as mock_pool:
        destroy_redis_connection_pool()
        mock_pool.disconnect.assert_called_once()


def test_get_redis_connection():
    with patch("services.cache._connection_pool", None), patch(
        "services.cache.initialize_redis_connection_pool"
    ) as mock_init, patch("services.cache.Redis") as mock_redis:

        get_redis_connection()
        mock_init.assert_called_once()
        mock_redis.assert_called_once()


def test_get_redis_pubsub(mock_redis):
    with patch("services.cache.get_redis_connection", return_value=mock_redis):
        get_redis_pubsub()
        mock_redis.pubsub.assert_called_once()


def test_cache_clear(cache, mock_redis):
    cache.clear()
    mock_redis.flushall.assert_called_once()


def test_cache_has_key(cache, mock_redis):
    mock_redis.exists.return_value = 1
    assert cache.has_key("test_key") is True

    mock_redis.exists.return_value = 0
    assert cache.has_key("test_key") is False

    mock_redis.exists.side_effect = Exception("Redis error")
    assert cache.has_key("test_key") is False


def test_cache_get(cache, mock_redis):
    mock_redis.get.return_value = "42"
    assert cache.get("test_key") == "42"

    mock_redis.get.return_value = None
    assert cache.get("test_key") is None


def test_cache_increment(cache, mock_redis):
    mock_redis.incr.return_value = 1
    assert cache.increment("test_key") == 1
    mock_redis.incr.assert_called_once_with("test_key")


def test_cache_set(cache, mock_redis):
    cache.set("test_key", 42, 3600)
    mock_redis.set.assert_called_once_with("test_key", 42, ex=3600)

    # Test with default expiration
    cache.set("test_key", 42)
    mock_redis.set.assert_called_with("test_key", 42, ex=86400)


def test_cache_get_json(cache, mock_redis):
    # Test valid JSON
    valid_json = '{"key": "value"}'
    mock_redis.get.return_value = valid_json
    assert cache.get_json("test_key") == {"key": "value"}

    # Test non-existent key
    mock_redis.get.return_value = None
    assert cache.get_json("test_key") is None

    # Test invalid JSON
    mock_redis.get.return_value = "invalid json"
    assert cache.get_json("test_key") is None
