"""LLM client for generating text."""

import asyncio
import logging
import os
import pathlib
from typing import Any, Dict, List, Optional, Type

import aiohttp
import dotenv
import jinja2
from google import genai
from google.genai import types
from pydantic import BaseModel
from tqdm import tqdm

dotenv.load_dotenv()

logger = logging.getLogger(__name__)


class LLMResponse(BaseModel):
    """Response from LLM."""

    text: str
    metadata: Dict[str, Any] = {}
    parsed: Optional[Any] = None  # For structured output


class LLMClient:
    """Client for interacting with Gemini LLM."""

    def __init__(self, model_name: str = "gemini-2.0-flash-001"):
        """Initialize LLM client."""
        # Validate API key
        if not os.getenv("PROJECT_NAME"):
            raise ValueError("PROJECT_NAME environment variable must be set")

        if not os.getenv("PROJECT_LOCATION"):
            raise ValueError("PROJECT_LOCATION environment variable must be set")

        # Initialize Gemini client
        self.client = genai.Client(
            vertexai=True,
            project=os.getenv("PROJECT_NAME"),
            location=os.getenv("PROJECT_LOCATION"),
        )
        
        self.model_name = model_name

        # Initialize Jinja2 environment for loading prompts
        self.prompt_env = self._setup_prompt_environment()

    def _setup_prompt_environment(self) -> jinja2.Environment:
        """Set up the Jinja2 environment for loading prompts."""
        # Determine the base path for prompt templates
        base_dir = pathlib.Path(__file__).parent.parent.parent / "config" / "prompts"

        if not base_dir.exists():
            logger.warning(
                f"Prompt directory not found at {base_dir}. Using current directory."
            )
            base_dir = pathlib.Path(".")

        # Create a file system loader for the templates
        loader = jinja2.FileSystemLoader(base_dir)
        env = jinja2.Environment(loader=loader, autoescape=False)
        return env

    def load_prompt(self, template_name: str, **kwargs) -> str:
        """Load and render a prompt template with the given variables."""
        try:
            # Append .prompt if not already present
            if not template_name.endswith(".prompt"):
                template_name = f"{template_name}.prompt"

            template = self.prompt_env.get_template(template_name)
            rendered_prompt = template.render(**kwargs)
            return rendered_prompt
        except Exception as e:
            logger.error(f"Error loading prompt {template_name}: {e}")
            # Return a basic prompt if template loading fails
            return f"Please answer this query: {kwargs.get('user_query', 'No query provided')}"

    async def generate(
        self,
        prompt: str,
        max_tokens: int = 8192,
        temperature: float = 0.7,
        response_format: Optional[Type[BaseModel]] = None,
    ) -> LLMResponse:
        """Generate text using the configured LLM."""
        try:
            # Create generation config
            config = types.GenerateContentConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            )

            # Configure structured output if requested
            if response_format:
                config.response_mime_type = "application/json"
                config.response_schema = response_format

            # Create content
            contents = [
                types.Content(role="user", parts=[types.Part.from_text(text=prompt)])
            ]

            # Make the request
            response = self.client.models.generate_content(
                model=self.model_name, config=config, contents=contents
            )

            # Return structured response
            return LLMResponse(
                text=response.text,
                metadata={
                    "prompt_tokens": getattr(
                        response.usage_metadata, "prompt_token_count", 0
                    ),
                    "completion_tokens": getattr(
                        response.usage_metadata, "candidates_token_count", 0
                    ),
                },
                parsed=(
                    response.parsed
                    if response_format and hasattr(response, "parsed")
                    else None
                ),
            )

        except Exception as e:
            logger.error(f"Error generating text: {e}")
            raise

    async def generate_structured_output(
        self,
        prompt: str,
        response_model: Type[BaseModel],
        max_tokens: int = 8192,
        temperature: float = 0.2,
    ) -> BaseModel:
        """Generate a structured output using a Pydantic model."""
        response = await self.generate(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            response_format=response_model,
        )

        if response.parsed:
            return response.parsed, response.metadata
        else:
            raise ValueError("No structured output was returned from the model")


class LLMJudgeResult(BaseModel):
    """Result from LLM Judge evaluation."""

    user_query: str
    scientific_rigor_score: Optional[int] = (
        None  # Can be an integer (0-5) or None (null)
    )
    scientific_rigor_justification: str  # Always required, even when score is null
    source_attribution_score: Optional[int] = None
    source_attribution_justification: str
    contextual_relevance_score: Optional[int] = None
    contextual_relevance_justification: str
    response_reasoning_score: Optional[int] = None
    response_reasoning_justification: str
    clarity_of_language_score: Optional[int] = None
    clarity_of_language_justification: str
    structure_of_response_score: Optional[int] = None
    structure_of_response_justification: str
    transparency_and_confidence_calibration_score: Optional[int] = None
    transparency_and_confidence_calibration_justification: str
    perceived_credibility_score: Optional[int] = None
    perceived_credibility_justification: str
    actionability_score: Optional[int] = None
    actionability_justification: str
    adaptability_score: Optional[int] = None
    adaptability_justification: str
    further_exploration_score: Optional[int] = None
    further_exploration_justification: str

    @property
    def average_score(self) -> float:
        """Calculate the average score across all dimensions."""
        scores = []
        # Only include non-None scores in the calculation
        if self.scientific_rigor is not None:
            scores.append(self.scientific_rigor)

        # Return 0 if no scores are available
        if not scores:
            return 0.0

        return sum(scores) / len(scores)


class LLMJudge(LLMClient):
    """LLM Judge for evaluating agent responses."""

    def __init__(self, project_id: str, location: str, model_name: str, session=None):
        """Initialize LLM Judge."""
        super().__init__(project_id, location, model_name)
        self.logger = logging.getLogger(__name__)
        self.session = session
        self.owns_session = False

        if self.session is None:
            self.session = aiohttp.ClientSession()
            self.owns_session = True

    async def cleanup(self):
        """Clean up resources."""
        if self.owns_session and self.session is not None:
            await self.session.close()
            self.session = None

    async def evaluate_response(
        self, user_query: str, final_answer: str, llm_output: Dict[str, Any]
    ) -> LLMJudgeResult:
        """
        Evaluate an agent response using the evaluation prompt.

        Args:
            user_query: The original user query
            final_answer: The agent's final answer
            llm_output: The last LLM output with the prompt

        Returns:
            LLMJudgeResult with evaluation scores
        """
        # Load the evaluation prompt
        prompt = self.load_prompt(
            "evaluate",
            request=user_query,
            data=llm_output.get("prompt", "No prompt data available"),
            answer=final_answer,
        )
        logger.info(f"Evaluation prompt for user query: {user_query}")

        # Generate structured evaluation
        try:
            result = await self.generate_structured_output(
                prompt, max_tokens=8192, temperature=0.1
            )

            # Convert to LLMJudgeResult
            return LLMJudgeResult(
                user_query=user_query,
                scientific_rigor_score=result.get("scientific_rigor_score", None),
                scientific_rigor_justification=result.get(
                    "scientific_rigor_justification", "No justification provided"
                ),
                source_attribution_score=result.get("source_attribution_score", None),
                source_attribution_justification=result.get(
                    "source_attribution_justification", "No justification provided"
                ),
                contextual_relevance_score=result.get(
                    "contextual_relevance_score", None
                ),
                contextual_relevance_justification=result.get(
                    "contextual_relevance_justification", "No justification provided"
                ),
                response_reasoning_score=result.get("response_reasoning_score", None),
                response_reasoning_justification=result.get(
                    "response_reasoning_justification", "No justification provided"
                ),
                clarity_of_language_score=result.get("clarity_of_language_score", None),
                clarity_of_language_justification=result.get(
                    "clarity_of_language_justification", "No justification provided"
                ),
                structure_of_response_score=result.get(
                    "structure_of_response_score", None
                ),
                structure_of_response_justification=result.get(
                    "structure_of_response_justification", "No justification provided"
                ),
                transparency_and_confidence_calibration_score=result.get(
                    "transparency_and_confidence_calibration_score", None
                ),
                transparency_and_confidence_calibration_justification=result.get(
                    "transparency_and_confidence_calibration_justification",
                    "No justification provided",
                ),
                perceived_credibility_score=result.get(
                    "perceived_credibility_score", None
                ),
                perceived_credibility_justification=result.get(
                    "perceived_credibility_justification", "No justification provided"
                ),
                actionability_score=result.get("actionability_score", None),
                actionability_justification=result.get(
                    "actionability_justification", "No justification provided"
                ),
                adaptability_score=result.get("adaptability_score", None),
                adaptability_justification=result.get(
                    "adaptability_justification", "No justification provided"
                ),
                further_exploration_score=result.get("further_exploration_score", None),
                further_exploration_justification=result.get(
                    "further_exploration_justification", "No justification provided"
                ),
            )
        except Exception as e:
            self.logger.error(f"Error evaluating response: {e}")
            # Return default result on error
            return LLMJudgeResult(
                user_query=user_query,
                scientific_rigor_score=None,
                scientific_rigor_justification=f"Evaluation failed: {str(e)}",
                source_attribution_score=None,
                source_attribution_justification=f"Evaluation failed: {str(e)}",
                contextual_relevance_score=None,
                contextual_relevance_justification=f"Evaluation failed: {str(e)}",
                response_reasoning_score=None,
                response_reasoning_justification=f"Evaluation failed: {str(e)}",
                clarity_of_language_score=None,
                clarity_of_language_justification=f"Evaluation failed: {str(e)}",
                structure_of_response_score=None,
                structure_of_response_justification=f"Evaluation failed: {str(e)}",
                transparency_and_confidence_calibration_score=None,
                transparency_and_confidence_calibration_justification=f"Evaluation failed: {str(e)}",
                perceived_credibility_score=None,
                perceived_credibility_justification=f"Evaluation failed: {str(e)}",
                actionability_score=None,
                actionability_justification=f"Evaluation failed: {str(e)}",
                adaptability_score=None,
                adaptability_justification=f"Evaluation failed: {str(e)}",
                further_exploration_score=None,
                further_exploration_justification=f"Evaluation failed: {str(e)}",
            )

    async def batch_evaluate(
        self, results: List[Dict[str, Any]], max_concurrent: int = 5
    ) -> List[LLMJudgeResult]:
        """
        Evaluate multiple agent responses in parallel.

        Args:
            results: List of agent results to evaluate
            max_concurrent: Maximum number of concurrent evaluations

        Returns:
            List of LLMJudgeResult objects
        """
        evaluations = []
        semaphore = asyncio.Semaphore(max_concurrent)

        async def evaluate_single(result):
            async with semaphore:
                user_query = result.get("user_query", "")
                final_answer = result.get("final_answer", "")

                # Get the last LLM output with a prompt
                llm_outputs = result.get("llm_outputs", [])
                last_output = next(
                    (out for out in reversed(llm_outputs) if out.get("prompt")), {}
                )

                return await self.evaluate_response(
                    user_query, final_answer, last_output
                )

        # Create tasks for all evaluations
        tasks = []
        for result in results:
            if result.get("success", True) and result.get("final_answer"):
                tasks.append(asyncio.create_task(evaluate_single(result)))

        # Wait for all tasks to complete with progress bar
        for task in tqdm(
            asyncio.as_completed(tasks), total=len(tasks), desc="Evaluating responses"
        ):
            evaluation = await task
            evaluations.append(evaluation)

        return evaluations

    @staticmethod
    def compute_aggregate_metrics(evaluations: List[LLMJudgeResult]) -> Dict[str, Any]:
        """
        Compute simple aggregate metrics from a list of evaluations.

        Args:
            evaluations: List of evaluation results

        Returns:
            Dictionary with basic aggregate metrics
        """
        total_queries = len(evaluations)
        valid_scientific_rigor_queries = len(
            [
                x.scientific_rigor_score
                for x in evaluations
                if x.scientific_rigor_score is not None
            ]
        )
        valid_source_attribution_queries = len(
            [
                x.source_attribution_score
                for x in evaluations
                if x.source_attribution_score is not None
            ]
        )
        valid_contextual_relevance_queries = len(
            [
                x.contextual_relevance_score
                for x in evaluations
                if x.contextual_relevance_score is not None
            ]
        )
        valid_response_reasoning_queries = len(
            [
                x.response_reasoning_score
                for x in evaluations
                if x.response_reasoning_score is not None
            ]
        )
        valid_clarity_of_language_queries = len(
            [
                x.clarity_of_language_score
                for x in evaluations
                if x.clarity_of_language_score is not None
            ]
        )
        valid_structure_of_response_queries = len(
            [
                x.structure_of_response_score
                for x in evaluations
                if x.structure_of_response_score is not None
            ]
        )
        valid_transparency_and_confidence_calibration_queries = len(
            [
                x.transparency_and_confidence_calibration_score
                for x in evaluations
                if x.transparency_and_confidence_calibration_score is not None
            ]
        )
        valid_perceived_credibility_queries = len(
            [
                x.perceived_credibility_score
                for x in evaluations
                if x.perceived_credibility_score is not None
            ]
        )
        valid_actionability_queries = len(
            [
                x.actionability_score
                for x in evaluations
                if x.actionability_score is not None
            ]
        )
        valid_adaptability_queries = len(
            [
                x.adaptability_score
                for x in evaluations
                if x.adaptability_score is not None
            ]
        )
        valid_further_exploration_queries = len(
            [
                x.further_exploration_score
                for x in evaluations
                if x.further_exploration_score is not None
            ]
        )

        average_scientific_rigor_score = (
            sum(
                [
                    x.scientific_rigor_score
                    for x in evaluations
                    if x.scientific_rigor_score is not None
                ]
            )
            / valid_scientific_rigor_queries
        )
        average_source_attribution_score = (
            sum(
                [
                    x.source_attribution_score
                    for x in evaluations
                    if x.source_attribution_score is not None
                ]
            )
            / valid_source_attribution_queries
        )
        average_contextual_relevance_score = (
            sum(
                [
                    x.contextual_relevance_score
                    for x in evaluations
                    if x.contextual_relevance_score is not None
                ]
            )
            / valid_contextual_relevance_queries
        )
        average_response_reasoning_score = (
            sum(
                [
                    x.response_reasoning_score
                    for x in evaluations
                    if x.response_reasoning_score is not None
                ]
            )
            / valid_response_reasoning_queries
        )
        average_clarity_of_language_score = (
            sum(
                [
                    x.clarity_of_language_score
                    for x in evaluations
                    if x.clarity_of_language_score is not None
                ]
            )
            / valid_clarity_of_language_queries
        )
        average_structure_of_response_score = (
            sum(
                [
                    x.structure_of_response_score
                    for x in evaluations
                    if x.structure_of_response_score is not None
                ]
            )
            / valid_structure_of_response_queries
        )
        average_transparency_and_confidence_calibration_score = (
            sum(
                [
                    x.transparency_and_confidence_calibration_score
                    for x in evaluations
                    if x.transparency_and_confidence_calibration_score is not None
                ]
            )
            / valid_transparency_and_confidence_calibration_queries
        )
        average_perceived_credibility_score = (
            sum(
                [
                    x.perceived_credibility_score
                    for x in evaluations
                    if x.perceived_credibility_score is not None
                ]
            )
            / valid_perceived_credibility_queries
        )
        average_actionability_score = (
            sum(
                [
                    x.actionability_score
                    for x in evaluations
                    if x.actionability_score is not None
                ]
            )
            / valid_actionability_queries
        )
        average_adaptability_score = (
            sum(
                [
                    x.adaptability_score
                    for x in evaluations
                    if x.adaptability_score is not None
                ]
            )
            / valid_adaptability_queries
        )
        average_further_exploration_score = (
            sum(
                [
                    x.further_exploration_score
                    for x in evaluations
                    if x.further_exploration_score is not None
                ]
            )
            / valid_further_exploration_queries
        )

        average_content_score = (
            average_scientific_rigor_score
            + average_source_attribution_score
            + average_contextual_relevance_score
            + average_response_reasoning_score
        ) / 4
        average_form_score = (
            average_clarity_of_language_score + average_structure_of_response_score
        ) / 2
        average_trust_score = (
            average_transparency_and_confidence_calibration_score
            + average_perceived_credibility_score
        ) / 2
        average_usefulness_score = (
            average_actionability_score
            + average_adaptability_score
            + average_further_exploration_score
        ) / 3

        return {
            "total_queries": total_queries,
            "valid_scientific_rigor_queries": valid_scientific_rigor_queries,
            "average_scientific_rigor_score": round(average_scientific_rigor_score, 2),
            "valid_source_attribution_queries": valid_source_attribution_queries,
            "average_source_attribution_score": round(
                average_source_attribution_score, 2
            ),
            "valid_contextual_relevance_queries": valid_contextual_relevance_queries,
            "average_contextual_relevance_score": round(
                average_contextual_relevance_score, 2
            ),
            "valid_response_reasoning_queries": valid_response_reasoning_queries,
            "average_response_reasoning_score": round(
                average_response_reasoning_score, 2
            ),
            "valid_clarity_of_language_queries": valid_clarity_of_language_queries,
            "average_clarity_of_language_score": round(
                average_clarity_of_language_score, 2
            ),
            "valid_structure_of_response_queries": valid_structure_of_response_queries,
            "average_structure_of_response_score": round(
                average_structure_of_response_score, 2
            ),
            "valid_transparency_and_confidence_calibration_queries": valid_transparency_and_confidence_calibration_queries,
            "average_transparency_and_confidence_calibration_score": round(
                average_transparency_and_confidence_calibration_score, 2
            ),
            "valid_perceived_credibility_queries": valid_perceived_credibility_queries,
            "average_perceived_credibility_score": round(
                average_perceived_credibility_score, 2
            ),
            "valid_actionability_queries": valid_actionability_queries,
            "average_actionability_score": round(average_actionability_score, 2),
            "valid_adaptability_queries": valid_adaptability_queries,
            "average_adaptability_score": round(average_adaptability_score, 2),
            "valid_further_exploration_queries": valid_further_exploration_queries,
            "average_further_exploration_score": round(
                average_further_exploration_score, 2
            ),
            "average_content_score": round(average_content_score, 2),
            "average_form_score": round(average_form_score, 2),
            "average_trust_score": round(average_trust_score, 2),
            "average_usefulness_score": round(average_usefulness_score, 2),
        }
