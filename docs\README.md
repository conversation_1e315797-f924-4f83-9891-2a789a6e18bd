# ImpactAI Documentation

Welcome to the ImpactAI project documentation. This repository contains a comprehensive AI-powered platform for development economics research analysis and insights.

## Overview

ImpactAI is a specialized platform designed for development practitioners, policymakers, researchers, and international organizations. It provides research-backed insights using customized large language models (LLMs) and a structured knowledge database of validated research studies.

### Key Features

- AI-powered research analysis
- Interactive visualization of policy interventions
- Curated knowledge database of validated research
- Structured information on policy interventions and outcomes
- Quality-assessed research findings
- Bias mitigation through structured prompting

### Repository Structure

The repository is organized into several main components:

- [`backend/`](./backend.md) - Main application backend API
- [`agent/`](../docs/agent/README.md) - AI agent for processing and analyzing research papers
- [`frontend/`](./frontend.md) - Application frontend interface
- [`website/`](./website.md) - Landing page and marketing website
- [`infrastructure/`](./infrastructure.md) - Deployment and infrastructure configuration
- [`proxy/`](./proxy.md) - Proxy service configuration

## Getting Started

1. Install Docker following the [official installation guide](https://docs.docker.com/engine/install/)

2. Clone the repository:
   ```bash
   git clone https://github.com/worldbank/causal-ai-product.git
   cd causal-ai-product
   ```

3. Start the development environment using Makefile commands (no YAML or nginx.conf files required):

- If you want to develop for the frontend
   ```bash
   make up
   ```
   You can also just `cd frontend` and `npm run dev` to start frontend development without docker

- If you want to develop for the backend
   ```bash
   make up-backend
   ```

- If you want to develop for the agent
   ```bash
   make up-agent
   ```

### Access Points

The application provides the following services (defined in `.honcho.yml`):

- **Frontend**: Web application interface
- **Backend**: API server with documentation
- **Agent**: AI processing service
- **Caddy**: Reverse proxy for domain routing

For local development URLs, see the service configuration in `services.yml` and the service map in `infrastructure/service-map.sh`.

To stop the development environment:
```bash
make down
```

## Deployment

All deployments are managed via Makefile commands and shell scripts. There are no YAML or nginx.conf files required for deployment. Environment configuration is handled via env files and Cloud Secret Manager.

- Backend Deployment:
  ```bash
  make deploy-backend
  ```
- Frontend Deployment:
  ```bash
  make deploy-frontend
  ```
- Website Deployment:
  ```bash
  make deploy-website
  ```
- Agent Deployment:
  ```bash
  make deploy-agent
  ```

## Additional Documentation

- [Backend Documentation](./backend.md)
- [Agent Documentation](../docs/agent/README.md)
- [Frontend Documentation](./frontend.md)
- [Website Documentation](./website.md)
- [Infrastructure Documentation](./infrastructure.md) - Includes database migration management with Alembic
- [Architecture Overview](./architecture.md)
- [Development Guide](./development.md)

## Platform Capabilities

### Research Source Information

ImpactAI provides rich research source data to support evidence-based decision making for development practitioners and policymakers.

#### Core Features

**Paper Abstract Integration**
- **Full Abstract Retrieval**: Research paper abstracts are fetched directly from the database and included in API responses
- **Complete Context**: Users receive full abstract text for immediate research assessment
- **Research Validation**: Researchers can assess paper relevance and methodology before diving into full citations

### Advanced Text Processing
ImpactAI includes sophisticated text processing capabilities for clean, contextual research summaries:
- **Intelligent Sanitization**: Automated text cleaning and formatting for research content
- **Data-Driven Filtering**: Intervention-outcome pairs validated against available data
- **Context Awareness**: Removes irrelevant references when no supporting data is available
- **Research Integrity**: Ensures only validated citations appear in final summaries
- **Source Citation Filtering**: Validates source references like `[A123]`, `[P456]` against paper datasets
- **Multi-Layer Processing**: Comprehensive text cleaning through integrated processing pipeline
- **Professional Presentation**: Clean, formatted text optimized for development practitioners

### Integration Benefits
The enhanced text processing system provides:
- **Citation Integrity**: Only sources with supporting data appear in summaries
- **Clean Presentation**: Automated removal of broken or unsupported references
- **Consistent Formatting**: Standardized text presentation across all responses
- **Evidence-Based Research**: All citations backed by actual research data
- **Professional Quality**: Publication-ready text suitable for policy documents

#### Text Processing Capabilities

**Plot Reference Management**
- **Data-Driven Filtering**: Intervention-outcome pairs `[intervention=X, outcome=Y]` are validated against available data
- **Context Awareness**: Automatically removes all plot references when no supporting data is available
- **Format Standardization**: Converts various text formats to consistent, readable output

**Research Summary Enhancement**
- **Effect Size Formatting**: Converts parenthetical effect sizes `(0.5)` to clean format `0.5`
- **Invalid Reference Removal**: Removes incomplete bracket references and unsupported claims
- **Evidence Validation**: Maintains only meaningful research citations that match underlying dataset

#### Support for Development Research

These capabilities directly support ImpactAI's mission to serve development practitioners:

1. **Research Discovery**: Abstracts provide immediate context about study methodology and findings
2. **Informed Decision Making**: Quality scores help prioritize high-reliability research
3. **Time Efficiency**: Users can quickly assess paper relevance without external lookups
4. **Evidence Quality**: Comprehensive metadata supports proper research validation workflows
5. **Clean Communication**: Sanitized text ensures clear, professional research summaries
6. **Data Integrity**: Filtered content maintains research credibility and accuracy

#### Technical Architecture

**Database Performance**
- High-performance connection pooling (20 concurrent connections)
- Optimized query performance with compiled caching
- Reliable connection health monitoring

**API Services**
- `get_paper_abstracts_by_ids()` service for efficient abstract retrieval
- Conversation endpoints with comprehensive source metadata
- Robust error handling and session management

#### Developer Documentation

For technical implementation details, see:
- [Backend Service Documentation](../backend/README.md)
- [Infrastructure Documentation](./infrastructure.md)
- [API Documentation](../backend/README.md#api-documentation) (when running locally)

The platform provides development researchers with the rich, contextual information they need to make evidence-based decisions that can impact real communities and development outcomes.
