"""Model classes for the agent module."""

import json
import logging
import os
import re
import regex
from typing import Any, Dict, List, Optional
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, model_validator
from datetime import datetime


from src.services.cache import Cache
from src.utils.serialization import safe_serialize_for_logging

logger = logging.getLogger(__name__)


class ActionModel(BaseModel):
    """Model for agent actions."""

    name: str
    reason: str





# --- Intents enum (values match what the model must emit) ---
class Intent(str, Enum):
    greeting_onboarding = "greeting_onboarding"
    causal_impact = "causal impact"
    comparative = "comparative"
    generalizability = "generalizability"
    theory_of_change = "theory of change"
    implementation_details = "implementation details"
    descriptive = "descriptive"
    methodology_inquiry = "methodology_inquiry"
    jargon_clarification = "jargon clarification"
    ambiguous = "ambiguous"
    out_of_scope = "out of scope"

# --- Action payload (no unexpected keys) ---
class Action(BaseModel):
    model_config = ConfigDict(extra="forbid")
    name: str
    reason: str
    # args allows arbitrary keys (additionalProperties: true)
    args: Dict[str, Any] = Field(default_factory=dict)

# --- Single-envelope response (no unexpected top-level keys) ---
class AgentResponse(BaseModel):
    model_config = ConfigDict(extra="forbid")
    intent: Intent
    follow_up_question: bool
    thought: Optional[str]
    action: Optional[Action] = None
    user_query: Optional[str] = None
    answer: Optional[str] = None

    
    @model_validator(mode="after")
    def _xor_action_answer(self):
        has_action = self.action is not None
        has_answer = self.answer is not None
        has_thought = self.thought is not None

        if has_action == has_answer:  # both True or both False
            raise ValueError("Exactly one of 'action' or 'answer' must be present.")

        if has_answer and not has_thought:
            raise ValueError("'thought' must be present when 'answer' is provided.")

        return self
    
    @classmethod
    def from_llm_response(cls, response_text: str) -> "AgentResponse":
        try:
            return cls.model_validate_json(response_text)
        except Exception:
            # Legacy fenced JSON
            m = re.search(r"```json\s*(.*?)\s*```", response_text, re.DOTALL)
            if not m:
                # Last-chance: recursive match for nested JSON
                mm = regex.search(r"\{(?:[^{}]|(?R))*\}", response_text, regex.DOTALL)
                if not mm:
                    raise ValueError("No JSON found in LLM response")
                payload = mm.group(0).strip()
            else:
                payload = m.group(1).strip()

            try:
                data = json.loads(payload)
            except Exception as e:
                # Make the invalid-JSON test pass with the expected wording
                raise ValueError(f"Error parsing LLM response: {e}") from e

            if isinstance(data.get("action"), dict) and "input" in data["action"]:
                data["action"].pop("input", None)

            return cls(**data)


class HistoryEntry(BaseModel):
    """Entry in the agent's history."""

    intent: str
    follow_up_question: bool
    thought: str
    action: Optional[ActionModel] = None  # Updated to use ActionModel
    observation: Optional[str] = None
    answer: Optional[str] = None
    iteration: Optional[int] = None


class LLMOutput(BaseModel):
    """Track LLM outputs for each iteration."""

    iteration: int
    prompt: str
    response: str
    intent: str
    follow_up_question: bool
    parsed_response: Optional[AgentResponse] = None
    thinking_time_seconds: Optional[float] = None  # Time for LLM to generate response
    action_time_seconds: Optional[float] = (
        None  # Time for executing the pipeline action
    )
    input_tokens: Optional[int] = None  # Number of tokens in the prompt
    output_tokens: Optional[int] = None  # Number of tokens in the response

    def to_dict(self) -> Dict[str, Any]:
        """Convert LLMOutput to a dictionary."""
        return {
            "iteration": self.iteration,
            "prompt": self.prompt,
            "intent": self.intent,
            "follow_up_question": self.follow_up_question,
            "thought": (
                self.parsed_response.thought
                if self.parsed_response
                else "Failed to parse response"
            ),
            "action": self.parsed_response.action if self.parsed_response else None,
            "answer": self.parsed_response.answer if self.parsed_response else None,
            "thinking_time_seconds": self.thinking_time_seconds,
            "action_time_seconds": self.action_time_seconds,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens,
        }


class ConversationEntry(BaseModel):
    """Entry in conversation history."""

    user: str
    agent: Optional[str] = None


class AgentCache:
    """Cache for agent history and conversation data."""

    def __init__(self):
        """Initialize the cache."""
        self.redis_enabled = os.getenv("REDIS_CACHE_ENABLED", "false").lower() == "true"
        self.history: Dict[str, List[HistoryEntry]] = {}
        self.conversation_history: Dict[str, List[ConversationEntry]] = {}
        # In-memory fallback for execution steps when Redis is disabled/unavailable
        self.execution_steps: Dict[str, List[Dict[str, Any]]] = {}

        if self.redis_enabled:
            try:
                self.redis_cache = Cache()
            except Exception as e:
                self.redis_cache = None
                self.redis_enabled = False
                logger.warning(f"Redis cache is not enabled: {e}")
        else:
            self.redis_cache = None

    def get_or_init_history(self, key: str) -> List[HistoryEntry]:
        """Get the cached history.

        Filters out entries without answers (failed executions).
        """
        if self.redis_enabled and self.redis_cache:
            try:
                cached_data = self.redis_cache.get_json(f"agent_history:{key}")
                if cached_data:
                    all_entries = [HistoryEntry(**item) for item in cached_data]
                    return [entry for entry in all_entries if entry.answer is not None]
            except Exception as e:
                logger.error(f"Error getting history: {e}")

        if key not in self.history:
            self.history[key] = []
        return [entry for entry in self.history[key] if entry.answer is not None]

    def update_history(self, key: str, value: List[HistoryEntry]) -> None:
        """Set the cached history."""
        self.history[key] = value

        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = [item.model_dump() for item in value]
                self.redis_cache.store_json(f"agent_history:{key}", serialized_data)
            except Exception as e:
                logger.error(f"Error updating history: {e}")

    def get_or_init_conversation_history(self, key: str) -> List[ConversationEntry]:
        """Get the cached conversation history.

        Filters out entries without agent responses (failed executions).
        """
        if self.redis_enabled and self.redis_cache:
            try:
                cached_data = self.redis_cache.get_json(f"agent_conversation:{key}")
                if cached_data:
                    all_entries = [ConversationEntry(**item) for item in cached_data]
                    return [entry for entry in all_entries if entry.agent is not None]
            except Exception as e:
                logger.error(f"Error getting conversation history: {e}")

        if key not in self.conversation_history:
            self.conversation_history[key] = []
        return [entry for entry in self.conversation_history[key] if entry.agent is not None]

    def update_conversation_history(
        self, key: str, value: List[ConversationEntry]
    ) -> None:
        """Set the cached conversation history."""
        self.conversation_history[key] = value

        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = [item.model_dump() for item in value]
                self.redis_cache.store_json(f"agent_conversation:{key}", serialized_data)
            except Exception as e:
                logger.error(f"Error updating conversation history: {e}")

    def clear_cache(self, key: str) -> None:
        """Clear cache for a specific conversation."""
        if key in self.history:
            del self.history[key]
        if key in self.conversation_history:
            del self.conversation_history[key]

        if self.redis_enabled and self.redis_cache:
            try:
                self.redis_cache.store_json(f"agent_history:{key}", [])
                self.redis_cache.store_json(f"agent_conversation:{key}", [])
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")

    def clear_all_caches(self) -> None:
        """Clear all cached data."""
        self.history.clear()
        self.conversation_history.clear()

        if self.redis_enabled and self.redis_cache:
            try:
                history_keys = self.redis_cache.get_all_by_prefix("agent_history:")
                conversation_keys = self.redis_cache.get_all_by_prefix("agent_conversation:")

                for key in history_keys + conversation_keys:
                    self.redis_cache.store_json(key, [])
            except Exception as e:
                logger.error(f"Error clearing all caches: {e}")

    def log_tool_execution(
        self, conversation_id: str, tool_name: str, status: str, data: Dict[str, Any] = None
    ) -> None:
        """Log tool execution step with standardized format using Redis RPUSH for atomic operations."""
        step_data = {
            'tool_name': tool_name,
            'status': status,
            'executed_at': datetime.now().isoformat(),
            'data': data,
        }

        # Always record in-memory for local/dev environments
        self.execution_steps.setdefault(conversation_id, []).append(step_data)
        if len(self.execution_steps[conversation_id]) > 1000:
            self.execution_steps[conversation_id] = self.execution_steps[conversation_id][-1000:]

        # If Redis is enabled, mirror to Redis list
        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = safe_serialize_for_logging(step_data)
                key = f"agent_execution_steps:{conversation_id}"
                self.redis_cache.rpush_json(key, serialized_data)
                # Optional: Trim list to prevent unbounded growth (keep last 1000 items)
                if self.redis_cache.llen(key) > 1000:
                    self.redis_cache.ltrim_list(key, 1000)
            except Exception as e:
                logger.error(f"Error logging tool execution: {e}")

    def get_execution_steps(
        self, conversation_id: str, start: int = 0, end: int = -1
    ) -> list:
        """Retrieve execution steps for a conversation.

        If Redis is enabled, it fetches from Redis. Otherwise, it returns the in-memory list,
        emulating Redis LRANGE semantics for start/end indices.
        """
        if self.redis_enabled and self.redis_cache:
            try:
                key = f"agent_execution_steps:{conversation_id}"
                return self.redis_cache.lrange_json(key, start, end)
            except Exception as e:
                logger.error(f"Error retrieving execution steps: {e}")
                return []

        steps = self.execution_steps.get(conversation_id, [])
        if not steps:
            return []

        # Emulate Redis LRANGE behavior
        n = len(steps)
        s = start if start >= 0 else max(n + start, 0)
        e = n if end == -1 else (end + 1 if end >= 0 else n)
        s = max(0, min(s, n))
        e = max(0, min(e, n))
        return steps[s:e]

    def get_recent_execution_steps(self, conversation_id: str, count: int = 10) -> list:
        """Get the most recent execution steps for a conversation.

        Args:
            conversation_id: The conversation ID
            count: Number of recent steps to retrieve

        Returns:
            List of recent execution step dictionaries
        """
        if not self.redis_enabled or not self.redis_cache:
            return []

        try:
            key = f"agent_execution_steps:{conversation_id}"
            # Get last 'count' items from the list
            return self.redis_cache.lrange_json(key, -count, -1)
        except Exception as e:
            logger.error(f"Error retrieving recent execution steps: {e}")
            return []

    def get_execution_steps_count(self, conversation_id: str) -> int:
        """Get the total count of execution steps for a conversation.

        Args:
            conversation_id: The conversation ID

        Returns:
            Total number of execution steps
        """
        if self.redis_enabled and self.redis_cache:
            try:
                key = f"agent_execution_steps:{conversation_id}"
                return self.redis_cache.llen(key)
            except Exception as e:
                logger.error(f"Error getting execution steps count: {e}")
                return 0

        return len(self.execution_steps.get(conversation_id, []))

    def append_execution_step(
        self, conversation_id: str, step_data: Dict[str, Any]
    ) -> None:
        """Add an execution step to the cache. Deprecated: use log_tool_execution instead."""
        # Append to in-memory list
        step_data['executed_at'] = datetime.now().isoformat()
        self.execution_steps.setdefault(conversation_id, []).append(step_data)
        if len(self.execution_steps[conversation_id]) > 1000:
            self.execution_steps[conversation_id] = self.execution_steps[conversation_id][-1000:]

        # Mirror to Redis if enabled
        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = safe_serialize_for_logging(step_data)
                key = f"agent_execution_steps:{conversation_id}"
                self.redis_cache.rpush_json(key, serialized_data)
            except Exception as e:
                logger.error(f"Error appending execution step: {e}")

class AgentState(BaseModel):
    """Current state of the agent during execution."""

    conversation_id: str
    current_iteration: int = 0
    max_iterations: int = 5
    query: str  # Original query
    current_query: Optional[str] = None  # Reformulated/current query being processed
    history: List[HistoryEntry] = []
    conversation_history: List[ConversationEntry] = []
    llm_outputs: List[LLMOutput] = []
    cached_papers: List[Dict[str, Any]] = []

    class Config:
        arbitrary_types_allowed = True

    def add_to_history(
        self,
        intent: str,
        follow_up_question: bool,
        thought: str,
        action: Optional[Dict[str, Any]] = None,
        observation: Optional[str] = None,
        answer: Optional[str] = None,
        iteration: Optional[int] = None,
    ) -> None:
        """Add an entry to history."""
        entry = HistoryEntry(
            intent=intent,
            follow_up_question=follow_up_question,
            thought=thought,
            action=action,
            observation=observation,
            answer=answer,
            iteration=iteration,
        )
        self.history.append(entry)

    def add_conversation_entry(
        self, user_message: str, agent_response: Optional[str] = None
    ) -> None:
        """Add a conversation entry."""
        entry = ConversationEntry(user=user_message, agent=agent_response)
        self.conversation_history.append(entry)

    def update_last_conversation_entry(self, agent_response: str) -> None:
        """Update the last conversation entry with agent response."""
        if self.conversation_history:
            self.conversation_history[-1].agent = agent_response

    def format_history(self) -> str:
        """Format history for prompt."""
        formatted = []
        for entry in self.history:
            formatted.append(f"=========Iteration: {entry.iteration}=========")
            formatted.append(f"Thought: {entry.thought}")
            if entry.action:
                # Handle ActionModel properly
                formatted.append(
                    f"Action: {entry.action.name} - Objective: {entry.action.reason}"
                )
            if entry.observation:
                formatted.append(f"Observation: {entry.observation}")
            if entry.answer:
                formatted.append(f"Answer: {entry.answer}")
            formatted.append(f"=========End of Iteration: {entry.iteration}=========")
        return "\n".join(formatted)

    def format_conversation_history(self) -> str:
        """Format conversation history for prompt."""
        formatted = []
        for entry in self.conversation_history:
            formatted.append(f"User: {entry.user}")
            if entry.agent:
                formatted.append(f"Agent: {entry.agent}")
        return "\n".join(formatted)

    def get_llm_output(self, iteration: int) -> Optional[LLMOutput]:
        """Get LLM output for a specific iteration."""
        for output in self.llm_outputs:
            if output.iteration == iteration:
                return output
        return None

    def get_recent_conversation_history(
        self, max_entries: int = 4
    ) -> List[ConversationEntry]:
        """Get the last few conversation entries for context."""
        return (
            self.conversation_history[-max_entries:]
            if len(self.conversation_history) > max_entries
            else self.conversation_history
        )
