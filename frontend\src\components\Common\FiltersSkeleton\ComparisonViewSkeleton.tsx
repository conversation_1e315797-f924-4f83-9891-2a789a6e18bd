import { Box, Skeleton } from '@mui/material';
import BaseSkeleton from './BaseSkeleton';

const ComparisonViewSkeleton = ({ hideFiltersPanel, onClose, theme }) => {
    const headerContent = (
        <Box
            sx={{
                flexGrow: 1,
                display: "flex",
                flexDirection: "row",
                gap: "8px",
            }}
        >
            <Skeleton variant="rectangular" width="50%" height={40} sx={{ borderRadius: "4px" }} />
            <Skeleton variant="rectangular" width="50%" height={40} sx={{ borderRadius: "4px" }} />
        </Box>
    );

    const bodyContent = (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                p: 1.5,
                pt: 1,
                gap: '16px',
            }}
        >
            <Box
                sx={{
                    flex: '1 1 0',
                    display: 'flex',
                    flexDirection: 'column',
                    height: '50%',
                    minHeight: '200px',
                }}
            >
                <Skeleton variant="rectangular" sx={{ width: '100%', height: '100%' }} />
            </Box>
            <Box
                sx={{
                    flex: '1 1 0',
                    display: 'flex',
                    flexDirection: 'column',
                    height: '50%',
                    minHeight: '200px',
                }}
            >
                <Skeleton variant="rectangular" sx={{ width: '100%', height: '100%' }} />
            </Box>
        </Box>
    );

    return (
        <BaseSkeleton
            hideFiltersPanel={hideFiltersPanel}
            onClose={onClose}
            theme={theme}
            rightHeaderContent={headerContent}
            rightBodyContent={bodyContent}
            showFilterIcon={false}
        />
    );
};

export default ComparisonViewSkeleton;