import logging
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
from dotenv import load_dotenv

load_dotenv()

from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

logger = logging.getLogger(__name__)


def create_database_engine(db_name_env_var: str, default_db_name: str = None):
    """
    Create a database engine with common configuration.

    Args:
        db_name_env_var: Environment variable name for the database name
        default_db_name: Default database name if environment var not set

    Returns:
        SQLAlchemy async engine
    """
    db_host = os.getenv("MYSQL_HOST")
    db_name = os.getenv(db_name_env_var, default_db_name)
    db_user = os.getenv("MYSQL_USER")
    db_password = os.getenv("MYSQL_PASSWORD")

    use_sql_proxy = os.getenv("USE_SQL_PROXY", "false").lower() == "true"
    cloud_sql_instance = os.getenv(
        "CLOUDSQL_INSTANCE", "impactai-430615:us-east1:impactai-db"
    )

    DATABASE_URL = (
        f"mysql+aiomysql://{db_user}:{db_password}@{db_host}/{db_name}"
    )
    if use_sql_proxy:
        unix_socket = f"/cloudsql/{cloud_sql_instance}"
        DATABASE_URL = (
            f"mysql+aiomysql://{db_user}:{db_password}@/{db_name}"
            f"?unix_socket={unix_socket}"
        )

    return create_async_engine(
        DATABASE_URL,
        echo=False,
        pool_size=20,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=1800,
        pool_pre_ping=True,
        execution_options={"compiled_cache": {}},
        connect_args={"connect_timeout": 10},
    )


def create_async_session_maker(engine):
    """
    Create an async session maker with common configuration.

    Args:
        engine: SQLAlchemy async engine

    Returns:
        SQLAlchemy async session maker
    """
    return async_sessionmaker(
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        bind=engine,
        expire_on_commit=False,
    )


@asynccontextmanager
async def get_db_session(session_maker) -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for database sessions with improved error handling.

    Args:
        session_maker: SQLAlchemy async session maker

    Yields:
        AsyncSession: Database session
    """
    session: Optional[AsyncSession] = None
    try:
        session = session_maker()
        yield session
    except Exception as e:
        logger.error("Session error: %s", e)
        if session:
            await session.rollback()
        raise
    finally:
        if session:
            await session.close()
            logger.debug("Session closed, connection returned to pool")
