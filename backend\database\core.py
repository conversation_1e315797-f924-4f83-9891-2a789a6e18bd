import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession

from .utils import (
    create_database_engine,
    create_async_session_maker,
    get_db_session,
)

logger = logging.getLogger(__name__)

# Create engine and session maker for core database
engine = create_database_engine("MYSQL_CORE_DATABASE", "impactai-db")
AsyncSessionLocal = create_async_session_maker(engine)


@asynccontextmanager
async def get_core_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Context manager for core database sessions with improved error handling.
    """
    async with get_db_session(AsyncSessionLocal) as session:
        yield session
