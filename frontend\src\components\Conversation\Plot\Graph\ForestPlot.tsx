import React, { useState, useEffect } from "react";
import { Box, useTheme } from "@mui/material";
import EffectSizesPlot2 from "./EffectSizesPlot2";
import { Outcome } from "./ForestPlotJWT.types";
import * as d3 from "d3";
import "./ForestPlot.css";

interface ForestPlotProps {
  plotData: any;
  effectSizes: any; // This should be the processed data for effect sizes
  studiesData: any;
  selectedOutcome: Outcome;
  selectedIntervention: string;
  activePlotDetails?: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any; } | null;
  onSelectStudy?: (id: string) => void;
}

const ForestPlot: React.FC<ForestPlotProps> = ({
  plotData,
  effectSizes,
  studiesData,
  selectedOutcome,
  selectedIntervention,
  activePlotDetails,
  onSelectStudy,
}: ForestPlotProps) => {
  const theme = useTheme();

  const [selectedOutcome2, setSelectedOutcome2] = useState<Outcome>(
    // uniqueOutcomes[0]
    selectedOutcome
  );

  if (!plotData) {
    return null;
  }

  const yearExtent = d3.extent(
    plotData.flat_effect_sizes.filter((d) =>
      d.outcome_tag_ids.includes(
        selectedOutcome2?.outcome_tag_id || selectedOutcome2
      )
    ),
    (d) => d.year
  );

  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedSectors, setSelectedSectors] = useState<string[]>([]);
  const [selectedIncomeGroups, setSelectedIncomeGroups] = useState<string[]>(
    []
  );
  const [publicationYear2, setPublicationYear2] = useState<
    number[] | undefined[] | string[]
  >(yearExtent);
  const [selectedQualityScore, setSelectedQualityScore] = useState<string[]>(
    []
  );
  console.log(plotData, "plotData");
  const bySelectedOutcome3 = effectSizes.filter(
    (d) =>
      d.outcome_tag_ids.includes(
        selectedOutcome2?.outcome_tag_id || selectedOutcome2
      ) &&
      (selectedSectors.length === 0
        ? true
        : selectedSectors.some((sector) =>
          d.intervention_sectors.includes(sector)
        )) &&
      (selectedRegions.length === 0
        ? true
        : selectedRegions.some((region) => d.region.includes(region))) &&
      (selectedIncomeGroups.length === 0
        ? true
        : selectedIncomeGroups.includes(d.income_group)) &&
      (publicationYear2[0] === publicationYear2[1]
        ? true
        : d.year >= publicationYear2[0] && d.year <= publicationYear2[1]) &&
      (selectedQualityScore.length === 0
        ? true
        : selectedQualityScore.includes(d.quality_score_category))
  );

  const allInterventionsByForSelectedOutcome =
    effectSizes.filter((d) =>
      d.outcome_tag_ids.includes(
        // selectedOutcome2?.outcome_tag_id || selectedOutcome2
        +selectedOutcome?.outcome_tag_id || selectedOutcome
      )
    );
  
  const xDomain = [
    Math.min(
      0,
      d3.min(
        allInterventionsByForSelectedOutcome,
        (d) => d.standardized_ci_lower
      )
    ),
    Math.max(
      0,
      d3.max(
        allInterventionsByForSelectedOutcome,
        (d) => d.standardized_ci_upper
      )
    ),
  ];

  const byIntervention = bySelectedOutcome3.reduce((acc, cur) => {
    [cur.intervention_tag_short_labels].forEach((tag) => {
      if (!acc[tag]) {
        acc[tag] = [];
      }
      acc[tag].push(cur);
    });

    return acc;
  }, {});

  const byInterventionArray = Object.entries(byIntervention).map(
    ([key, value]) => [key, value]
  );
  console.log("byInterventionArray", byInterventionArray, bySelectedOutcome3);

  const [interventions, setInterventions] = useState<{ id: any; label: any }[]>(
    []
  );
  const [plotDataProcessed, setPlotDataProcessed] = useState<any>(null);

  useEffect(() => {
    if (plotData && Array.isArray(plotData) && plotData.length > 0) {
      setPlotDataProcessed(plotData);
    } else {
      setPlotDataProcessed(null);
    }
  }, [plotData]);

  

  useEffect(() => {
    if (selectedOutcome) {
      setActivePanel(selectedOutcome);
    }
  }, [selectedOutcome]);

  useEffect(() => {
    if (selectedOutcome2?.id && plotDataProcessed) {
      const interventionData = d3
        .groups(
          plotDataProcessed.filter((d) => d.outcome_id === selectedOutcome2.id),
          (d) => d.intervention
        )
        .map((intervention) => ({
          id: intervention[1][0]?.intervention_id,
          label: intervention[0],
        }))
        .filter(
          (intervention) =>
            intervention.id !== undefined && intervention.label !== undefined
        );
      setInterventions(interventionData);
    } else {
      setInterventions([]);
    }
  }, [plotDataProcessed, selectedOutcome2?.id]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        borderRadius: "4px",
        backgroundColor: `${theme.palette.background.default}`,
        width: "100%",
        maxWidth: "100%",
        [theme.breakpoints.down("sm")]: {
          padding: "8px",
        },
      }}
    >
      <div>
        <div
          style={{ maxHeight: 300, overflowY: "auto" }}
        >
          {byInterventionArray
            .sort(
              (a, b) =>
                (b[1][0].outcome_connotation === "Negative" ? -1 : 1) *
                d3.mean(b[1], (d: any) => d.cohen_d) -
                (a[1][0].outcome_connotation === "Negative" ? -1 : 1) *
                d3.mean(a[1], (d: any) => d.cohen_d)
            )
            .map((d, idx) => (
              <EffectSizesPlot2
                key={d[0] || idx}
                data={d}
                xDomain={xDomain}
                onSelectStudy={onSelectStudy}
                activePlotCitationIds={(activePlotDetails?.citation_ids || []).map(item => item.value) || []}
                activePlotMessageId={activePlotDetails?.messageId || null}
              />
            ))}

          <h5
            style={{ width: "100%", textAlign: "center", fontWeight: "normal" }}
          >
            Effect Size (in Standardized Mean Difference)
          </h5>
        </div>
      </div>
    </Box>
  );
};

export default ForestPlot;