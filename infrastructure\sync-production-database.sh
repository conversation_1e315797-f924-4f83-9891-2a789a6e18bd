#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Sync production database to development environment"
            echo "  --dry-run    Perform validation checks only, skip actual sync"
            echo "  --help       Show this help message"
            echo ""
            echo "Description:"
            echo "  This script exports the production database and imports it to development."
            echo "  It performs comprehensive validation before making any changes."
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly DEVELOPMENT_INSTANCE_NAME="impactai-db"
readonly PRODUCTION_INSTANCE_NAME="impactai-db-production"
readonly BUCKET_NAME="impactai-db"
readonly DATABASE_NAME="impactai-silver-db"
readonly EXPORT_FILE="$(create_timestamped_filename "$PRODUCTION_INSTANCE_NAME")"

# Custom cleanup function for this script
db_cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Script failed with exit code $exit_code"
        if [ "$DRY_RUN" != true ]; then
            log_info "You may need to manually clean up the export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        fi
    fi
    exit $exit_code
}

# Set up cleanup trap
setup_cleanup db_cleanup

# Function to check if required instances exist
check_instances() {
    log_info "Checking if required Cloud SQL instances exist..."
    
    check_sql_instance "$PRODUCTION_INSTANCE_NAME" "Production instance"
    check_sql_instance "$DEVELOPMENT_INSTANCE_NAME" "Development instance"
    
    log_success "Both instances are accessible"
}

# Function to export database
export_database() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would export from production database..."
        log_info "[DRY RUN] Export file would be: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_success "[DRY RUN] Export validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Starting export from production database..."
    log_info "Export file: gs://$BUCKET_NAME/$EXPORT_FILE"
    
    if gcloud sql export sql "$PRODUCTION_INSTANCE_NAME" "gs://$BUCKET_NAME/$EXPORT_FILE" \
        --database="$DATABASE_NAME" --quiet; then
        log_success "Export completed successfully"
        return 0
    else
        log_error "Export failed"
        return 1
    fi
}

# Function to import database
import_database() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would import to development database..."
        log_info "[DRY RUN] Would use export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_success "[DRY RUN] Import validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Starting import to development database..."
    
    if gcloud sql import sql "$DEVELOPMENT_INSTANCE_NAME" "gs://$BUCKET_NAME/$EXPORT_FILE" \
        --database="$DATABASE_NAME" --quiet; then
        log_success "Import completed successfully"
        return 0
    else
        log_error "Import failed"
        return 1
    fi
}

# Function to clean up export file
cleanup_export_file() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would clean up export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_success "[DRY RUN] Cleanup validation completed - would proceed successfully"
        return 0
    fi
    
    log_info "Cleaning up export file..."
    if gsutil rm "gs://$BUCKET_NAME/$EXPORT_FILE" >/dev/null 2>&1; then
        log_success "Export file cleaned up successfully"
    else
        log_warning "Failed to clean up export file: gs://$BUCKET_NAME/$EXPORT_FILE"
        log_info "You may need to clean it up manually"
    fi
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        log_info "Starting database sync validation (DRY RUN MODE)"
    else
        log_info "Starting database sync from production to development"
    fi
    log_info "Production: $PRODUCTION_INSTANCE_NAME"
    log_info "Development: $DEVELOPMENT_INSTANCE_NAME"
    log_info "Database: $DATABASE_NAME"
    
    # Pre-flight checks
    check_gcloud_auth
    check_instances
    check_gcs_bucket "$BUCKET_NAME"
    
    # Export from production
    if ! export_database; then
        exit 1
    fi
    
    # Import to development
    if ! import_database; then
        log_error "Import failed. Export file remains at: gs://$BUCKET_NAME/$EXPORT_FILE"
        exit 1
    fi
    
    # Clean up
    cleanup_export_file
    
    if [ "$DRY_RUN" = true ]; then
        log_success "Database sync validation completed successfully! All checks passed."
    else
        log_success "Database sync completed successfully!"
    fi
}

# Run main function
main "$@"