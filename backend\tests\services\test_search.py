import pytest
import json
from unittest.mock import patch, AsyncMock
from services.search import SearchService


@pytest.fixture
def search_service():
    """Create a SearchService instance for testing."""
    return SearchService()


@pytest.fixture
def mock_chips_response():
    """Load the mock response from the mocks directory."""
    with open("tests/mocks/mock-search-chips-response.json", "r") as f:
        return json.load(f)


@pytest.mark.asyncio
async def test_get_chips_success(search_service, mock_chips_response):
    """Test get_chips method with successful API response."""
    with patch("services.search.get_json", return_value=mock_chips_response) as mock_get_json:
        chips = await search_service.get_chips()

        # Verify the API was called with correct URL
        mock_get_json.assert_called_once_with(
            url="https://chips-564807556547.us-central1.run.app/get_chips"
        )

        # Verify the response structure and content
        assert isinstance(chips, list)
        assert len(chips) == 10  # 5 interventions + 5 outcomes

        # Check intervention chips
        intervention_chips = [chip for chip in chips if chip["type"] == "intervention"]
        assert len(intervention_chips) == 5
        
        # Test first intervention chip
        first_intervention = intervention_chips[0]
        assert first_intervention["type"] == "intervention"
        assert first_intervention["value"] == "Effectiveness of quality improvement methods"
        assert first_intervention["label"] == "quality improvement methods"

        # Check outcome chips
        outcome_chips = [chip for chip in chips if chip["type"] == "outcome"]
        assert len(outcome_chips) == 5
        
        # Test first outcome chip
        first_outcome = outcome_chips[0]
        assert first_outcome["type"] == "outcome"
        assert first_outcome["value"] == "Improving air quality"
        assert first_outcome["label"] == "air quality"


@pytest.mark.asyncio
async def test_get_chips_empty_response(search_service):
    """Test get_chips method with empty API response."""
    empty_response = {"intervention": [], "outcome": []}
    
    with patch("services.search.get_json", return_value=empty_response):
        chips = await search_service.get_chips()
        
        assert isinstance(chips, list)
        assert len(chips) == 0


@pytest.mark.asyncio
async def test_get_chips_api_error(search_service):
    """Test get_chips method when API call fails."""
    with patch("services.search.get_json", side_effect=Exception("API Error")):
        with pytest.raises(Exception) as exc_info:
            await search_service.get_chips()
        
        assert "API Error" in str(exc_info.value)


@pytest.mark.asyncio
async def test_get_chips_malformed_response(search_service):
    """Test get_chips method with malformed API response."""
    malformed_response = {"intervention": [{"no_question_field": "test"}], "outcome": []}
    
    with patch("services.search.get_json", return_value=malformed_response):
        with pytest.raises(KeyError):
            await search_service.get_chips()


@pytest.mark.asyncio
async def test_get_chips_specific_intervention_data(search_service, mock_chips_response):
    """Test that intervention data is correctly processed."""
    with patch("services.search.get_json", return_value=mock_chips_response):
        chips = await search_service.get_chips()
        
        intervention_chips = [chip for chip in chips if chip["type"] == "intervention"]
        
        # Test specific intervention data
        insurance_chip = next(
            chip for chip in intervention_chips 
            if "insurance programs" in chip["value"]
        )
        assert insurance_chip["type"] == "intervention"
        assert insurance_chip["value"] == "Effectiveness of insurance programs"
        assert insurance_chip["label"] == "insurance programs"


@pytest.mark.asyncio
async def test_get_chips_specific_outcome_data(search_service, mock_chips_response):
    """Test that outcome data is correctly processed."""
    with patch("services.search.get_json", return_value=mock_chips_response):
        chips = await search_service.get_chips()
        
        outcome_chips = [chip for chip in chips if chip["type"] == "outcome"]
        
        # Test specific outcome data
        economic_chip = next(
            chip for chip in outcome_chips 
            if "economic analysis" in chip["value"]
        )
        assert economic_chip["type"] == "outcome"
        assert economic_chip["value"] == "Improving economic analysis"
        assert economic_chip["label"] == "economic analysis"


@pytest.mark.asyncio
async def test_get_chips_empty_labels_fallback(search_service):
    """Test that chips fall back to question when labels are empty."""
    response_with_empty_labels = {
        "intervention": [
            {
                "intervention_id": [1],
                "intervention_label": [],  # Empty label array
                "outcome_id": [],
                "outcome_label": [],
                "target_population_id": [],
                "target_population_label": [],
                "country_code": [],
                "region": [],
                "papers": 10,
                "variant": "basic",
                "question": "Test intervention question"
            }
        ],
        "outcome": [
            {
                "intervention_id": [],
                "intervention_label": [],
                "outcome_id": [1],
                "outcome_label": [],  # Empty label array
                "target_population_id": [],
                "target_population_label": [],
                "country_code": [],
                "region": [],
                "papers": 5,
                "variant": "basic",
                "question": "Test outcome question"
            }
        ]
    }
    
    with patch("services.search.get_json", return_value=response_with_empty_labels):
        chips = await search_service.get_chips()
        
        assert len(chips) == 2
        
        # Check intervention falls back to question
        intervention_chip = next(chip for chip in chips if chip["type"] == "intervention")
        assert intervention_chip["value"] == "Test intervention question"
        assert intervention_chip["label"] == "Test intervention question"  # Falls back to question
        
        # Check outcome falls back to question  
        outcome_chip = next(chip for chip in chips if chip["type"] == "outcome")
        assert outcome_chip["value"] == "Test outcome question"
        assert outcome_chip["label"] == "Test outcome question"  # Falls back to question
