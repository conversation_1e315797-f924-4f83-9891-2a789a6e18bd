import React, { useState, useRef, useEffect } from "react";
import { Paper, Box, Typography, Card, CardContent, CardMedia, IconButton } from "@mui/material";
import PlayCircleIcon from "@mui/icons-material/PlayCircleOutline";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";
import { useTheme } from "@mui/material/styles";

const Section2 = () => {
    const theme = useTheme();
    const [isPlaying, setIsPlaying] = useState(false);
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const [videoUrl, setVideoUrl] = useState<string>("");
    useEffect(() => {
        let url;
        if (/prod/.test(window.location.hostname)) {
            url = 'https://storage.googleapis.com/impactai-website-production/ImpactAi_UserJourney.mp4';
        } else {
            url = 'https://storage.googleapis.com/impactai-website-development/ImpactAi_UserJourney.mp4';
        }
        setVideoUrl(url);
    }, []);
    const videoRef = useRef<HTMLVideoElement | null>(null);

    const handlePlay = () => {
        setIsPlaying(true);
        if (videoRef.current) {
            videoRef.current.play().catch(error => {
                console.error("Playback failed:", error);
            });
        }
    };

    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                background: "none",
                boxShadow: 0,
                borderRadius: "8px",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: isMobile ? "48px" : isTablet ? "56px" : "70px",
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                    mt: 2,
                    position: "relative",
                }}
            >
                <Card
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        borderRadius: "16px",
                        p: 2,
                        boxShadow: 0,
                        height: "auto",
                        background: "none",
                        position: "relative",
                    }}
                >
                    {/* Show Image if Not Playing */}
                    {!isPlaying ? (
                        <>
                            <CardMedia
                                component="img"
                                image="/images/home/<USER>"
                                alt="Video thumbnail depicting women at a market"
                                sx={{
                                    width: "100%",
                                    borderRadius: "12px",
                                }}
                            />
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: "50%",
                                    left: "50%",
                                    transform: "translate(-50%, -50%)",
                                    display: "flex",
                                    alignItems: "center",
                                }}
                            >
                                <IconButton
                                    onClick={handlePlay}
                                    sx={{
                                        backgroundColor: "rgba(0,0,0,0.5)",
                                        color: theme.palette.common.white,
                                        borderRadius: isMobile ? "20px" : isTablet ? "28px" : "50px",
                                        p: isMobile ? 0.2 : isTablet ? 0.4 : 0.8,
                                        display: 'flex',
                                        alignItems: 'center',
                                        "&:hover": {
                                            backgroundColor: "rgba(0,0,0,0.5)",
                                            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                                        },
                                        cursor: "pointer",
                                    }}
                                >
                                    <PlayCircleIcon sx={{ fontSize: isMobile ? 16 : isTablet ? 20 : 48 }} />
                                    <Typography
                                        variant={isMobile ? "caption" : isTablet ? "body2" : "h2"}
                                        sx={{
                                            marginLeft: "3px",
                                            color: `${theme.palette.common.white} !important`,
                                            fontWeight: 600,
                                            fontSize: isMobile ? "0.7rem" : isTablet ? "0.9rem" : "inherit",
                                        }}
                                    >
                                        Play
                                    </Typography>
                                </IconButton>
                            </Box>
                        </>
                    ) : (
                        // Show Video when Playing
                        <video
                            ref={videoRef}
                            poster="/images/home/<USER>"
                            controls
                            autoPlay
                            style={{
                                width: "100%",
                                borderRadius: "12px",
                                background: "black",
                                maxHeight: "736px",
                            }}
                        >
                            <source src={videoUrl} type="video/mp4" />
                            Your browser does not support the video tag.
                        </video>
                    )}

                    <CardContent sx={{ textAlign: "center", width: "80%", p: '0px !important' }}>
                        <Typography variant="body2" color="text.secondary">
                        </Typography>
                    </CardContent>
                </Card>
            </Box>
        </Paper>
    );
};

export default Section2;
