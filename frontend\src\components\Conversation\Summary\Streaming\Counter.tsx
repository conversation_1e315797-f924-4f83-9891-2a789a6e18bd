import { Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';

const Counter = ({ count, disableHover = false, textColor, backgroundColor, borderColor }) => {
    const theme = useTheme();
    const colorToUse = textColor || theme.components.icon.hover;
    const backgroundColorToUse = backgroundColor || theme.brandVisuals.twoHundread;
    const borderStyle = borderColor ? `1px solid ${borderColor}` : 'none';

    return (
        <Typography
            variant="body1"
            component="span"
            color={colorToUse}
            fontSize="12px"
            fontWeight="500"
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '22px',
                width: '16px',
                borderRadius: '50%',
                backgroundColor: backgroundColorToUse,
                border: borderStyle,
                padding: 0,
                transition: 'background-color 0.3s ease, color 0.3s ease',
                "&:hover": disableHover
                    ? {}
                    : {
                        backgroundColor: theme.palette.action.active,
                        color: theme.common.white.main,
                    },
            }}
        >
            {count}
        </Typography>
    );
};

export default Counter;