import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

import pytest
from helpers.filesystem import get_fixture_file_path

from src.services.agent import AgentService
from src.agent.main import Agent


def load_mock_data():
    file_path = get_fixture_file_path("mock-agent-responses.json")
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


MOCK_DATA = load_mock_data()


@pytest.fixture
def agent_service():
    """Create AgentService with mocked Google Cloud authentication to prevent credential errors."""
    # Mock Google Cloud authentication components before creating AgentService
    with patch('google.auth.default') as mock_google_auth, \
         patch('src.tools.structure_data_organizer.AuthorizedSession') as mock_auth_session, \
         patch('google.cloud.storage.Client') as mock_storage_client:

        # Configure mock Google auth to return fake credentials
        mock_credentials = Mock()
        mock_credentials.universe_domain = "googleapis.com"
        mock_google_auth.return_value = (mock_credentials, "fake-project")
        mock_auth_session.return_value = Mock()
        mock_storage_client.return_value = Mock()

        yield AgentService()


@pytest.fixture
def mock_data():
    mock_data_object = MOCK_DATA[0]
    context_data = mock_data_object["response"]["context"]

    return {
        "summary": mock_data_object["response"]["response"],
        "query": context_data["query"],
        "conversation_id": context_data["conversation_id"],
        "data_used": context_data["tool_data"]["data_used"],
        "expected_response": mock_data_object["response"],
    }


@pytest.fixture
def mock_agent(mock_data):
    mock = Mock(spec=Agent)
    mock.conversation_id = mock_data["conversation_id"]
    mock.tool_manager = Mock()

    mock.tool_manager.get_tool_data.return_value = {
        "structured_data": {"text": "some structured data"},
        "rag_results": {"documents": []},
        "data_used_url": "http://example.com/data",
    }

    async def mock_execute(*args, **kwargs):  # pylint: disable=unused-argument
        return mock_data["summary"]

    mock.execute = mock_execute
    return mock


@pytest.mark.asyncio
async def test_service_response_structure_with_data(
    agent_service, mock_agent, mock_data
):
    """Test service returns correct response structure with tool data."""
    with patch("src.services.agent.Agent", return_value=mock_agent):
        result = await agent_service.execute(
            conversation_id=mock_data["conversation_id"],
            query=mock_data["query"],
        )

        # Test service contract: response structure matches API expectations
        assert "response" in result
        assert "context" in result
        assert result["response"] == mock_data["summary"]
        assert result["context"]["query"] == mock_data["query"]
        assert result["context"]["conversation_id"] == mock_data["conversation_id"]
        assert "tool_data" in result["context"]
        assert "structured_data" in result["context"]["tool_data"]
        assert "rag_results" in result["context"]["tool_data"]
        assert "data_url" in result["context"]["tool_data"]


@pytest.mark.asyncio
async def test_service_response_structure_without_data(
    agent_service, mock_agent, mock_data
):
    """Test service returns correct response structure when no tool data available."""
    mock_agent.tool_manager.get_tool_data.return_value = {
        "dict_rows": None,
        "structured_data": None,
        "rag_results": None,
    }

    with patch("src.services.agent.Agent", return_value=mock_agent):
        result = await agent_service.execute(
            conversation_id=mock_data["conversation_id"],
            query=mock_data["query"],
        )

        # Test service contract: response structure consistent even without data
        assert "response" in result
        assert "context" in result
        assert result["response"] == mock_data["summary"]
        assert result["context"]["query"] == mock_data["query"]
        assert result["context"]["conversation_id"] == mock_data["conversation_id"]
        assert "tool_data" in result["context"]
        assert result["context"]["tool_data"]["structured_data"] is None
        assert result["context"]["tool_data"]["rag_results"] is None
        assert result["context"]["tool_data"]["data_url"] is None


@pytest.mark.asyncio
async def test_service_handles_null_conversation_id(agent_service, mock_data):
    """Test service handles null conversation_id correctly."""
    # Create a fresh mock agent that preserves None conversation_id
    mock_agent = Mock(spec=Agent)
    mock_agent.conversation_id = None  # This should stay None in the test
    mock_agent.tool_manager = Mock()
    mock_agent.tool_manager.get_tool_data.return_value = {
        "structured_data": None,
        "rag_results": None,
        "data_used_url": None,
        "url_post_processed": None,
        "structured_data_v2": None,
    }

    async def mock_execute(*args, **kwargs):  # pylint: disable=unused-argument
        return mock_data["summary"]

    mock_agent.execute = mock_execute

    # Patch Agent where it's used in the AgentService module
    with patch("src.services.agent.Agent", return_value=mock_agent) as mock_agent_class:
        result = await agent_service.execute(
            conversation_id=None,
            query=mock_data["query"],
        )

        # Verify the mock was called (this helps debug if patch isn't working)
        mock_agent_class.assert_called_once()

        # Test service contract: handles null conversation_id
        assert "response" in result
        assert "context" in result
        assert result["context"]["conversation_id"] is None
        assert result["context"]["query"] == mock_data["query"]


@pytest.mark.asyncio
async def test_generate_summary_stores_initial_status_and_yields_response(agent_service, mock_data):  # pylint: disable=unused-argument
    """Test generate_summary stores initial stream status in cache and yields final response."""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    query = "test query"
    expected_cache_key = f"summary_data:{conversation_id}:{message_id}"

    # Mock the cache methods
    agent_service.cache.store_json = Mock()
    agent_service.cache.get_json = Mock()

    # Mock the execute method to return test data
    mock_response = {
        "response": "Test summary response",
        "context": {
            "tool_data": {"test": "data"}
        }
    }

    # Mock Google Cloud authentication to prevent credential errors during Agent creation
    with patch('google.auth.default') as mock_google_auth, \
         patch('src.tools.structure_data_organizer.AuthorizedSession') as mock_auth_session, \
         patch('google.cloud.storage.Client') as mock_storage_client, \
         patch.object(agent_service, 'execute',
                      AsyncMock(return_value=mock_response)) as mock_execute:

        # Configure mock Google auth to return fake credentials
        mock_credentials = Mock()
        mock_credentials.universe_domain = "googleapis.com"
        mock_google_auth.return_value = (mock_credentials, "fake-project")
        mock_auth_session.return_value = Mock()
        mock_storage_client.return_value = Mock()

        # Execute the method and collect yielded values
        results = []
        async for result in agent_service.generate_summary(conversation_id, message_id, query):
            results.append(result)

        # Verify execute was called with correct parameters
        mock_execute.assert_called_once()
        call_args = mock_execute.call_args[0]  # Get positional arguments
        assert len(call_args) == 2  # conversation_id, query, agent
        assert call_args[0] == conversation_id  # conversation_id
        assert call_args[1] == query  # query
        assert isinstance(mock_execute.call_args[1]["agent"], Agent)  # agent should be an instance of Agent

    # Verify cache was called twice (initial and final status)
    assert agent_service.cache.store_json.call_count == 2

    # Verify both calls used the correct cache key
    for call in agent_service.cache.store_json.call_args_list:
        assert call[0][0] == expected_cache_key  # First argument is the key

    # Verify the final stream status
    final_call = agent_service.cache.store_json.call_args_list[1]
    final_status = final_call[0][1]  # Second argument is the data
    assert final_status["query"] == query
    assert final_status["stream_started_at"] is not None
    assert final_status["summary_text"] == "Test summary response"
    assert final_status["summary_data"] == {"test": "data"}
    assert final_status["stream_ended_at"] is not None

    # Verify at least the initial status and final summary are yielded
    # Note: In test environment without real tool execution, we expect at least 2 events:
    # 1. Initial "Thinking." status
    # 2. Final summary
    assert len(results) >= 2

    # Verify initial status event (should be the starting research message)
    first_result = results[0]
    assert first_result.startswith('event: status\n')
    assert '"title": "Thinking."' in first_result
    assert '"description": "Initializing research process for your query"' in first_result

    # Verify final summary event
    expected_summary = 'event: summary\ndata: "Test summary response"\n\n'
    assert results[-1] == expected_summary


def test_format_to_sse_event_valid_event_types(agent_service):
    """Test format_to_sse_event formats SSE events correctly for valid event types."""
    # Test status event
    status_event = agent_service.format_to_sse_event("status", {"message": "Processing..."})
    expected_status = 'event: status\ndata: {"message": "Processing..."}\n\n'
    assert status_event == expected_status

    # Test summary event
    summary_event = agent_service.format_to_sse_event("summary", {"text": "Summary result"})
    expected_summary = 'event: summary\ndata: {"text": "Summary result"}\n\n'
    assert summary_event == expected_summary

    # Test error event
    error_event = agent_service.format_to_sse_event("error", {"error": "Something went wrong"})
    expected_error = 'event: error\ndata: {"error": "Something went wrong"}\n\n'
    assert error_event == expected_error


def test_format_to_sse_event_invalid_event_type(agent_service):
    """Test format_to_sse_event raises ValueError for invalid event types."""
    with pytest.raises(ValueError, match="Event type must be 'status', 'summary', or 'error'"):
        agent_service.format_to_sse_event("invalid", {"data": "test"})


@pytest.mark.asyncio
async def test_generated_summary_data_returns_none_when_no_cache_data(agent_service):
    """Test generated_summary_data returns None when no data exists in cache."""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    expected_cache_key = f"summary_data:{conversation_id}:{message_id}"

    # Mock cache to return None (no data found)
    agent_service.cache.get_json = Mock(return_value=None)

    result = await agent_service.generated_summary_data(conversation_id, message_id)

    # Verify cache was checked with correct key
    agent_service.cache.get_json.assert_called_once_with(expected_cache_key)

    # Verify None is returned when no cache data exists
    assert result is None


@pytest.mark.asyncio
async def test_generated_summary_data_returns_data_when_stream_has_ended(agent_service):
    """Test generated_summary_data returns cached data when stream has completed."""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    expected_cache_key = f"summary_data:{conversation_id}:{message_id}"

    # Mock cache data with completed stream
    cached_data = {
        "query": "test query",
        "stream_started_at": "2024-01-01 12:00:00.000000",
        "stream_ended_at": "2024-01-01 12:05:00.000000",  # Stream has ended
        "summary_text": "Completed summary",
        "summary_data": {"result": "data"}
    }
    agent_service.cache.get_json = Mock(return_value=cached_data)

    result = await agent_service.generated_summary_data(conversation_id, message_id)

    # Verify cache was checked with correct key
    agent_service.cache.get_json.assert_called_once_with(expected_cache_key)

    # Verify completed stream data is returned immediately
    assert result == cached_data


@pytest.mark.asyncio
async def test_generated_summary_data_returns_data_when_stream_older_than_5_minutes(agent_service):
    """Test generated_summary_data returns cached data when stream is older than 5 minutes."""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    expected_cache_key = f"summary_data:{conversation_id}:{message_id}"

    # Create a timestamp that's older than 5 minutes
    old_time = datetime.now() - timedelta(minutes=6)
    cached_data = {
        "query": "test query",
        "stream_started_at": old_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "stream_ended_at": None,  # Stream hasn't ended but is old
        "summary_text": None,
        "summary_data": None
    }
    agent_service.cache.get_json = Mock(return_value=cached_data)

    result = await agent_service.generated_summary_data(conversation_id, message_id)

    # Verify cache was checked with correct key
    agent_service.cache.get_json.assert_called_once_with(expected_cache_key)

    # Verify old stream data is returned without waiting
    assert result == cached_data


@pytest.mark.asyncio
async def test_generated_summary_data_waits_and_retries_for_recent_incomplete_stream(agent_service):
    """Test generated_summary_data waits 1 second and retries for recent incomplete streams."""
    conversation_id = "test_conv_123"
    message_id = "msg_456"
    expected_cache_key = f"summary_data:{conversation_id}:{message_id}"

    # Create a recent timestamp (less than 5 minutes old)
    recent_time = datetime.now() - timedelta(minutes=2)

    # First call returns incomplete recent stream
    incomplete_data = {
        "query": "test query",
        "stream_started_at": recent_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "stream_ended_at": None,  # Stream hasn't ended and is recent
        "summary_text": None,
        "summary_data": None
    }

    # Second call (after wait) returns completed stream
    complete_data = {
        "query": "test query",
        "stream_started_at": recent_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "stream_ended_at": "2024-01-01 12:05:00.000000",  # Now completed
        "summary_text": "Final summary",
        "summary_data": {"final": "data"}
    }

    # Mock cache to return incomplete data first, then complete data
    agent_service.cache.get_json = Mock(side_effect=[incomplete_data, complete_data])

    # Mock asyncio.sleep to avoid actual waiting in tests
    with patch('asyncio.sleep', return_value=None) as mock_sleep:
        result = await agent_service.generated_summary_data(conversation_id, message_id)

    # Verify cache was checked twice (initial + retry after wait)
    assert agent_service.cache.get_json.call_count == 2
    agent_service.cache.get_json.assert_called_with(expected_cache_key)

    # Verify sleep was called with 1 second
    mock_sleep.assert_called_once_with(1)

    # Verify final completed data is returned
    assert result == complete_data
