"""Pipeline module for the research agent."""

from .manager import PipelineManager
from .base import Pipeline
from .descriptive import DescriptivePipeline
from .impact import ImpactPipeline
from .comparative import ComparativePipeline
from .generalizability import GeneralizabilityPipeline
from .implementation import ImplementationPipeline
from .change import ChangePipeline
from .methodology import MethodologyPipeline

__all__ = [
    "PipelineManager",
    "Pipeline",
    "DescriptivePipeline",
    "ImpactPipeline",
    "ComparativePipeline",
    "GeneralizabilityPipeline",
    "ImplementationPipeline",
    "ChangePipeline",
    "MethodologyPipeline",
]
