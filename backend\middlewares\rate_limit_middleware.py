from fastapi import Request, status
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)


def request_requires_rate_limiting(request: Request):

    if request.method not in ["POST"]:
        return False

    if "/conversations/" not in request.url.path:
        return False

    # Our target = request.url.path = '/conversations/aba7a271-8ccf-46d4-8f90-5aca167508b7'
    split_path = request.url.path.split("/")
    if len(split_path) != 3:
        return False
    return True


async def rate_limit_middleware(request: Request, call_next):

    if not request_requires_rate_limiting(request):
        response = await call_next(request)
        return response

    user_id = request.state.user_id

    if rate_limit_service.has_exceeded_limit(user_id):
        logger.info("Rate limit exceeded for user: %s", user_id)
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "success": False,
                "message": "Usage limit exceeded",
            },
        )

    response = await call_next(request)

    if response.status_code < 500:
        rate_limit_service.increment_usage(user_id)

    return response
