from unittest.mock import AsyncMock, Mock, patch

import pytest
from services.paper import get_paper_abstracts_by_ids

# Sample test data
SAMPLE_SQL_QUERY = "select id, abstract from papers where id in (:paper_ids);"
SAMPLE_PAPER_IDS = [1, 2, 3]
SAMPLE_RESULTS = [
    {"id": 1, "abstract": "First paper abstract"},
    {"id": 2, "abstract": "Second paper abstract"},
    {"id": 3, "abstract": "Third paper abstract"},
]


@pytest.fixture
def mock_session():
    session = AsyncMock()
    session.execute = AsyncMock()
    return session


@pytest.fixture
def mock_sql_result():
    """Mock SQLAlchemy row objects with _asdict method"""
    rows = []
    for data in SAMPLE_RESULTS:
        row = Mock()
        row._asdict = Mock(return_value=data)
        rows.append(row)

    result = Mock()
    result.all = Mock(return_value=rows)
    return result


@pytest.mark.asyncio
async def test_get_paper_abstracts_by_ids_success(mock_session, mock_sql_result):
    """Test successful retrieval of paper abstracts"""
    mock_session.execute.return_value = mock_sql_result

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        result = await get_paper_abstracts_by_ids([1, 2, 3])

    assert result == SAMPLE_RESULTS
    mock_session.execute.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_paper_abstracts_by_ids_empty_list(mock_session):
    """Test handling of empty paper IDs list"""
    empty_result = Mock()
    empty_result.all = Mock(return_value=[])
    mock_session.execute.return_value = empty_result

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        result = await get_paper_abstracts_by_ids([])

    assert result == []
    mock_session.execute.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_paper_abstracts_by_ids_single_id(mock_session):
    """Test handling of single paper ID"""
    single_row = Mock()
    single_row._asdict = Mock(return_value={"id": 42, "abstract": "Single abstract"})

    single_result = Mock()
    single_result.all = Mock(return_value=[single_row])
    mock_session.execute.return_value = single_result

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        result = await get_paper_abstracts_by_ids([42])

    assert result == [{"id": 42, "abstract": "Single abstract"}]
    assert len(result) == 1
    mock_session.execute.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_paper_abstracts_by_ids_sql_injection_protection():
    """Test SQL injection protection by verifying ID formatting"""
    mock_session = AsyncMock()
    empty_result = Mock()
    empty_result.all = Mock(return_value=[])
    mock_session.execute.return_value = empty_result

    # Test with potentially malicious input
    malicious_ids = [1, 2, "'; DROP TABLE papers; --"]

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        await get_paper_abstracts_by_ids(malicious_ids)

    # Verify that IDs are properly converted to strings and joined
    call_args = mock_session.execute.call_args
    executed_query = str(call_args[0][0])

    # The malicious input should be converted to string, not executed as SQL
    assert "1,2,'; DROP TABLE papers; --" in executed_query


@pytest.mark.asyncio
async def test_get_paper_abstracts_by_ids_database_error():
    """Test handling of database errors"""
    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.side_effect = Exception("Database connection failed")

        with pytest.raises(Exception, match="Database connection failed"):
            await get_paper_abstracts_by_ids([1, 2, 3])


@pytest.mark.asyncio
async def test_files_service_integration(mock_session):
    """Test integration with FilesService for loading SQL query"""
    mock_sql_result = Mock()
    mock_sql_result.all = Mock(return_value=[])
    mock_session.execute.return_value = mock_sql_result

    test_sql = "SELECT id, abstract FROM papers WHERE id IN (:paper_ids);"

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request", return_value=test_sql
    ) as mock_files:
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        await get_paper_abstracts_by_ids([1, 2, 3])

    mock_files.assert_called_once()
    # Verify SQL query placeholder replacement
    call_args = mock_session.execute.call_args
    executed_query = str(call_args[0][0])
    assert "1,2,3" in executed_query
    assert "SELECT id, abstract FROM papers WHERE id IN (1,2,3);" == executed_query


@pytest.mark.asyncio
async def test_database_session_integration():
    """Test proper use of database session context manager"""
    mock_session = AsyncMock()
    mock_result = Mock()
    mock_result.all = Mock(return_value=[])
    mock_session.execute.return_value = mock_result

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        await get_paper_abstracts_by_ids([1, 2, 3])

    # Verify context manager was used
    mock_ctx.assert_called_once()
    mock_session.execute.assert_awaited_once()


@pytest.mark.asyncio
async def test_text_query_execution():
    """Test that SQL is executed using text() function"""

    mock_session = AsyncMock()
    mock_result = Mock()
    mock_result.all = Mock(return_value=[])
    mock_session.execute.return_value = mock_result

    with patch("services.paper.get_core_db_session") as mock_ctx, patch(
        "services.paper.files_service.load_abstract_request",
        return_value=SAMPLE_SQL_QUERY,
    ):
        mock_ctx.return_value.__aenter__.return_value = mock_session
        mock_ctx.return_value.__aexit__.return_value = None

        await get_paper_abstracts_by_ids([1, 2, 3])

    # Verify session.execute was called with text() object
    call_args = mock_session.execute.call_args
    executed_query = call_args[0][0]
    assert hasattr(executed_query, "text")  # text() objects have .text attribute
