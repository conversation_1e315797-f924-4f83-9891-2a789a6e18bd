import json
import pytest
from unittest.mock import patch, AsyncMock
from routers.search import get_search_chips


@pytest.fixture
def mock_chips_response():
    """Load the mock response from the mocks directory."""
    with open("tests/mocks/mock-search-chips-response.json", "r") as f:
        return json.load(f)


@pytest.fixture
def expected_chips_data():
    """Expected chips data structure from the service."""
    return [
        {
            "type": "intervention",
            "value": "Effectiveness of quality improvement methods",
            "label": "quality improvement methods"
        },
        {
            "type": "intervention", 
            "value": "Effectiveness of insurance programs",
            "label": "insurance programs"
        },
        {
            "type": "intervention",
            "value": "Effectiveness of social support services", 
            "label": "social support services"
        },
        {
            "type": "intervention",
            "value": "Effectiveness of social welfare programs",
            "label": "social welfare programs"
        },
        {
            "type": "intervention",
            "value": "Effectiveness of policy trial",
            "label": "policy trial"
        },
        {
            "type": "outcome",
            "value": "Improving air quality",
            "label": "air quality"
        },
        {
            "type": "outcome",
            "value": "Improving economic analysis",
            "label": "economic analysis"
        },
        {
            "type": "outcome",
            "value": "Improving arrest rates among at-risk youth.",
            "label": "arrest"
        },
        {
            "type": "outcome",
            "value": "Improving agricultural extension",
            "label": "agricultural extension"
        },
        {
            "type": "outcome",
            "value": "Improving financial transactions",
            "label": "financial transactions"
        }
    ]


@pytest.mark.asyncio
async def test_get_search_chips_success(expected_chips_data):
    """Test successful response from get_search_chips function."""
    with patch("routers.search.search_service.get_chips", 
               return_value=expected_chips_data) as mock_get_chips:
        
        result = await get_search_chips()
        
        # Verify the response structure
        assert result["success"] is True
        assert "data" in result
        assert "entries" in result["data"]
        
        entries = result["data"]["entries"]
        assert len(entries) == 10  # 5 interventions + 5 outcomes
        
        # Verify intervention entries
        intervention_entries = [entry for entry in entries if entry["type"] == "intervention"]
        assert len(intervention_entries) == 5
        
        # Verify outcome entries  
        outcome_entries = [entry for entry in entries if entry["type"] == "outcome"]
        assert len(outcome_entries) == 5
        
        # Verify service was called
        mock_get_chips.assert_called_once()


@pytest.mark.asyncio
async def test_get_search_chips_empty_response():
    """Test endpoint with empty service response."""
    with patch("routers.search.search_service.get_chips", return_value=[]):
        
        result = await get_search_chips()
        
        assert result["success"] is True
        assert result["data"]["entries"] == []


@pytest.mark.asyncio
async def test_get_search_chips_service_value_error():
    """Test endpoint when service raises a ValueError."""
    with patch("routers.search.search_service.get_chips", 
               side_effect=ValueError("Service error")):
        
        result = await get_search_chips()
        
        assert result["success"] is False
        assert result["error"] == "Failed to fetch entries."


@pytest.mark.asyncio
async def test_get_search_chips_service_generic_error():
    """Test endpoint when service raises a generic exception."""
    with patch("routers.search.search_service.get_chips", 
               side_effect=Exception("Generic error")):
        
        # Since the router only catches ValueError, other exceptions should propagate
        with pytest.raises(Exception) as exc_info:
            await get_search_chips()
        
        assert "Generic error" in str(exc_info.value)


@pytest.mark.asyncio
async def test_get_search_chips_response_structure(expected_chips_data):
    """Test that response structure matches expected format."""
    with patch("routers.search.search_service.get_chips", 
               return_value=expected_chips_data):
        
        result = await get_search_chips()
        
        # Verify response structure
        assert "success" in result
        assert "data" in result
        assert "entries" in result["data"]
        
        # Verify each entry has required fields
        for entry in result["data"]["entries"]:
            assert "type" in entry
            assert "value" in entry
            assert "label" in entry
            assert entry["type"] in ["intervention", "outcome"]


@pytest.mark.asyncio
async def test_get_search_chips_intervention_content(expected_chips_data):
    """Test that intervention content is correctly processed."""
    with patch("routers.search.search_service.get_chips", 
               return_value=expected_chips_data):
        
        result = await get_search_chips()
        
        intervention_entries = [
            entry for entry in result["data"]["entries"] 
            if entry["type"] == "intervention"
        ]
        
        # Test specific intervention content
        insurance_entry = next(
            entry for entry in intervention_entries 
            if "insurance programs" in entry["value"]
        )
        assert insurance_entry["value"] == "Effectiveness of insurance programs"
        assert insurance_entry["label"] == "insurance programs"


@pytest.mark.asyncio
async def test_get_search_chips_outcome_content(expected_chips_data):
    """Test that outcome content is correctly processed."""
    with patch("routers.search.search_service.get_chips", 
               return_value=expected_chips_data):
        
        result = await get_search_chips()
        
        outcome_entries = [
            entry for entry in result["data"]["entries"] 
            if entry["type"] == "outcome"
        ]
        
        # Test specific outcome content
        economic_entry = next(
            entry for entry in outcome_entries 
            if "economic analysis" in entry["value"]
        )
        assert economic_entry["value"] == "Improving economic analysis"
        assert economic_entry["label"] == "economic analysis" 