#!/bin/bash

# Source logging if not already loaded
if ! declare -f log_info >/dev/null 2>&1; then
    source "$(dirname "${BASH_SOURCE[0]}")/logging.sh"
fi

# Standard script initialization with strict error handling
init_script() {
    # Exit on any error, undefined variable, or pipe failure
    set -euo pipefail
}

# Generic cleanup function that can be customized per script
setup_cleanup() {
    local cleanup_function="${1:-default_cleanup}"
    trap "$cleanup_function" EXIT
}

# Default cleanup function
default_cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Script failed with exit code $exit_code"
    fi
    exit $exit_code
}

# Function to show standard help header
show_help_header() {
    local script_name="$1"
    local description="$2"
    
    echo "Usage: $script_name [OPTIONS]"
    echo ""
    echo "$description"
    echo ""
    echo "Options:"
}

# Function to show standard help footer
show_help_footer() {
    echo ""
    echo "Common Options:"
    echo "  --dry-run    Perform validation checks only, skip actual operations"
    echo "  --help       Show this help message"
}

# Function to parse common command line arguments
parse_common_args() {
    local -n dry_run_ref=$1
    
    while [[ $# -gt 1 ]]; do
        case $2 in
            --dry-run)
                dry_run_ref=true
                shift
                ;;
            -h|--help)
                return 1  # Signal that help was requested
                ;;
            *)
                # Unknown option, let the caller handle it
                return 2
                ;;
        esac
        shift
    done
    return 0
}

# Function to validate required environment variable
require_env_var() {
    local var_name="$1"
    local var_value="${!var_name:-}"
    local description="${2:-$var_name}"
    
    if [[ -z "$var_value" ]]; then
        log_error "$description environment variable is required"
        return 1
    fi
    
    return 0
}

# Function to validate that a value is in a list of allowed values
validate_enum() {
    local value="$1"
    local var_name="$2"
    shift 2
    local allowed_values=("$@")
    
    for allowed in "${allowed_values[@]}"; do
        if [[ "$value" == "$allowed" ]]; then
            return 0
        fi
    done
    
    log_error "Invalid $var_name value: '$value'"
    log_info "$var_name must be one of: ${allowed_values[*]}"
    return 1
}

# Function to create a timestamped filename
create_timestamped_filename() {
    local prefix="$1"
    local suffix="${2:-.sql}"
    echo "${prefix}-$(date +%Y%m%d-%H%M%S)${suffix}"
} 