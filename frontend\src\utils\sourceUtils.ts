/**
 * Utility functions for sanitizing and formatting source data
 */

/**
 * Removes HTML tags from a string while preserving content
 * @param text String that might contain HTML tags
 * @returns Sanitized string without HTML tags
 */
export const removeHtmlTags = (text: string | null | undefined): string => {
  if (!text) return '';
  
  // First replace specific tags that we've seen in the data
  let sanitized = text
    .replace(/<scp>([^<]+)<\/scp>/gi, '$1')  // Remove <scp> tags but keep their content
    .replace(/<sup>([^<]+)<\/sup>/gi, '')    // Remove <sup> tags and their content
    .replace(/<[^>]+>/g, '');                // Remove any remaining HTML tags
    
  return sanitized;
};

/**
 * Decodes HTML entities in a string
 * @param text String that might contain HTML entities
 * @returns String with decoded HTML entities
 */
export const decodeHtmlEntities = (text: string | null | undefined): string => {
  if (!text) return '';
  
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  
  // This uses the browser's built-in HTML entity decoding
  const decoded = textarea.value;
  
  return decoded;
};

/**
 * Comprehensive sanitization of text data from sources
 * Removes HTML tags and decodes HTML entities
 * @param text String to sanitize
 * @returns Fully sanitized string
 */
export const sanitizeSourceText = (text: string | null | undefined): string => {
  if (!text) return '';
  
  // First remove HTML tags, then decode entities
  const withoutTags = removeHtmlTags(text);
  const decoded = decodeHtmlEntities(withoutTags);
  
  return decoded.trim();
};
