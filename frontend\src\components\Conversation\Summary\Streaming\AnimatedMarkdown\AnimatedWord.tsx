import React from 'react';

interface AnimatedWordProps {
  word: string;
  delay: number;
  onAnimated: () => void;
  isEmphasized?: boolean;
  isBold?: boolean;
}

const AnimatedWord = React.memo(({ word, delay, onAnimated, isEmphasized, isBold }: AnimatedWordProps) => {
  const animationSpeed = 0.03;
  const animationStyle = {
    opacity: 0,
    animation: `fadeIn ${animationSpeed}s ${delay}s forwards`,
  };

  return (
    <React.Fragment>
      {isBold ? (
        <strong style={animationStyle} onAnimationEnd={onAnimated} className={`animated-word ${isEmphasized ? 'emphasized' : ''}`}>
          {word}
        </strong>
      ) : (
        <span
          className={`animated-word ${isEmphasized ? 'emphasized' : ''}`}
          style={animationStyle}
          onAnimationEnd={onAnimated}
        >
          {word}
        </span>
      )}
    </React.Fragment>
  );
});

export default AnimatedWord;