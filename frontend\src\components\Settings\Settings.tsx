import DeleteIcon from '@mui/icons-material/Delete';
import { <PERSON><PERSON>, <PERSON>, Typography, Tab, Tabs } from '@mui/material';
import { del } from "../../services/apiService";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from 'react';
import { useTheme } from "@mui/material/styles";
import useDynamicMeta from '../../hooks/useDynamicMeta';

interface SettingsProps {
    updateRefreshChatHistory: (value: boolean) => void;
}

const Settings: React.FC<SettingsProps> = ({ updateRefreshChatHistory }) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState(0);

    useDynamicMeta({
        title: 'ImpactAI Settings Page - Tranform Evidence into Action - World Bank Group',
        description: 'Manage application settings and clear site data.'
    });

    useEffect(() => {
        updateRefreshChatHistory(true);
        return () => {
            updateRefreshChatHistory(false);
        };
    }, [updateRefreshChatHistory]);

    const handleDelete = async (url: string) => {
        try {
            await del(url);
            navigate('/');
        } catch (error) {
            console.error("Error deleting data:", error);
        }
    };

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setActiveTab(newValue);
    };

    const deleteActions = [
        { icon: DeleteIcon, label: 'Dev Data', url: 'https://api.impact-ai-dev.app/conversations/clear' },
        { icon: DeleteIcon, label: 'Prod Data', url: 'https://api.impact-ai-prod.app/conversations/clear' },
    ];

    return (
        <Box sx={{ padding: '80px', maxWidth: '600px', margin: '0 auto' }}>
            <Typography variant="h4" component="h1" sx={{ marginBottom: '24px' }}>
                Settings
            </Typography>
            <Box sx={{ borderBottom: '1px solid #ddd', marginBottom: '16px' }}>
                <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    aria-label="Settings Tabs"
                    TabIndicatorProps={{ style: { background: theme.palette.primary.main } }}
                >
                    <Tab
                        label="Clear Site Data"
                        typography="subtitle2"
                        sx={{
                            textTransform: 'uppercase',
                            padding: '9px 16px',
                            color: theme.palette.primary.main,
                        }}
                    />
                    <Tab
                        label="API"
                        disabled
                        typography="subtitle2"
                        sx={{
                            textTransform: 'uppercase',
                            padding: '9px 16px',
                            color: theme.palette.primary.main,
                        }}
                    />
                </Tabs>
            </Box>

            {activeTab === 0 && (
                <Box sx={{ marginBottom: '16px' }}>
                    <Typography variant="h6" component="h2" sx={{ fontWeight: 'bold', marginBottom: '8px' }}>
                        Clear Site Data
                    </Typography>
                    <Typography variant="body2" sx={{ marginBottom: '16px' }}>
                        Clearing the site data will remove all your stored chats such as conversations. Please proceed with caution.
                    </Typography>
                    <Box
                        sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '8px',
                            [theme.breakpoints.down('sm')]: {
                                flexDirection: 'column',
                                alignItems: 'center',
                            },
                        }}
                    >
                        {deleteActions.map(({ icon: Icon, label, url }, index) => (
                            <Box key={index}>
                                <Button
                                    variant="outlined"
                                    size="small"
                                    color="primary"
                                    startIcon={<Icon />}
                                    onClick={() => handleDelete(url)}
                                    sx={{
                                        width: "141px",
                                        height: "30px",
                                        color: theme.sidebar.secondaryFocused,
                                        borderColor: theme.elevation.outlined,
                                        textTransform: 'uppercase',
                                        "&:hover": {
                                            borderColor: theme.palette.divider,
                                            backgroundColor: theme.palette.action.hover,
                                        },
                                    }}
                                >
                                    {label}
                                </Button>
                            </Box>
                        ))}
                    </Box>
                </Box>
            )}
        </Box>
    );
};

export default Settings;