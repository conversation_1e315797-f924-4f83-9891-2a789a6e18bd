"""RAG search module for development economics research documents."""

from __future__ import annotations

import json
import logging
from typing import Any, Dict, List, Union

from pydantic import BaseModel

from src.tools.base import Tool
from src.tools.sql_generator import QueryResult
from src.utils.flags import get_use_new_da_flag
from src.utils.http_client import get_json
from src.utils.url_management import get_rag_tool_url

logger = logging.getLogger("tool.rag_search")

# API Configuration
RAG_API_URL = get_rag_tool_url()

_TOKEN_KEYS = ("prompt_tokens", "completion_tokens", "thoughts_token_count")

# TODO: remove after we migrate to new DA
USE_NEW_DA_FLAG = get_use_new_da_flag()


def _ensure_tokens(md: Dict[str, Any] | None) -> Dict[str, int]:
    md = md or {}
    for k in _TOKEN_KEYS:
        md.setdefault(k, 0)
    return md


class Document(BaseModel):
    """Model representing a document in the RAG search results."""

    paper_id: str
    text: str
    title: str = "No title"
    url: str = ""

    def __str__(self) -> str:
        """String representation of the document."""
        return f'"{self.text}" [{self.paper_id}]'


class RAGResults(BaseModel):
    """Results from RAG search."""

    documents: List[Document]
    metadata: Dict[str, Any] = {}

    def model_post_init(self, __context: Any) -> None:  # noqa: D401, N802
        self.metadata = _ensure_tokens(self.metadata)

    def __str__(self) -> str:
        """String representation of the RAG results."""
        return (
            f"We now have the answer for the query after performing a semantic search.\n"
            f"Output:\nContext for the query (with sources between brackets): {self.documents}\n"
        )


class RAGSearcher(Tool):
    """Tool for semantic search in research papers using external API."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the RAG searcher."""
        super().__init__(
            name="rag_search",
            description="Search within academic papers using embeddings.",
            func=self.search,
            arguments=[
                (
                    "query_result",
                    "QueryResult(user_query: str, dataset: str, row_count: int, unique_papers: int, paper_ids: Dict[str, Any], execution_time: float, metadata: Dict[str, Any])",
                ),
                ("num_results", "int = 15"),
            ],
            outputs=[
                (
                    "rag_results",
                    "RAGResults(documents: str, source_documents: List[str], metadata: Dict[str, Any])",
                )
            ],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.logger = logging.getLogger(f"tool.{self.name}")

    def _process_query_result(
        self, qr_in: Union[QueryResult, Dict[str, Any], str]
    ) -> QueryResult:
        if isinstance(qr_in, QueryResult):
            return qr_in
        if isinstance(qr_in, str):
            qr_in = json.loads(qr_in)
        return QueryResult(**qr_in)  # type: ignore[arg-type]

    async def _call_api(
        self, query: str, paper_ids: List[str], limit: int
    ) -> Dict[str, Any]:
        params = {"query": query, "limit": limit}
        if paper_ids:
            params["document_ids"] = ",".join(paper_ids)
        if self.verbose:
            self.logger.info("Calling RAG API with params: %s", params)
        resp = await get_json(f"{RAG_API_URL}/search", params=params)
        resp.setdefault("metadata", {})
        resp["metadata"] = _ensure_tokens(resp["metadata"])
        return resp

    def _build_results(
        self, api_resp: Dict[str, Any], query: str, paper_ids: List[str] | None = None
    ) -> RAGResults:
        """Build RAGResults from API response and query result."""
        docs = []
        for res in api_resp.get("results", []):
            docs.append(
                Document(
                    paper_id=res.get("document_id", ""),
                    text=res.get("text", ""),
                    # title=qr.paper_ids.get(res.get("document_id", ""), "No title"),
                    url=res.get("url", ""),
                )
            )
        md = {
            "query": query,
            "embedding_model": api_resp.get("embedding_model", ""),
            "total_found": api_resp.get("total_found", 0),
            "time_taken": api_resp.get("time_taken", 0),
            "paper_ids": paper_ids or [],
            # "data_points": qr.row_count,
            **api_resp.get("metadata", {}),
        }
        return RAGResults(documents=docs, metadata=md)

    async def search(
        self,
        query_result: Union[QueryResult, Dict[str, Any], str],
        num_results: int = 15,
        paper_ids: List[str] | None = None,
    ) -> RAGResults:
        """Search for relevant passages in papers using external API."""
        try:
            if USE_NEW_DA_FLAG:
                if self.verbose:
                    logger.info(f"Starting RAG search with query: {query_result}")
                    logger.info(
                        f"Number of papers: {len(paper_ids) if paper_ids else 'all'}"
                    )
                # Make API call and build results
                api_resp = await self._call_api(query_result, paper_ids, num_results)

                if self.verbose:
                    logger.info(
                        f"API returned {len(api_resp.get('results', []))} results"
                    )
                return self._build_results(api_resp, query_result, paper_ids)
            else:
                # Process and validate input
                qr = self._process_query_result(query_result)
                paper_ids = list(qr.paper_ids.keys())

                if self.verbose:
                    logger.info(f"Starting RAG search with query: {qr.user_query}")
                    logger.info(f"Number of papers: {qr.row_count}")

                # Make API call and build results
                api_resp = await self._call_api(qr.user_query, paper_ids, num_results)

                if self.verbose:
                    logger.info(
                        f"API returned {len(api_resp.get('results', []))} results"
                    )
                return self._build_results(api_resp, qr)

        except Exception as e:
            if self.verbose:
                logger.error(f"Error performing RAG search: {str(e)}")
            raise
