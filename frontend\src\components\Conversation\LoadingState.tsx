import { Grid, Skeleton, Box, styled, Typography, Theme } from '@mui/material';
import { useIsMobile, useIsTablet } from "../Layout/MobileUtils";

interface LoadingStateProps {
  theme: Theme;
  layoutType?: 'text' | 'skeleton';
  loadingTexts?: string[];
  currentTextIndex?: number;
}

const LoadingSpinner = () => {
  return (
    <Box
      className="spinner-container"
      component="img"
      src="/ImpactAI.svg"
      title="Loading Spinner"
      sx={{
        width: '32px',
        height: '32px',
        border: 'none',
      }}
    />
  );
};

const LoadingTextItem = ({ text, theme }: { text: string, theme: Theme }) => (
  <Typography variant="body1"
    sx={{
      color: theme.palette.text.primary,
      fontSize: '16px'
    }}
  >{text}</Typography>
);

const LoadingState = ({ theme, layoutType, loadingTexts, currentTextIndex }: LoadingStateProps) => {
  return (
    <Box
      className="loading-state"
      sx={{
        alignItems: 'center',
        justifyContent: 'center',
        height: '32px',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box>
        {layoutType === 'text' && loadingTexts && Array.isArray(loadingTexts) && loadingTexts.length > 0 && typeof currentTextIndex === 'number' && currentTextIndex >= 0 && currentTextIndex < loadingTexts.length ? (
          <LoadingTextItem text={loadingTexts[currentTextIndex]} theme={theme} />
        ) : null}
      </Box>
    </Box>
  );
};

const CenteredPageLoader = (isMobileProp: any) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;

  return (
    <Box
      className="conversation-container"
      display="flex"
      flexDirection="column"
      height={`calc(100vh - ${typeof isMobileProp === 'object' && isMobileProp.isMobile !== undefined ? (isMobileProp.isMobile ? '0' : '34.4vh') : (isMobileProp ? '0' : '14.4vh')})`}
      width={isMobileOrTablet ? '50%' : '848px'}
      mx="auto"
      justifyContent="center"
      alignItems="center"
    >
      <Grid container direction="column" justifyContent="center" alignItems="center">
        <Box
          component="img"
          src="/ImpactAI.svg"
          alt="ImpactAI Loading Animation"
          sx={{
            width: isMobileOrTablet ? '80px' : '100px',
            height: isMobileOrTablet ? '80px' : '100px',
            border: 'none',
          }}
        />
      </Grid>
    </Box>
  );
};

export { LoadingState, CenteredPageLoader, LoadingTextItem, LoadingSpinner };