import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

const allowedHosts = [
  'localhost',
  'impactai-ai.local',
  'impact-ai-dev.app',
  'impact-ai-test.app',
  'impact-ai-prod.app',
]

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    allowedHosts,
    port: 3000,
    host: true,
  },
  preview: {
    port: 3000,
    strictPort: true,
    host: true,
    allowedHosts,
  },
  plugins: [react()],
  build: {
    outDir: 'dist',
    minify: 'esbuild',
    sourcemap: false,
    target: 'esnext',
    rollupOptions: {
      input: {
        main: './index.html',
        index: './src/index.tsx',
      },
    },
  },
})
