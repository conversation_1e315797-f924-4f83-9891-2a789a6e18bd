import { useState, memo, SyntheticEvent, useEffect } from 'react';
import { Box, Typography, Slider, styled, Divider, Chip, Select, MenuItem, Checkbox, ListItemText, IconButton } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CloseIcon from '@mui/icons-material/Close';
import { FilterState } from '../../../types/ConversationTypes';

interface FiltersPanelProps {
  theme: any;
  onFiltersChange: (filters: FilterState) => void;
  filterOptions: FilterState;
  activeFilters: FilterState;
}

const StyledSlider = styled(Slider)(({ theme }) => ({
  '& .MuiSlider-rail': {
    height: '2px',
    borderRadius: '100px',
    opacity: 0.38,
    backgroundColor: theme.elevation.paperElevationSixteen,
  },
  '& .MuiSlider-track': {
    height: '2px',
    borderRadius: '100px',
    background: 'none',
    border: 'none'
  },
  '& .MuiSlider-mark': {
    width: '2px',
    height: '2px',
    borderRadius: '100px',
    opacity: 0.38,
    background: theme.palette.action.active,
  },
  '& .MuiSlider-markActive': {
    background: theme.palette.action.active,
  },
  '& .MuiSlider-thumb': {
    width: '12px',
    height: '12px',
    border: 'none',
    boxShadow: '0 1px 5px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.05), 0 3px 1px -2px rgba(0, 0, 0, 0.05)',
    backgroundColor: theme.palette.action.disabledBackground,
    '&::before': {
      backgroundColor: '#D4E4FC',
    }
  },
  '& .MuiSlider-valueLabel': {
    top: -10,
    backgroundColor: 'transparent',
    color: theme.palette.text.secondary,
    padding: 0,
    '&::before': {
      display: 'none',
    },
    '& .MuiSlider-valueLabelLabel': {
      fontSize: 13,
      fontWeight: 600,
      lineHeight: '12px',
      letterSpacing: '0.15px',
    },
  }
}));

const FiltersPanel = memo(({ theme, onFiltersChange, filterOptions, activeFilters }: FiltersPanelProps) => {
  const yearsRange = filterOptions?.years || [];

  const [year, setYear] = useState<number[]>(activeFilters?.years || yearsRange);
  const [activeSectors, setActiveSectors] = useState<string[]>(activeFilters?.sectors || []);
  const [activeGeographies, setActiveGeographies] = useState<string[]>(activeFilters?.countries || []);
  const [isGeographyMenuOpen, setIsGeographyMenuOpen] = useState(false);
  useEffect(() => {
    if (activeFilters?.years) {
      setYear(activeFilters.years);
    } else {
      setYear(yearsRange);
    }

    if (activeFilters?.sectors) {
      setActiveSectors(activeFilters.sectors);
    } else {
      setActiveSectors([]);
    }

    if (activeFilters?.countries) {
      setActiveGeographies(activeFilters.countries);
    } else {
      setActiveGeographies([]);
    }
  }, [activeFilters, yearsRange]);

  const handleYearChange = (event: Event, newValue: number | number[]) => {
    if (Array.isArray(newValue)) {
      setYear(newValue);
    }
  };

  const handleYearChangeCommitted = (event: Event | SyntheticEvent, newValue: number | number[]) => {
    if (Array.isArray(newValue)) {
      onFiltersChange({
        ...activeFilters,
        years: newValue,
      });
    }
  };

  const handleChipClick = (sector: string) => {
    const newSectors = activeSectors.includes(sector)
      ? activeSectors.filter((s) => s !== sector)
      : [...activeSectors, sector];

    setActiveSectors(newSectors);
    onFiltersChange({
      ...activeFilters,
      sectors: newSectors,
    });
  };

  const handleChipDelete = (sector: string) => {
    const newSectors = activeSectors.filter((s) => s !== sector);
    setActiveSectors(newSectors);
    onFiltersChange({
      ...activeFilters,
      sectors: newSectors,
    });
  };

  const handleGeographyChange = (event: any) => {
    const newGeographies = event.target.value as string[];
    setActiveGeographies(newGeographies);
    onFiltersChange({
      ...activeFilters,
      countries: newGeographies,
    });
  };

  const handleClearGeography = (event: SyntheticEvent) => {
    event.stopPropagation();
    setActiveGeographies([]);
    onFiltersChange({
      ...activeFilters,
      countries: [],
    });
  };

  const labelStyles = {
    color: 'rgba(0, 51, 128, 0.70)',
    fontFeatureSettings: "'liga' off, 'clig' off",
    fontFamily: 'Roboto',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 600,
    lineHeight: '12px',
    letterSpacing: '0.15px',
    mb: 2,
  };

  const CustomIcon = () => {
    const iconButtonStyles = {
      width: '30px',
      height: '30px',
      padding: '5px',
      borderRadius: '100px',
      color: theme.palette.action.disabled,
    };

    if (activeGeographies.length > 0) {
      return (
        <IconButton sx={iconButtonStyles} onClick={handleClearGeography}>
          <CloseIcon
            sx={{
              fontSize: '16px',
              color: theme.palette.primary.main,
            }}
          />
        </IconButton>
      );
    }

    return (
      <IconButton sx={iconButtonStyles}>
        {isGeographyMenuOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
      </IconButton>
    );
  };


  return (
    <Box
      id="filter-panel-body"
      sx={{
        width: '100%',
        height: '100%',
        background: theme.palette.common.white,
        display: 'flex',
        flexDirection: 'column',
        overflowY: 'auto',
        overflowX: 'hidden'
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box sx={{ mb: 2 }}>
          <Typography sx={labelStyles}>
            Year
          </Typography>
          <Box sx={{ width: '100%' }}>
            <StyledSlider
              aria-label="Year range"
              value={year}
              onChange={handleYearChange}
              onChangeCommitted={handleYearChangeCommitted}
              valueLabelDisplay="auto"
              step={1}
              marks
              min={yearsRange[0]}
              max={yearsRange[yearsRange.length - 1]}
            />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography
              sx={{
                color: theme.palette.text.secondary,
                fontSize: '13px',
                fontWeight: 600,
              }}
            >
              {year[0]}
            </Typography>
            <Typography
              sx={{
                color: theme.palette.text.secondary,
                fontSize: '13px',
                fontWeight: 600,
              }}
            >
              {year[1]}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 2 }}>
          <Typography sx={labelStyles}>
            Sectors
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
            {filterOptions?.sectors?.map((sector) => (
              <Chip
                key={sector}
                label={sector}
                size="small"
                onClick={() => handleChipClick(sector)}
                onDelete={activeSectors.includes(sector) ? () => handleChipDelete(sector) : undefined}
                deleteIcon={<CloseIcon sx={{ fontSize: '16px' }} />}
                sx={{
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '3px 4px',
                  borderRadius: '100px',
                  border: `1px solid ${theme.palette.secondary.main}`,
                  opacity: activeSectors.includes(sector) ? 1 : 0.38,
                  backgroundColor: activeSectors.includes(sector) ? '#F2F6FC' : 'inherit',
                  '& .MuiChip-label': {
                    fontFamily: 'Roboto',
                    fontSize: '13px',
                    fontWeight: 400,
                    lineHeight: '18px',
                    letterSpacing: '0.16px',
                    color: activeSectors.includes(sector) ? theme.palette.text.primary : theme.palette.text.secondary,
                    padding: '0 4px',
                  },
                  '& .MuiChip-deleteIcon': {
                    color: theme.palette.primary.main,
                    fontSize: '16px',
                    '&:hover': {
                      color: theme.palette.primary.main,
                    },
                  },
                  '&:hover': {
                    backgroundColor: '#F2F6FC',
                    opacity: 1,
                  },
                }}
              />
            ))}
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 2 }}>
          <Typography sx={labelStyles}>
            Geography
          </Typography>
          <Select
            id="filter-panel-body"
            multiple
            displayEmpty
            value={activeGeographies}
            onChange={handleGeographyChange}
            onOpen={() => setIsGeographyMenuOpen(true)}
            onClose={() => setIsGeographyMenuOpen(false)}
            IconComponent={CustomIcon}
            renderValue={(selected) =>
              selected.length === 0 ? (
                <Typography
                  sx={{
                    color: 'rgba(0, 51, 128, 0.70)',
                    fontFamily: 'Roboto',
                    fontSize: '13px',
                    fontWeight: 400,
                    lineHeight: '18px',
                    letterSpacing: '0.17px',
                    ...(isGeographyMenuOpen && { display: 'flex', alignItems: 'center', height: '100%' }),
                  }}
                >
                  Filter Geographies
                </Typography>
              ) : (
                <Typography
                  sx={{
                    fontFamily: 'Roboto',
                    fontSize: '13px',
                    fontWeight: 400,
                    lineHeight: '18px',
                    letterSpacing: '0.16px',
                    color: theme.palette.text.primary,
                    padding: '0 4px',
                  }}
                >
                  {selected.join(', ')}
                </Typography>
              )
            }
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: '206px',
                  padding: '3px 4px',
                  borderRadius: '0 0 12px 12px',
                  border: `1px solid ${theme.palette.secondary.main}`,
                  borderTop: 'none',
                  background: 'none',
                  boxShadow: 'none',
                },
              },
              sx: {
                marginTop: '-1px',
                '& .MuiList-root': {
                  p: 0,
                },
              },
            }}
            sx={{
              display: 'flex',
              height: '24px',
              padding: '0 4px',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignSelf: 'stretch',
              borderRadius: isGeographyMenuOpen ? '16px 16px 0 0' : '16px',
              border: `1px solid ${activeGeographies.length > 0 || isGeographyMenuOpen ? theme.palette.secondary.main : 'rgba(0, 51, 128, 0.70)'}`,
              backgroundColor: activeGeographies.length > 0 || isGeographyMenuOpen ? 'transparent' : 'transparent',
              '&:hover': {
                backgroundColor: 'transparent',
              },
              '&.Mui-focused': {
                backgroundColor: 'transparent',
                border: `1px solid ${theme.palette.secondary.main}`,
              },
              '& .MuiSelect-select': {
                display: 'flex',
                flexWrap: 'wrap',
                gap: '4px',
                height: '18px',
                minHeight: '18px !important',
                paddingTop: '3px',
                paddingBottom: '3px',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
            }}
          >
            {filterOptions?.countries?.map((geo, index) => (
              <MenuItem
                key={geo}
                value={geo}
                sx={{
                  display: 'flex',
                  minHeight: '36px',
                  padding: '0 4px',
                  alignItems: 'center',
                  backgroundColor: 'transparent',
                  borderTop: index !== 0 ? '1px solid #D4E4FC' : 'none',
                  '&:hover': {
                    backgroundColor: 'transparent',
                  },
                  '& .MuiListItemText-root': {
                    padding: '0 4px',
                  },
                }}
              >
                <Checkbox
                  checked={activeGeographies.indexOf(geo) > -1}
                  sx={{
                    padding: '0px',
                    '& .MuiSvgIcon-root': {
                      width: '20px',
                      height: '20px',
                    },
                  }}
                />
                <ListItemText
                  primary={geo}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '13px',
                    },
                  }}
                />
              </MenuItem>
            ))}
          </Select>
        </Box>
      </Box>
    </Box>
  );
});

export default FiltersPanel;