"""Summary generation module for query results."""

from __future__ import annotations

import json
import logging
import os
import re
from typing import Any, Dict, List, Optional, Union

import aiohttp
from pydantic import BaseModel

from src.tools.base import Tool
from src.tools.rag_searcher import RAGResults, Document
from src.tools.structure_data_organizer import StructuredData
from src.tools.structure_data_organizer import StructuredDataV2
from src.utils.url_management import get_summarizer_tool_url

logger = logging.getLogger("tool.final_answer_generator")


SUMMARIZER_API_URL = get_summarizer_tool_url()
POSTPROCESS_SUMMARY = os.getenv("POSTPROCESS_SUMMARY", "false").lower() == "true"

_TOKEN_KEYS = ("prompt_tokens", "completion_tokens", "thoughts_token_count")


def _ensure_tokens(md: Dict[str, Any] | None) -> Dict[str, int]:
    md = md or {}
    for k in _TOKEN_KEYS:
        md.setdefault(k, 0)
    return md


def remove_unwanted_ids(
    final_answer: str, dataset_rows: list[dict], rag_documents: List[Document]
) -> str:
    """Remove unwanted paper IDs and effect sizes from the final answer."""
    # Remove paper IDs not in the dataset
    paper_ids = set()
    for row in dataset_rows:
        if "paper_combined_id" in row:
            paper_ids.add(row["paper_combined_id"])
    for doc in rag_documents:
        if doc.paper_id:
            paper_ids.add(doc.paper_id)

    # create regex patterns for paper IDs and effect sizes
    # paper ids are in the format "[A123]"
    paper_ids_patterns = re.findall(
        r"\[([A-Z][0-9]+(?:\s*,\s*[A-Z][0-9]+)*)]", final_answer
    )
    # separate paper IDs by comma and remove whitespace
    paper_ids_patterns = [
        paper_id.strip().split(",")
        for paper_id in paper_ids_patterns
        if paper_id.strip()
    ]
    # flatten the list of lists into a single list
    paper_ids_patterns = [
        item.strip() for sublist in paper_ids_patterns for item in sublist
    ]

    for paper_id in paper_ids_patterns:
        # if the paper ID is not in the dataset, remove it from the final answer
        if paper_id not in paper_ids:
            final_answer = final_answer.replace(f"[{paper_id}]", "")

    # Build set of valid effect sizes from dataset
    effect_sizes = set()
    for row in dataset_rows:
        if "intervention_tag_ids" in row and "outcome_tag_ids" in row:
            # Handle multiple outcome IDs separated by semicolons
            intervention_id = str(row["intervention_tag_ids"])
            outcome_ids = str(row["outcome_tag_ids"]).split(";")
            for outcome_id in outcome_ids:
                effect_sizes.add(
                    f"intervention={intervention_id}, outcome={outcome_id.strip()}"
                )

    # Find and split effect size patterns with multiple outcomes
    effect_size_pattern = (
        r"\[intervention\s*=\s*(\d+)(?:;\d+)*\s*,\s*outcome\s*=\s*(\d+(?:;\d+)*)\]"
    )
    matches = re.findall(effect_size_pattern, final_answer)

    for intervention_id, outcome_ids_str in matches:
        original_match = f"[intervention={intervention_id}, outcome={outcome_ids_str}]"
        outcome_ids = outcome_ids_str.split(";")

        # Create separate brackets for each outcome ID
        valid_pairs = []
        for outcome_id in outcome_ids:
            pair = f"intervention={intervention_id}, outcome={outcome_id.strip()}"
            if pair in effect_sizes:
                valid_pairs.append(f"[{pair}]")

        # Replace original with valid pairs or empty string
        replacement = " ".join(valid_pairs) if valid_pairs else ""
        final_answer = final_answer.replace(original_match, replacement)

    # Handle single effect size patterns (no semicolons)
    single_effect_pattern = r"\[intervention\s*=\s*(\d+)\s*,\s*outcome\s*=\s*(\d+)\]"
    single_matches = re.findall(single_effect_pattern, final_answer)

    for intervention_id, outcome_id in single_matches:
        pair = f"intervention={intervention_id}, outcome={outcome_id}"
        original_match = f"[{pair}]"
        if pair not in effect_sizes:
            final_answer = final_answer.replace(original_match, "")

    # Clean up brackets and remove duplicates
    bracket_pattern = r"\[(.*?)\]"
    brackets = re.findall(bracket_pattern, final_answer)

    for bracket_content in brackets:
        original_bracket = f"[{bracket_content}]"

        # Skip empty brackets
        if not bracket_content.strip():
            final_answer = final_answer.replace(original_bracket, "")
            continue

        # Split by commas and clean up
        items = [item.strip() for item in bracket_content.split(",")]
        # Remove empty items
        items = [item for item in items if item]
        # Remove duplicates while preserving order
        unique_items = []
        for item in items:
            if item not in unique_items:
                unique_items.append(item)

        if unique_items:
            new_bracket = f"[{', '.join(unique_items)}]"
            final_answer = final_answer.replace(original_bracket, new_bracket)
        else:
            final_answer = final_answer.replace(original_bracket, "")

    # Clean up any consecutive spaces that might appear
    final_answer = re.sub(r" {2,}", " ", final_answer)

    return final_answer.strip()


def remove_all_ids(final_answer: str):
    """
    Remove all paper IDs and effect sizes (inside square brackets) from the final answer.
    Cleans up any extra whitespace and punctuation left by the removal.
    """
    # Remove all paper IDs and effect sizes inside brackets (including semicolon-separated values)
    # Define regex patterns for paper IDs and effect sizes
    paper_id_pattern = (
        r"[A-Z][0-9]+(?:\s*,\s*[A-Z][0-9]+)*"  # Matches paper IDs like [A123, B456]
    )
    effect_size_pattern = r"intervention\s*=\s*\d+(?:;\d+)*\s*,\s*outcome\s*=\s*\d+(?:;\d+)*"  # Matches effect sizes like [intervention=123, outcome=789;456] or [intervention=10543, outcome=13210;13216]

    # Combine the patterns into a single regex for matching brackets
    combined_pattern = rf"\[(?:{paper_id_pattern}|{effect_size_pattern})\]"

    # Remove all matches of the combined pattern
    final_answer = re.sub(combined_pattern, "", final_answer)
    # Remove leftover spaces before punctuation
    final_answer = re.sub(r"\s+([.,;:!?])", r"\1", final_answer)
    return final_answer.strip()


# -----------------------------------------------------------------------------
# Pydantic DTOs
# -----------------------------------------------------------------------------


class FinalAnswer(BaseModel):
    text: str
    metadata: Dict[str, Any] = {}

    def model_post_init(self, __context: Any):  # noqa: N802,D401
        self.metadata = _ensure_tokens(self.metadata)


# -----------------------------------------------------------------------------
# Utility helpers
# -----------------------------------------------------------------------------


def postprocess_final_answer(
    final_answer: str, dataset_rows: list[dict], rag_documents: List[Document]
) -> str:
    # first, remove any paper IDs and effect sizes IDs not present in the dataset rows
    final_answer = remove_unwanted_ids(final_answer, dataset_rows, rag_documents)
    # here we can add more post-processing steps if needed
    # ...
    # remove all paper IDs and effect sizes
    final_answer = remove_all_ids(final_answer)
    return final_answer.strip()


# -----------------------------------------------------------------------------
# Tool implementation
# -----------------------------------------------------------------------------


class FinalAnswerGenerator(Tool):
    """Generate a concise, reference‑free answer from structured data + RAG context."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            name="final_answer_generator",
            description="Generate a polished answer summarising the research findings.",
            func=self.generate,
            arguments=[
                ("user_query", "str"),
                ("structured_data", "StructuredData | None"),
                ("rag_results", "RAGResults | None"),
                ("dataset_rows", "List[dict] | None"),
                ("previous_conversations", "List[dict] | None"),
            ],
            outputs=[("final_answer", "FinalAnswer")],
            config=config,
        )
        self.verbose = config.get("verbose", False)
        self.logger = logging.getLogger(f"tool.{self.name}")

    # ------------------------------------------------------------------
    # Public coroutine
    # ------------------------------------------------------------------

    async def generate(
        self,
        user_query: str,
        structured_data: Optional[
            Union[StructuredData, List[StructuredDataV2], str]
        ] = None,
        rag_results: Optional[Union[RAGResults, list[dict]]] = None,
        dataset_rows: Optional[List[Dict[str, Any]]] = None,
        conversation_history: Optional[list[dict]] = None,
        **kwargs: Any,
    ) -> FinalAnswer:
        dataset_rows = dataset_rows or []

        analysis = None
        if isinstance(structured_data, StructuredData):
            analysis = structured_data.text
        elif isinstance(structured_data, str):
            analysis = structured_data
        elif isinstance(structured_data, list):
            analysis = [a.model_dump() for a in structured_data]
        else:
            analysis = structured_data

        retrieved_documents = None
        if rag_results is not None:
            if isinstance(rag_results, RAGResults):
                retrieved_documents = rag_results.documents
            elif isinstance(rag_results, list):
                retrieved_documents = [Document(**doc) for doc in rag_results]
            else:
                raise ValueError(
                    "rag_results must be either RAGResults or a list of dictionaries"
                )

        payload: Dict[str, Any] = {"query": user_query}
        if structured_data:
            payload["reference_analysis"] = analysis
        if rag_results:
            payload["relevant_documents"] = [
                doc.model_dump() for doc in retrieved_documents
            ]
        if conversation_history:
            # map conversation_history
            conversation_history = [
                {
                    "query": item.get("user", ""),
                    "answer": item.get("agent", ""),
                }
                for item in conversation_history
            ]
            payload["conversation_history"] = conversation_history

        # Log the payload
        if self.verbose:
            simplified_payload = {
                "query": user_query,
                "reference_analysis": analysis if analysis else None,
                "relevant_documents": (
                    [doc.model_dump() for doc in retrieved_documents[:3]]
                    if retrieved_documents
                    else None
                ),
                "conversation_history": conversation_history,
            }
            logger.info(
                f"Sending payload to Final Answer API: {json.dumps(simplified_payload, indent=2)}"
            )

        async with aiohttp.ClientSession() as sess:
            async with sess.post(
                SUMMARIZER_API_URL,
                json=payload,
                headers={"Content-Type": "application/json"},
            ) as resp:
                resp_text = await resp.text()

                if resp.status != 200:
                    raise Exception(
                        f"Final Answer API returned status {resp.status}: {resp_text}"
                    )

                try:
                    data = json.loads(resp_text)
                except json.JSONDecodeError as e:
                    raise Exception(
                        f"Failed to parse API response: {e}\nResponse text: {resp_text}"
                    )

        # Log the response
        if self.verbose:
            simplified_response = {
                "answer": data.get("answer", "N/A"),
                "metadata": {
                    "prompt": data.get("metadata", {}).get("prompt", "N/A"),
                },
            }
            logger.info(
                f"Final Answer API Response: {json.dumps(simplified_response, indent=2)}"
            )

        md = _ensure_tokens(data.get("metadata"))
        answer_text = data.get("answer", "").strip()
        if POSTPROCESS_SUMMARY:
            answer_text = postprocess_final_answer(
                answer_text, dataset_rows, rag_results.documents if rag_results else []
            )
        return FinalAnswer(text=answer_text, metadata=md)
