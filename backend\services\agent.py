import json
import uuid
import google.generativeai as genai
import structlog
from models.agent import AgentResponse, SummaryResponse
from utils.measure import measure_async_time
from utils.requests import get_json, get_text, post_json, get_stream_events
from utils.urls import get_agent_url
from services.files import FilesService

logger = structlog.get_logger(__name__)

files_service = FilesService()
agent_responses = files_service.load_agent_responses()

hashed_responses = {}
for response in agent_responses:
    key = response["response"]["context"]["query"]
    hashed_responses[key] = response


class AgentService:
    """Service for summarizing data based on query values."""

    def __init__(self, conversation_id: uuid.UUID, message_id: uuid.UUID, query: str):
        self.conversation_id = conversation_id
        self.message_id = message_id
        self.query = query
        self.agent_api_url = get_agent_url()

    @measure_async_time
    async def get_agent_response_data(self) -> AgentResponse:
        try:
            api_url = f"{self.agent_api_url}/conversations/{self.conversation_id}/messages/{self.message_id}/data"
            summary_data_response = await get_json(api_url)
            summary_response = SummaryResponse.from_json(json.dumps(summary_data_response))

            data_used = []
            data_url = summary_response.get_data_url()
            if data_url is not None:
                try:
                    data_used = await get_json(data_url)
                except Exception as e:
                    logger.error("Failed to fetch data from data_url", error=e)

            agent_response_data = {
                "response": summary_response.summary_text,
                "context": {
                    "query": summary_response.query,
                    "conversation_id": self.conversation_id,
                    "tool_data": {
                        "data_url": data_url,
                        "data_used": data_used,
                    }
                }
            }

            return AgentResponse.from_json(json.dumps(agent_response_data))
        except Exception as e:
            logger.error("Failed to get agent response.", error=e)
            return None

    @measure_async_time
    async def generate_summary(self):
        api_url = f"{self.agent_api_url}/conversations/{self.conversation_id}/messages/{self.message_id}/summary"

        return get_stream_events(api_url, params={"query": self.query })

    @measure_async_time
    async def get_summary(self) -> str:
        """Returns the summary as plain text without streaming events."""
        try:
            api_url = f"{self.agent_api_url}/conversations/{self.conversation_id}/messages/{self.message_id}/summary"

            response = await get_text(api_url, params={"query": self.query })
            event_stream_data = response.splitlines()
            # event_stream_data = ['event: summary', 'data: "some data"']
            if len(event_stream_data) > 0 and event_stream_data[0] == "event: summary":
                summary_text = event_stream_data[1].split('data: ')[1]
                return summary_text
            # default to returning as in.
            return response
        except Exception as e:
            logger.error("Failed to get summary.", error=e)
            return ""

    @measure_async_time
    async def execute(self) -> AgentResponse:
        """Fetches summary configs based on the given tag."""
        api_url = f"{self.agent_api_url}/execute"

        response_data = None
        if self.query in hashed_responses:
            response_data = hashed_responses[self.query]

        if not response_data:
            json_data = {
                "conversation_id": str(self.conversation_id),
                "query": self.query,
            }
            response_data = await post_json(api_url, body=json_data, timeout=300)
        try:
            tool_data = (
                response_data.get("response", {})
                .get("context", {})
                .get("tool_data", {})
            )
            if "data_url" in tool_data:
                try:
                    fetched_data_used = await get_json(tool_data["data_url"])
                    response_data["response"]["context"]["tool_data"][
                        "data_used"
                    ] = fetched_data_used
                except Exception as e:
                    logger.error("Failed to fetch data from data_url", error=e)
                    response_data["response"]["context"]["tool_data"]["data_used"] = []
            return AgentResponse.from_json(json.dumps(response_data["response"]))
        except Exception as e:
            logger.error("Failed to get agent response.", error=e)
            raise e


async def get_gemini_completion(
    prompt: str, model_name: str = "gemini-1.5-flash"
) -> str:
    """Generates a completion from a Gemini model with error handling."""
    try:
        model = genai.GenerativeModel(model_name)
        response = await model.generate_content_async(prompt)
        return response.text
    except Exception as e:
        logger.error("Gemini API call failed", error=e)
        return ""
