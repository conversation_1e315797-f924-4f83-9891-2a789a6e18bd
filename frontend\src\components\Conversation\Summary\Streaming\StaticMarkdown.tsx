import React, { useRef } from 'react';
import Markdown from 'markdown-to-jsx';
import LinkComponent from "./LinkComponent";
import { replaceTags } from "./Utils";
import AccessibleHeader from './AnimatedMarkdown/AccessibleHeader';

interface StaticMarkdownProps {
    text: string;
    sources?: any;
    plotData?: any;
    onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
    onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
    messageId?: string;
}

const StaticMarkdown = ({
    text,
    sources,
    plotData,
    onViewOnPlotClicked,
    onViewOnSourceClicked,
    messageId
}: StaticMarkdownProps) => {
    const lastHeaderLevelRef = useRef(0);

    const headerLevels = ['h1', 'h2', 'h3', 'h4', 'h5'] as const;
    type HeaderLevel = typeof headerLevels[number];

    const markdownOptions = {
        overrides: {
            ol: {
                component: (props: any) => (
                    <ol start={props.start}>
                        {React.Children.map(props.children, (child: any, index: number) => (
                            <li key={index}>
                                {React.Children.map(child.props.children, (grandChild: any) => {
                                    if (React.isValidElement(grandChild) && grandChild.type === 'a') {
                                        return (
                                            <LinkComponent
                                                {...grandChild.props}
                                                messageId={messageId || ''}
                                                onViewOnPlotClicked={onViewOnPlotClicked}
                                                onViewOnSourceClicked={onViewOnSourceClicked}
                                                plotData={plotData}
                                            />
                                        );
                                    }
                                    return grandChild;
                                })}
                            </li>
                        ))}
                    </ol>
                ),
            },
            li: {
                component: (props: any) => (
                    <li>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </li>
                ),
            },
            ...headerLevels.reduce((acc, level) => {
                acc[level as HeaderLevel] = {
                    component: (props: any) => (
                        <AccessibleHeader
                            as={level}
                            currentHeaderLevelRef={lastHeaderLevelRef}
                            {...props}
                        />
                    ),
                };
                return acc;
            }, {} as Record<HeaderLevel, any>),
            p: {
                component: (props: any) => (
                    <div>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </div>
                ),
            },
            em: {
                component: ({ children }: { children: React.ReactNode }) => <em>{children}</em>
            },
            hr: {
                component: (props: any) => (
                    <hr {...props} />
                ),
            },
            table: {
                component: (props: any) => (
                    <table className="markdown-table">
                        {props.children}
                    </table>
                ),
            },
            th: {
                component: (props: any) => (
                    <th {...props}>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </th>
                ),
            },
            td: {
                component: (props: any) => (
                    <td {...props}>
                        {React.Children.map(props.children, (child: any) => {
                            if (React.isValidElement(child) && child.type === 'a') {
                                return (
                                    <LinkComponent
                                        {...child.props}
                                        messageId={messageId || ''}
                                        onViewOnPlotClicked={onViewOnPlotClicked}
                                        onViewOnSourceClicked={onViewOnSourceClicked}
                                        plotData={plotData}
                                    />
                                );
                            }
                            return child;
                        })}
                    </td>
                ),
            },
        },
    };

    return (
        <Markdown
            className="markdown-container"
            options={markdownOptions}
        >
            {replaceTags(text, sources, plotData)}
        </Markdown>
    );
};

export default StaticMarkdown;