"""initial database state

Revision ID: 653681e7a8c3
Revises: 
Create Date: 2025-07-18 20:22:25.040405

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '653681e7a8c3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('estimates',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('comparison_id', sa.Integer(), nullable=True),
    sa.Column('outcome_id', sa.String(length=255), nullable=True),
    sa.Column('parameter', sa.String(length=255), nullable=True),
    sa.Column('cohen_d', sa.Float(), nullable=True),
    sa.Column('treatment_effect', sa.Text(), nullable=True),
    sa.Column('hedges_d', sa.Float(), nullable=True),
    sa.Column('precision', sa.String(length=255), nullable=True),
    sa.Column('standardized_ci_lower', sa.Float(), nullable=True),
    sa.Column('standardized_ci_upper', sa.Float(), nullable=True),
    sa.Column('n_control', sa.Float(), nullable=True),
    sa.Column('n_treatment', sa.Float(), nullable=True),
    sa.Column('n_total', sa.Float(), nullable=True),
    sa.Column('effect_type', sa.Text(), nullable=True),
    sa.Column('outcome_type', sa.Text(), nullable=True),
    sa.Column('outcome_scale', sa.Text(), nullable=True),
    sa.Column('regression_type', sa.Text(), nullable=True),
    sa.Column('control_variables', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_estimates_id'), 'estimates', ['id'], unique=False)
    op.create_table('interventions',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('arm_id', sa.Integer(), nullable=True),
    sa.Column('paper_id', sa.String(length=255), nullable=True),
    sa.Column('name', sa.Text(), nullable=True),
    sa.Column('objective', sa.Text(), nullable=True),
    sa.Column('taxonomy_l1', sa.String(length=255), nullable=True),
    sa.Column('scale', sa.Text(), nullable=True),
    sa.Column('taxonomy_l2', sa.String(length=255), nullable=True),
    sa.Column('intensity', sa.Text(), nullable=True),
    sa.Column('taxonomy_l3', sa.String(length=255), nullable=True),
    sa.Column('fidelity', sa.Text(), nullable=True),
    sa.Column('taxonomy_l4', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('taxonomy_l3_definition', sa.String(length=255), nullable=True),
    sa.Column('analysis_unit', sa.Text(), nullable=True),
    sa.Column('taxonomy_l4_definition', sa.String(length=255), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('details', sa.Text(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('timeline', sa.Text(), nullable=True),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_interventions_id'), 'interventions', ['id'], unique=False)
    op.create_table('outcomes',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('outcome_name', sa.Text(), nullable=True),
    sa.Column('intervention_id', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('analysis_unit', sa.Text(), nullable=True),
    sa.Column('taxonomy_l1', sa.String(length=255), nullable=True),
    sa.Column('outcome_connotation', sa.Text(), nullable=True),
    sa.Column('taxonomy_l2', sa.String(length=255), nullable=True),
    sa.Column('outcome_type', sa.Text(), nullable=True),
    sa.Column('taxonomy_l3', sa.String(length=255), nullable=True),
    sa.Column('is_primary_period', sa.Boolean(), nullable=True),
    sa.Column('taxonomy_l4', sa.String(length=255), nullable=True),
    sa.Column('data_collection_round', sa.Text(), nullable=True),
    sa.Column('taxonomy_l3_definition', sa.String(length=255), nullable=True),
    sa.Column('taxonomy_l4_definition', sa.String(length=255), nullable=True),
    sa.Column('category', sa.String(length=255), nullable=True),
    sa.Column('statistical_type', sa.String(length=255), nullable=True),
    sa.Column('standardized', sa.String(length=255), nullable=True),
    sa.Column('standardization_description', sa.String(length=255), nullable=True),
    sa.Column('coding', sa.String(length=255), nullable=True),
    sa.Column('direction', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_outcomes_id'), 'outcomes', ['id'], unique=False)
    op.create_table('papers',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('combined_id', sa.String(length=50), nullable=True),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('citation', sa.Text(), nullable=True),
    sa.Column('abstract', sa.Text(), nullable=True),
    sa.Column('country', sa.String(length=255), nullable=True),
    sa.Column('year', sa.Integer(), nullable=True),
    sa.Column('doi_url', sa.String(length=255), nullable=True),
    sa.Column('doi', sa.Text(), nullable=True),
    sa.Column('authors', sa.Text(), nullable=True),
    sa.Column('volume', sa.Text(), nullable=True),
    sa.Column('issue', sa.Text(), nullable=True),
    sa.Column('pages', sa.Text(), nullable=True),
    sa.Column('url', sa.Text(), nullable=True),
    sa.Column('citation_count', sa.Integer(), nullable=True),
    sa.Column('paper_type', sa.Text(), nullable=True),
    sa.Column('journal_id', sa.Integer(), nullable=True),
    sa.Column('abstract_summary', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_papers_id'), 'papers', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('waitlists',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('organization', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_waitlists_id'), 'waitlists', ['id'], unique=False)
    op.create_table('conversations',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversations_id'), 'conversations', ['id'], unique=False)
    op.create_table('messages',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('conversation_id', sa.String(length=255), nullable=True),
    sa.Column('text', sa.Text(), nullable=True),
    sa.Column('author', sa.String(length=255), nullable=True),
    sa.Column('type', sa.String(length=255), nullable=True),
    sa.Column('choices', sa.JSON(), nullable=True),
    sa.Column('query_values', sa.JSON(), nullable=True),
    sa.Column('streaming_finished_at', sa.DateTime(), nullable=True),
    sa.Column('summary_configs', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_id'), 'messages', ['id'], unique=False)
    op.create_table('feedbacks',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('message_id', sa.String(length=255), nullable=True),
    sa.Column('reaction', sa.String(length=255), nullable=True),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('tags', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['messages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_feedbacks_id'), 'feedbacks', ['id'], unique=False)
    op.create_table('plots',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('message_id', sa.String(length=255), nullable=True),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['messages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_plots_id'), 'plots', ['id'], unique=False)
    op.create_table('sources',
    sa.Column('id', sa.String(length=255), nullable=False),
    sa.Column('message_id', sa.String(length=255), nullable=True),
    sa.Column('paper_id', sa.String(length=255), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('short_paper_id', sa.String(length=255), nullable=True),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('doi_url', sa.String(length=255), nullable=True),
    sa.Column('journal_name', sa.String(length=255), nullable=True),
    sa.Column('citation', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['message_id'], ['messages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sources_id'), 'sources', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sources_id'), table_name='sources')
    op.drop_table('sources')
    op.drop_index(op.f('ix_plots_id'), table_name='plots')
    op.drop_table('plots')
    op.drop_index(op.f('ix_feedbacks_id'), table_name='feedbacks')
    op.drop_table('feedbacks')
    op.drop_index(op.f('ix_messages_id'), table_name='messages')
    op.drop_table('messages')
    op.drop_index(op.f('ix_conversations_id'), table_name='conversations')
    op.drop_table('conversations')
    op.drop_index(op.f('ix_waitlists_id'), table_name='waitlists')
    op.drop_table('waitlists')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_papers_id'), table_name='papers')
    op.drop_table('papers')
    op.drop_index(op.f('ix_outcomes_id'), table_name='outcomes')
    op.drop_table('outcomes')
    op.drop_index(op.f('ix_interventions_id'), table_name='interventions')
    op.drop_table('interventions')
    op.drop_index(op.f('ix_estimates_id'), table_name='estimates')
    op.drop_table('estimates')
    # ### end Alembic commands ###
