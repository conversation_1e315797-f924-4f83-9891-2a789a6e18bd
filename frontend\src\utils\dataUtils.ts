/**
 * Checks if a given value (array or object) contains meaningful content.
 * Returns true if the array has elements or the object has enumerable properties.
 * Handles null/undefined values gracefully.
 * @param value The array or object to check.
 * @returns True if the value contains content, false otherwise.
 */
 export const hasContent = <T>(value: T | null | undefined): boolean => {
    if (value === null || typeof value === 'undefined') {
        return false;
    }
    if (Array.isArray(value)) {
        return value.length > 0;
    } else if (typeof value === 'object') {
        return Object.keys(value).length > 0;
    }
    return false;
};