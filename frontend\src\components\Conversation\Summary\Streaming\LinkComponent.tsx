import React, { useRef, useState, useCallback } from "react";
import { Box, IconButton } from "@mui/material";
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import { useTheme } from '@mui/material/styles';
import ShowChartOutlinedIcon from '@mui/icons-material/ShowChartOutlined';
import CustomTooltip from '../../../Common/CustomTooltip';

interface LinkComponentProps {
  children: React.ReactNode;
  href?: string;
  messageId: string;
  onViewOnPlotClicked: (payload: {
    citation_ids: {
      key: string; value: string
    }[];
    messageId: string;
    plotDataInfo?: any
  }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  plotData?: any;
}

const LinkComponent: React.FC<LinkComponentProps> = ({
  children,
  href,
  messageId,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  plotData
}) => {
  const params = href ? new URLSearchParams(href) : null;

  const allPaperIds = params?.getAll("paper_id") || [];
  const hasInterventionParam = params?.has("intervention");
  const hasOutcomeParam = params?.has("outcome");

  const isSourceLink = allPaperIds.length > 0;
  const isPlotLink = hasInterventionParam || hasOutcomeParam;

  const [tooltipOpen, setTooltipOpen] = useState(false);
  const iconRef = useRef(null);
  const theme = useTheme();

  const handleTooltipOpen = (event: React.PointerEvent) => {
    setTooltipOpen(true);
    event.stopPropagation();
  };

  const handleTooltipClose = (event: React.PointerEvent) => {
    setTooltipOpen(false);
    event.stopPropagation();
  };

  const handleIconClick = useCallback(() => {
    if (isSourceLink) {
      onViewOnSourceClicked({ paper_ids: allPaperIds, messageId });
    } else if (isPlotLink) {
      const plotIdentifiers: { key: string; value: string }[] = [];
      if (hasInterventionParam) {
        params?.getAll("intervention").forEach(id => plotIdentifiers.push({ key: 'intervention', value: id }));
      }
      if (hasOutcomeParam) {
        params?.getAll("outcome").forEach(id => plotIdentifiers.push({ key: 'outcome', value: id }));
      }
      onViewOnPlotClicked({ citation_ids: plotIdentifiers, messageId, plotDataInfo: plotData });
    }
  }, [
    isSourceLink, allPaperIds, messageId, onViewOnSourceClicked,
    isPlotLink, hasInterventionParam, hasOutcomeParam, params, messageId, onViewOnPlotClicked,
    plotData
  ]);

  const commonIconStyles = {
    display: "flex",
    width: "16px",
    height: "24px",
    padding: "0px",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: "10px",
    flexShrink: 0,
    borderRadius: '100px',
    position: "relative",
    top: "-5px",
    mr: "2px",
    color: theme.components.icon.defaultLight,
    backgroundColor: theme.components.icon.backgroundDefaultFill,
    "&:hover": {
      backgroundColor: theme.components.icon.backgroundHover,
      color: theme.components.icon.defaultLight,
    },
    minWidth: "18px",
    minHeight: "18px",
  };

  const tooltipContent = isSourceLink ? "View Sources" : isPlotLink ? "View Graph" : "No details available";

  const renderIcon = isSourceLink || isPlotLink;

  return (
    <Box component="span" sx={{ display: "inline-flex", alignItems: "center" }}>
      <Box
        component="span"
        sx={{
          display: "inline-block",
          verticalAlign: "middle",
          lineHeight: 1.4,
        }}
      >
        {children}
      </Box>
      {renderIcon && (
        <Box
          sx={{
            display: "inline-flex",
            justifyContent: "center",
            alignItems: "center",
            flexShrink: 0,
          }}
        >
          <CustomTooltip
            content={tooltipContent}
            enterDelay={100}
            leaveDelay={300}
            open={tooltipOpen}
            onOpen={handleTooltipOpen}
            onClose={handleTooltipClose}
            placement="top"
          >
            <IconButton
              ref={iconRef}
              className="chart-icon"
              sx={{
                ...commonIconStyles,
                cursor: 'pointer',
              }}
              onPointerEnter={handleTooltipOpen}
              onPointerLeave={handleTooltipClose}
              onClick={handleIconClick}
              disabled={false}
            >
              {isSourceLink ? (
                <ArticleOutlinedIcon sx={{ fontSize: 12 }} pointerEvents="none" />
              ) : isPlotLink ? (
                <ShowChartOutlinedIcon sx={{ fontSize: 12 }} pointerEvents="none" />
              ) : null}
            </IconButton>
          </CustomTooltip>
        </Box>
      )}
    </Box>
  );
};

export default LinkComponent;