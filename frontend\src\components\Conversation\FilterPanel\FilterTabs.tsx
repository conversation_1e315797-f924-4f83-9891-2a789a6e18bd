import React from 'react';
import {
    Box,
    Tabs,
    Tab,
    useTheme,
} from '@mui/material';

interface FilterTabsProps {
    tabKeys: string[];
    activeTab: string;
    tabCounts: { [key: string]: number };
    onTabChange: (event: React.SyntheticEvent, newValue: string) => void;
}

const FilterTabs: React.FC<FilterTabsProps> = ({ tabKeys, activeTab, tabCounts, onTabChange }) => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                alignSelf: 'stretch',
                borderBottom: `1px solid ${theme.palette.text.disabled}`,
            }}
        >
            <Tabs
                value={activeTab}
                onChange={onTabChange}
                variant="scrollable"
                aria-label="Filter tabs"
                sx={{
                    flex: '1 0 0',
                    '& .MuiTabs-flexContainer': {
                        alignItems: 'center',
                        alignSelf: 'stretch',
                    },
                    [theme.breakpoints.up('md')]: {
                        variant: 'fullWidth',
                    },
                }}
                TabIndicatorProps={{
                    sx: {
                        backgroundColor: theme.palette.text.primary,
                        height: '1px',
                    },
                }}
            >
                {tabKeys.map(tab => (
                    <Tab
                        key={tab}
                        label={
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    fontSize: '12px',
                                    fontWeight: '600',
                                }}>
                                <span>{tab}</span>
                                {tabCounts[tab] > 0 && (
                                    <Box
                                        sx={{
                                            ml: 1,
                                            height: 18,
                                            minWidth: 18,
                                            backgroundColor: theme.palette.primary.main,
                                            borderRadius: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'white',
                                            fontSize: '11px',
                                            fontWeight: 'bold',
                                        }}
                                    >
                                        {tabCounts[tab]}
                                    </Box>
                                )}
                            </Box>
                        }
                        value={tab}
                        sx={{
                            height: '44px',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flex: '1 0 0',
                            background: theme.palette.common.white,
                            textTransform: 'none',
                            minWidth: 0,
                            color: theme.palette.text.disabled,
                            '&.Mui-selected': {
                                background: theme.elevation.paperElevationTwo,
                                '& .MuiTab-wrapper': {
                                    color: theme.palette.text.primary,
                                },
                            },
                        }}
                    />
                ))}
            </Tabs>
        </Box>
    );
};

export default FilterTabs;