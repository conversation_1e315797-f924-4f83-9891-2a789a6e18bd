import "./Outcomes.css";
import { Select, MenuItem } from "@mui/material";
import { useState } from "react";

interface OutcomesProps {
  outcomes: any[];
  selectedOutcome: any;
  onOutcomeClicked: (id: any) => void;
}

const Outcomes: React.FC<OutcomesProps> = ({
  outcomes,
  selectedOutcome,
  onOutcomeClicked,
}) => {

  const [outcome, setOutcome] = useState<any>(
    selectedOutcome?.outcome_tag_id !== undefined
      ? selectedOutcome.outcome_tag_id
      : outcomes?.length > 0
      ? outcomes[0]?.outcome_tag_id
      : undefined
  );
console.log(outcomes, "outcomes")
  const handleChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const selectedId = event.target.value;

    setOutcome(selectedId);
    onOutcomeClicked(selectedId);
  };

  return (
    <div className="outcomes container">
      <h4>
        <span style={{ marginRight: "4px", fontWeight: 'normal' }}>Outcome</span>
        <Select
          size="small"
          labelId="dropdown-label"
          id="dropdown"
          value={selectedOutcome?.outcome_tag_id || selectedOutcome}
          onChange={handleChange}
          label="Select an Option"
          style={{ width: "100%", maxWidth: "100%", fontSize: "0.8125rem" }}
        >
          {outcomes?.map((outcomeItem: any) => (
            <MenuItem
              key={outcomeItem?.outcome_tag_id}
              value={outcomeItem?.outcome_tag_id}
            >
              {outcomeItem?.label}
            </MenuItem>
          ))}
        </Select>
      </h4>
    </div>
  );
};

export default Outcomes;
