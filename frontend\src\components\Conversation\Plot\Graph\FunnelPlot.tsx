import React, { useState, useEffect, useRef } from "react";
import { Box } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import "./FunnelPlot.css";
import { NewForestPlotProps } from "./ForestPlotJWT.types";
import * as d3 from "d3";

const FunnelPlot = ({
  data,
  hoveredPair,
  outcomeHover,
  interventionHover,
  selectedOutcome,
  hoveredIntervention,
  onStudyPointerOver,
  onStudyPointerOut,
}: NewForestPlotProps) => {
  const theme = useTheme();
  const [activePanel, setActivePanel] = useState<string>("");
  const containerRef = useRef<HTMLDivElement>(null);
  const [svgWidth, setSvgWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  if (!data) {
    return null;
  }

  //   useEffect(() => {
  //     if(selectedOutcome) {
  //       setActivePanel(selectedOutcome)
  //     }
  //   }, [selectedOutcome])

  // useEffect(() => {
  //   if(!selectedOutcome) {
  //     if(plotData.length) {
  //       setActivePanel(plot.data[0].label)
  //     }
  //   }
  // }, [plotData])

  const studies = data
    .map((item) =>
      item.data.map((d) => ({
        ...d,
        intervention_id: item.intervention_id,
        outcome_id: item.outcome_id,
      }))
    )
    .flat()
    .filter((d) => d.outcome_id === selectedOutcome.id)
    .sort((a, b) => b.score.value - a.score.value);

  const margin = {
    top: 40,
    right: 40,
    bottom: 0,
    left: 40,
  };

  const STUDY_HEIGHT = 16;

  // const height = 800;
  const height = studies.length * STUDY_HEIGHT + margin.top + margin.bottom;

  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;
  const h: number = height - margin.top - margin.bottom;

  const xScale = d3
    .scaleLinear()
    .domain([
      Math.min(
        0,
        d3.min(studies, (d) => d.score.lower)
      ),
      Math.max(
        0,
        d3.max(studies, (d) => d.score.upper)
      ),
      // d3.min(data.map((d) => d.data.map((d) => d.score.lower)).flat()),
      // d3.max(data.map((d) => d.data.map((d) => d.score.upper)).flat()),
    ])
    .range([0, w])
    .nice();

  const cScale = d3
    .scaleLinear()
    .domain([-0.8, -0.5, -0.2, 0, 0.2, 0.5, 0.8])
    .range([
      "#8aaf5f",
      "#afc183",
      "#c7d6ae",
      "#ffffff",
      "#a7c6e1",
      "#7aabd2",
      "#4e8fc0",
    ])
    // .range(['#d87a8c', '#eca6a0', '#fad2c6', '#ffffff', '#6da7d1', '#5e9bc8', '#4e8fc0'])
    .clamp(true);

  console.log("DATA", data);

  const getOpacity = (study) => {
    return interventionHover === undefined && outcomeHover === undefined
      ? 1
      : interventionHover === study.intervention_id ||
        outcomeHover === study.outcome_id
      ? 0
      : 1;
  };

  return (
    <Box className="funnel-plot-box" ref={containerRef}>
      <svg width={svgWidth} height={height + margin.bottom + margin.top}>
        <defs>
          <linearGradient
            id={`gradient-${xScale.domain()[0]}-${xScale.domain()[1]}`}
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            {xScale.ticks(5).map((stop) => {
              return (
                <stop
                  offset={`${
                    ((stop - xScale.domain()[0]) /
                      (xScale.domain()[1] - xScale.domain()[0])) *
                    100
                  }%`}
                  stop-color={`${cScale(stop)}`}
                />
              );
            })}
          </linearGradient>
        </defs>
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          <line
            x1={xScale.range()[0]}
            y1={h}
            x2={xScale.range()[1]}
            y2={h}
            stroke="rgb(22 54 97)"
          />
          <line
            x1={xScale(0)}
            y1={h}
            x2={xScale(0)}
            y2={0}
            stroke="#ccc"
            strokeDasharray="2 2"
          />

          <text
            x={w / 2}
            y={h}
            dy={30}
            style={{
              fontSize: 12,
              textAnchor: "middle",
              fontWeight: "bold",
              fill: "rgb(22 54 97)",
            }}
          >
            effect size
          </text>
          {studies.map((study, i) => (
            <g
              transform={`translate(0, ${i * STUDY_HEIGHT})`}
              className="study"
              opacity={
                hoveredIntervention === undefined ||
                hoveredIntervention === study.intervention_id
                  ? 1
                  : 0.1
              }
            >
              <line
                x1={xScale(study.score.lower)}
                x2={xScale(study.score.upper)}
                stroke="rgb(22 54 97)"
              />
              <line
                x1={xScale(study.score.lower)}
                y1={-2}
                x2={xScale(study.score.lower)}
                y2={2}
                stroke="rgb(22 54 97)"
              />
              <line
                x1={xScale(study.score.upper)}
                y1={-2}
                x2={xScale(study.score.upper)}
                y2={2}
                stroke="rgb(22 54 97)"
              />
              <g
                onPointerOver={() => onStudyPointerOver(study)}
                onPointerOut={() => onStudyPointerOut()}
              >
                <circle
                  className="outline"
                  cx={xScale(study.score?.value)}
                  r={8}
                  fill={`${cScale(study.score.value)}`}
                  fillOpacity={0.7}
                  stroke="rgb(22 54 97)"
                />
                <circle
                  cx={xScale(study.score?.value)}
                  r={3}
                  fill="rgb(22 54 97)"
                  opacity={0.8}
                />
              </g>
            </g>
          ))}
          {xScale.ticks(5).map((tick) => (
            <text
              y={h}
              x={xScale(tick)}
              dominantBaseline="central"
              dy={10}
              textAnchor="middle"
              fontSize={10}
              fill="rgb(22 54 97)"
            >
              {tick}
            </text>
          ))}
        </g>
      </svg>
    </Box>
  );
};

export default FunnelPlot;
