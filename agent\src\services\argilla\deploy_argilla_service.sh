#!/bin/bash

# Load environment variables from .env file
ENV_FILE="$(dirname "$0")/.env"

if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env file not found at $ENV_FILE"
    echo "Please create a .env file with the required environment variables:"
    echo "OWNER_USERNAME=your_owner_username"
    echo "OWNER_PASSWORD=your_owner_password"
    echo "OWNER_API_KEY=your_owner_api_key"
    echo "ADMIN_USERNAME=your_admin_username"
    echo "ADMIN_PASSWORD=your_admin_password"
    echo "ADMIN_API_KEY=your_admin_api_key"
    echo "ANNOTATOR_USERNAME=your_annotator_username"
    echo "ANNOTATOR_PASSWORD=your_annotator_password"
    echo "ANNOTATOR_API_KEY=your_annotator_api_key"
    echo "ARGILLA_DATABASE_URL=your_database_url"
    exit 1
fi

# Source the .env file
source "$ENV_FILE"

# Validate required environment variables
required_vars=(
    "OWNER_USERNAME" "OWNER_PASSWORD" "OWNER_API_KEY"
    "ADMIN_USERNAME" "ADMIN_PASSWORD" "ADMIN_API_KEY"
    "ANNOTATOR_USERNAME" "ANNOTATOR_PASSWORD" "ANNOTATOR_API_KEY"
    "ARGILLA_DATABASE_URL"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "Error: Required environment variable $var is not set in .env file"
        exit 1
    fi
done

echo "✅ All required environment variables loaded from .env file"

gcloud run deploy argilla \
  --image=argilla/argilla-quickstart:latest \
  --region=us-central1 \
  --port=6900 \
  --cpu=2 \
  --memory=4Gi \
  --min-instances=1 \
  --max-instances=1 \
  --add-cloudsql-instances=impactai-430615:us-central1:argilla-db \
  --set-env-vars=OWNER_USERNAME="$OWNER_USERNAME",OWNER_PASSWORD="$OWNER_PASSWORD",OWNER_API_KEY="$OWNER_API_KEY" \
  --set-env-vars=ADMIN_USERNAME="$ADMIN_USERNAME",ADMIN_PASSWORD="$ADMIN_PASSWORD",ADMIN_API_KEY="$ADMIN_API_KEY" \
  --set-env-vars=ANNOTATOR_USERNAME="$ANNOTATOR_USERNAME",ANNOTATOR_PASSWORD="$ANNOTATOR_PASSWORD",ANNOTATOR_API_KEY="$ANNOTATOR_API_KEY" \
  --set-env-vars=ARGILLA_DATABASE_URL="$ARGILLA_DATABASE_URL" \
  --allow-unauthenticated \
  --set-env-vars=ARGILLA_AUTHENTICATION_MODE="standard",ARGILLA_ENABLE_SIGNUP="false"
