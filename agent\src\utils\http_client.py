"""Async HTTP client utilities for making external API calls."""

import httpx
from typing import Dict, Any


async def post_json(url: str, body: Dict[str, Any], headers: Dict[str, str] = None, timeout: float = 300.0):
    """
    Asynchronously posts JSON data to the specified URL with custom options.

    Args:
        url (str): The target URL
        body: The JSON-serializable data to send
        headers (dict, optional): Custom headers to include
        timeout (float, optional): Request timeout in seconds

    Returns:
        The JSON response from the server
    """
    default_headers = {"Content-Type": "application/json"}
    if headers:
        default_headers.update(headers)

    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.post(url, json=body, headers=default_headers)
        response.raise_for_status()
        return response.json()


async def get_json(url: str, params: Dict[str, Any] = None, timeout: float = 300.0):
    """
    Asynchronously sends a GET request to the specified URL and returns the JSON response.

    Args:
        url (str): The target URL to send the GET request to
        params (dict, optional): Query parameters to include in the request
        timeout (float, optional): Request timeout in seconds

    Returns:
        The JSON response from the server

    Raises:
        httpx.HTTPStatusError: If the request fails with an HTTP error
        httpx.RequestError: If there's a network-related error
    """
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(url, params=params)
        response.raise_for_status()  # Raises an exception for 4xx/5xx responses
        return response.json() 