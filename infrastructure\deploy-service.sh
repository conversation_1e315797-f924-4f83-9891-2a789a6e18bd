#!/bin/bash

# Get script directory for relative imports
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source shared libraries
source "$SCRIPT_DIR/lib/common.sh"
source "$SCRIPT_DIR/lib/gcloud.sh"
source "$SCRIPT_DIR/lib/deploy.sh"

# Initialize script with strict error handling
init_script

# Parse command line arguments
DRY_RUN=false
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help_header "$0" "Deploy backend service to Google Cloud Run"
            echo "Environment Variables:"
            echo "  TARGET       Required. Must be one of: development, testing, production"
            echo "  SERVICE      Required. Service directory name to deploy"
            echo ""
            echo "Description:"
            echo "  This script builds and deploys a backend service to Cloud Run with"
            echo "  database connectivity, VPC connector, and environment configuration."
            show_help_footer
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Configuration
readonly ENV_FILE="${SCRIPT_DIR}/env.${SERVICE}.tmp.yml"

# Custom cleanup function for this script
deployment_cleanup() {
    local exit_code=$?
    if [ -f "$ENV_FILE" ]; then
        rm -f "$ENV_FILE"
        log_info "Cleaned up temporary environment file"
    fi
    if [ $exit_code -ne 0 ]; then
        log_error "Service deployment failed with exit code $exit_code"
    fi
    exit $exit_code
}

# Set up cleanup trap
setup_cleanup deployment_cleanup

# Function to fetch environment configuration
fetch_environment_config() {
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] Would fetch environment configuration for $TARGET"
        log_success "[DRY RUN] Environment config fetch validation completed"
        return 0
    fi
    
    log_info "Fetching environment configuration..."
    
    # Use the existing check-env.sh script to fetch configuration
    if TARGET="$TARGET" SERVICE="$SERVICE" "$SCRIPT_DIR/check-env.sh"; then
        log_success "Environment configuration fetched successfully"
        return 0
    else
        log_error "Failed to fetch environment configuration"
        return 1
    fi
}

# Main execution
main() {
    if [ "$DRY_RUN" = true ]; then
        log_info "Starting service deployment validation (DRY RUN MODE)"
    else
        log_info "Starting service deployment to Google Cloud Run"
    fi
    
    log_info "Target: ${TARGET:-'<not set>'}"
    log_info "Service: ${SERVICE:-'<not set>'}"
    
    # Validation and pre-flight checks
    validate_deployment_environment
    check_gcloud_auth
    
    # Create service and image names
    local full_service_name="impactai-$SERVICE-$TARGET"
    local image_tag
    image_tag=$(create_image_tag "$full_service_name")
    
    log_info "Full service name: $full_service_name"
    log_info "Image tag: $image_tag"
    
    # Fetch environment configuration
    if ! fetch_environment_config; then
        exit 1
    fi
    
    # Build and push image
    if ! build_and_push_image "$SERVICE" "$image_tag" "$DRY_RUN"; then
        exit 1
    fi
    
    # Deploy backend service
    if ! deploy_backend_service "$full_service_name" "$image_tag" "$ENV_FILE" "$TARGET" "$DRY_RUN"; then
        exit 1
    fi
    
    if [ "$DRY_RUN" = true ]; then
        log_success "Service deployment validation completed successfully!"
    else
        log_success "Service deployment completed successfully!"
        log_info "Deployed image: $image_tag"
    fi
}

# Run main function
main "$@"
