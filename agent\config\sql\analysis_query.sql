SELECT
   P.id AS paper_id,
   P.doi_url AS paper_doi_url,
   P.citation AS paper_citation,
   P.title AS paper_title,
   P.country as country,
   P.author_name as author_name,
   P.year as year,
   T_I_T.tag_label AS intervention_tag_label,
   T_I_T.id AS intervention_tag_id,
   T_I_G.tag_label AS intervention_group_label,
   O.direction AS direction,
   T_O_T.tag_label AS outcome_tag_label,
   T_O_T.id AS outcome_tag_id,
   T_O_G.tag_label AS outcome_group_label,
   E.treatment_effect,
   E.precision_value
FROM
   Papers AS P
INNER JOIN
   Interventions AS I ON P.id = I.paper_id
INNER JOIN
   Outcomes AS O ON I.id = O.Intervention_id
INNER JOIN
   Estimates AS E ON O.id = E.outcome_id
INNER JOIN
   Taxonomy AS T_I_T ON I.tag_id = T_I_T.id
INNER JOIN
   Taxonomy AS T_I_G ON I.tag_group_id = T_I_G.id
INNER JOIN
   Taxonomy AS T_O_T ON O.tag_id = T_O_T.id
INNER JOIN
   Taxonomy AS T_O_G ON O.tag_group_id = T_O_G.id
WHERE
   :table_name.tag_label IN (SELECT tag_label FROM Taxonomy WHERE id = :tag_id);
