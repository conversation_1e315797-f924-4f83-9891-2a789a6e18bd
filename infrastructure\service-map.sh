#!/bin/bash

# Service map configuration for causal-ai-product
# This file reads service configuration from services.yml using grep/sed

# Function to get service URL from services.yml
get_service_url() {
    local service_name="$1"
    local use_domain="${2:-false}"
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local project_root="$(cd "$script_dir/.." && pwd)"
    local services_file="$project_root/services.yml"
    
    if [ ! -f "$services_file" ]; then
        echo "Error: services.yml not found at $services_file"
        exit 1
    fi
    
    if [ "$use_domain" = "true" ]; then
        grep -A 10 "^  $service_name:" "$services_file" | grep "domain:" | sed 's/.*domain: "\(.*\)"/\1/'
    else
        grep -A 10 "^  $service_name:" "$services_file" | grep "localhost:" | sed 's/.*localhost: "\(.*\)"/\1/'
    fi
}

# Function to get service name from services.yml
get_service_name() {
    local service_name="$1"
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local project_root="$(cd "$script_dir/.." && pwd)"
    local services_file="$project_root/services.yml"
    
    if [ ! -f "$services_file" ]; then
        echo "Error: services.yml not found at $services_file"
        exit 1
    fi
    
    grep -A 10 "^  $service_name:" "$services_file" | grep "name:" | sed 's/.*name: "\(.*\)"/\1/'
}

# Function to get service description from services.yml
get_service_description() {
    local service_name="$1"
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local project_root="$(cd "$script_dir/.." && pwd)"
    local services_file="$project_root/services.yml"
    
    if [ ! -f "$services_file" ]; then
        echo "Error: services.yml not found at $services_file"
        exit 1
    fi
    
    grep -A 10 "^  $service_name:" "$services_file" | grep "description:" | sed 's/.*description: "\(.*\)"/\1/'
}

# Function to list all service URLs
list_service_urls() {
    local use_domain="${1:-false}"
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local project_root="$(cd "$script_dir/.." && pwd)"
    local services_file="$project_root/services.yml"
    
    if [ ! -f "$services_file" ]; then
        echo "Error: services.yml not found at $services_file"
        exit 1
    fi
    
    echo "Service Access URLs:"
    echo ""
    
    # Define services manually since parsing is complex
    local services="backend frontend agent caddy"
    
    for service in $services; do
        local url=$(get_service_url "$service" "$use_domain")
        local name=$(get_service_name "$service")
        echo "  $name: $url"
    done
    echo ""
}

# Function to list all services with descriptions
list_services() {
    local script_dir="$(dirname "${BASH_SOURCE[0]}")"
    local project_root="$(cd "$script_dir/.." && pwd)"
    local services_file="$project_root/services.yml"
    
    if [ ! -f "$services_file" ]; then
        echo "Error: services.yml not found at $services_file"
        exit 1
    fi
    
    echo "Services (from .honcho.yml):"
    echo ""
    
    # Define services manually since parsing is complex
    local services="backend frontend agent caddy"
    
    # Find the longest service name for alignment
    local max_name_length=0
    for service in $services; do
        local name=$(get_service_name "$service")
        local name_length=${#name}
        if [ $name_length -gt $max_name_length ]; then
            max_name_length=$name_length
        fi
    done
    
    # Display services in a beautiful format
    for service in $services; do
        local name=$(get_service_name "$service")
        local description=$(get_service_description "$service")
        local localhost_url=$(get_service_url "$service")
        local domain_url=$(get_service_url "$service" true)
        
        # Pad the name to align columns
        local padded_name=$(printf "%-${max_name_length}s" "$name")
        
        echo "  📦 $padded_name"
        echo "     Description: $description"
        echo "     Local:       $localhost_url"
        echo "     Domain:      $domain_url"
        echo ""
    done
}

# Export functions for use in other scripts
export -f get_service_url
export -f get_service_name
export -f get_service_description
export -f list_service_urls
export -f list_services 