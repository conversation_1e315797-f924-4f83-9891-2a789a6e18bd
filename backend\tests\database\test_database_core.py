from unittest.mock import AsyncMock, Mock

import pytest
from database.core import Async<PERSON><PERSON><PERSON><PERSON>ocal, engine, get_core_db_session


@pytest.fixture
def mock_session():
    session = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    return session


@pytest.mark.asyncio
async def test_get_core_db_session_success(monkeypatch, mock_session):
    """Test successful session creation and cleanup"""
    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: mock_session)

    async with get_core_db_session() as session:
        assert session is mock_session

    mock_session.close.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_core_db_session_exception_handling(monkeypatch, mock_session):
    """Test session rollback and cleanup when exception occurs"""
    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: mock_session)

    test_exception = Exception("Test error")

    with pytest.raises(Exception, match="Test error"):
        async with get_core_db_session() as session:
            assert session is mock_session
            raise test_exception

    mock_session.rollback.assert_awaited_once()
    mock_session.close.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_core_db_session_none_session(monkeypatch):
    """Test graceful handling when session is None"""
    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: None)

    async with get_core_db_session() as session:
        assert session is None

    # No assertions on rollback/close since session is None


@pytest.mark.asyncio
async def test_get_core_db_session_exception_with_none_session(monkeypatch):
    """Test exception handling when session is None"""
    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: None)

    test_exception = Exception("Test error")

    with pytest.raises(Exception, match="Test error"):
        async with get_core_db_session() as session:
            assert session is None
            raise test_exception

    # No assertions on rollback/close since session is None


def test_database_engine_configuration():
    """Test database engine configuration parameters"""
    # AsyncEngine stores pool configuration differently
    pool = engine.pool
    assert pool.size() == 20  # Check actual pool size
    assert pool._max_overflow == 20
    assert pool._pre_ping is True
    assert hasattr(engine, "execution_options")
    # Basic assertions that the engine is properly configured
    assert engine is not None
    assert pool is not None


def test_async_session_configuration():
    """Test AsyncSessionLocal configuration"""
    # Check session factory configuration
    assert AsyncSessionLocal.kw["autocommit"] is False
    assert AsyncSessionLocal.kw["autoflush"] is False
    assert AsyncSessionLocal.kw["expire_on_commit"] is False


@pytest.mark.asyncio
async def test_session_lifecycle_integration(monkeypatch, caplog):
    """Test complete session lifecycle with logging"""
    mock_session = AsyncMock()
    mock_session.close = AsyncMock()

    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: mock_session)

    with caplog.at_level("DEBUG"):
        async with get_core_db_session() as session:
            assert session is mock_session
            # Simulate some database operation
            await session.execute("SELECT 1")

    mock_session.close.assert_awaited_once()
    assert "Session closed, connection returned to pool" in caplog.text


@pytest.mark.asyncio
async def test_session_with_database_operation(monkeypatch, mock_session):
    """Test session with simulated database operation"""
    monkeypatch.setattr("database.core.AsyncSessionLocal", lambda: mock_session)

    mock_result = Mock()
    mock_session.execute.return_value = mock_result

    async with get_core_db_session() as session:
        result = await session.execute("SELECT * FROM test")
        assert result is mock_result

    mock_session.execute.assert_awaited_once()
    mock_session.close.assert_awaited_once()
