# Real-time Progress Status System

The agent provides intelligent, real-time status updates to keep users informed during query processing. This system is essential for complex research questions that can take 30-60 seconds to complete.

## Overview

### The Problem

Research queries can be complex and time-consuming. Without progress updates, users experience:
- **Black box processing**: No visibility into what the agent is doing
- **Uncertainty**: Users don't know if the system is working or stuck
- **Poor user experience**: Long wait times without feedback

### The Solution

The progress status system provides:
- **Real-time updates**: Users see exactly what step the agent is executing
- **Contextual messages**: Status updates reference the user's specific question
- **Intelligent descriptions**: AI-generated messages that are natural and informative
- **Cross-instance tracking**: Works even when processing moves between server instances

## System Architecture

### Component Overview

```mermaid
graph TB
    subgraph "Agent Execution"
        A[Agent] --> B[Tool Manager]
        B --> C[Pipeline Tools]
        C --> D[AgentCache]
    end
    
    subgraph "Progress Tracking"
        D --> E[Redis Lists]
        E --> F[ProgressMonitor]
        F --> G[LLM Status Generator]
        G --> H[Status Messages]
    end
    
    subgraph "Frontend Integration"
        H --> I[Server-Sent Events]
        I --> J[Real-time UI Updates]
    end
    
    style D fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#e8f5e8
```

### Detailed Execution Flow

```mermaid
graph TD
    A["User Submits Query<br/>What are the effects of cash transfers?"] --> B[Agent Starts Processing]
    
    B --> C[Initialize AgentCache & ProgressMonitor]
    C --> D[Pipeline Execution Begins]
    
    D --> E[Tool 1: Entity Extractor Starts]
    E --> F["Log Step to Redis<br/>tool_name: entity_extractor<br/>status: started"]
    F --> G[ProgressMonitor Detects New Step]
    G --> H["Generate Status Message<br/>Understanding your question about cash transfers"]
    H --> I[Send Update to Frontend]
    
    I --> J[Entity Extractor Completes]
    J --> K["Log Step to Redis<br/>tool_name: entity_extractor<br/>status: finished<br/>data: {interventions, outcomes}"]
    K --> L[ProgressMonitor Detects Completion]
    L --> M["Generate Status Message<br/>Found 2 concepts: cash transfers, education"]
    M --> N[Send Update to Frontend]
    
    N --> O[Tool 2: SQL Generator Starts]
    O --> P["Log Step to Redis<br/>tool_name: sql_generator<br/>status: started"]
    P --> Q[ProgressMonitor Detects New Step]
    Q --> R["Generate Status Message<br/>Searching studies database for cash transfer research"]
    R --> S[Send Update to Frontend]
    
    S --> T[SQL Generator Completes]
    T --> U["Log Step to Redis<br/>tool_name: sql_generator<br/>status: finished<br/>data: {paper_ids: [123, 456, 789]}"]
    U --> V[ProgressMonitor Detects Completion]
    V --> W["Generate Status Message<br/>Found 3 studies about cash transfer effects"]
    W --> X[Send Update to Frontend]
    
    X --> Y{More Tools?}
    Y -->|Yes| Z[Next Tool Execution]
    Z --> AA[Repeat Process for Each Tool]
    AA --> Y
    
    Y -->|No| BB[Final Answer Generation]
    BB --> CC["Log Step to Redis<br/>tool_name: final_answer_generator<br/>status: started"]
    CC --> DD["Generate Status Message<br/>Preparing comprehensive answer on cash transfers"]
    DD --> EE[Send Final Update to Frontend]
    EE --> FF[Agent Completes - Return Final Answer]
    
    subgraph "Redis Storage"
        GG["agent_execution_steps:conv_123<br/>[step1, step2, step3, ...]"]
    end
    
    subgraph "Status Generation System"
        HH[LLM Prompt System]
        II[Context Variables]
        JJ[Tool-Specific Rules]
        KK[Natural Language Output]
        
        HH --> II
        II --> JJ
        JJ --> KK
    end
    
    subgraph "Frontend Integration"
        LL[Server-Sent Events]
        MM[Real-time UI Updates]
        NN[Progress Indicators]
        
        LL --> MM
        MM --> NN
    end
    
    F --> GG
    K --> GG
    P --> GG
    U --> GG
    CC --> GG
    
    G --> HH
    L --> HH
    Q --> HH
    V --> HH
    DD --> HH
    
    I --> LL
    N --> LL
    S --> LL
    X --> LL
    EE --> LL
    
    style F fill:#e1f5fe
    style K fill:#e1f5fe
    style P fill:#e1f5fe
    style U fill:#e1f5fe
    style CC fill:#e1f5fe
    
    style H fill:#f3e5f5
    style M fill:#f3e5f5
    style R fill:#f3e5f5
    style W fill:#f3e5f5
    style DD fill:#f3e5f5
    
    style I fill:#e8f5e8
    style N fill:#e8f5e8
    style S fill:#e8f5e8
    style X fill:#e8f5e8
    style EE fill:#e8f5e8
```

### Data Flow

1. **Execution Step Logging**: When tools start/finish, execution steps are logged to Redis
2. **Progress Monitoring**: ProgressMonitor watches for new steps and detects changes
3. **Status Generation**: LLM analyzes steps and generates natural language descriptions
4. **Real-time Updates**: Status messages are sent to frontend via Server-Sent Events

## Core Components

### 1. AgentCache Execution Logging

**Location**: `src/agent/models.py`

The `AgentCache` class automatically logs each tool execution step:

```python
def log_tool_execution(
    self, conversation_id: str, tool_name: str, status: str, data: Dict[str, Any] = None
) -> None:
    """Log tool execution step with standardized format."""
    step_data = {
        'tool_name': tool_name,
        'status': status,  # "started" or "finished"
        'executed_at': datetime.now().isoformat(),
        'data': data
    }
    
    # Store as Redis list for atomic operations
    key = f"agent_execution_steps:{conversation_id}"
    self.redis_cache.rpush_json(key, serialized_data)
```

**Key Features**:
- **Atomic Operations**: Uses Redis RPUSH for thread-safe logging
- **Automatic Timestamps**: Each step includes execution time
- **Bounded Growth**: Automatically trims lists to prevent memory issues
- **Fallback Support**: Works without Redis using in-memory storage

### 2. ProgressMonitor Service

**Location**: `src/services/agent.py`

The `ProgressMonitor` class watches for new execution steps and generates status updates:

```python
class ProgressMonitor:
    """Monitors agent execution progress and emits SSE events for status updates."""
    
    def __init__(self, cache: Cache, conversation_id: str, query: str = None):
        self.cache = cache
        self.conversation_id = conversation_id
        self.query = query
        self.last_seen_count = 0
        self.seen_tools = {}  # Track tool repeat runs
        
    async def check_for_updates(self):
        """Check for new execution steps and return status update if found."""
        # Get new steps since last check
        current_count = await asyncio.to_thread(self.cache.llen, self.execution_key)
        
        if current_count <= self.last_seen_count:
            return None
            
        # Fetch only new steps for efficiency
        new_steps = await asyncio.to_thread(
            self.cache.lrange_json, self.execution_key, self.last_seen_count, -1
        )
        
        # Generate status message for latest step
        return await self._generate_status_message(new_steps[-1])
```

**Key Features**:
- **Efficient Polling**: Only fetches new steps since last check
- **Async Operations**: Non-blocking Redis operations using thread offloading
- **Repeat Detection**: Tracks when tools run multiple times for refined language
- **Fallback Messages**: Provides static messages if LLM generation fails

### 3. Smart Status Message Generation

Status messages are generated using a specialized LLM prompt that creates natural, contextual descriptions.

#### LLM Prompt Integration

**Location**: `config/prompts/status_message_generator.prompt`

The prompt incorporates:
- **User's Original Question**: Provides context for natural language
- **Tool Execution Data**: Extracts key entities and information
- **Repeat Run Detection**: Uses different language for first vs. subsequent runs
- **Length Constraints**: Ensures messages are ≤100 characters

#### Example Status Generations

**Entity Extraction Tool**:
```
Input: {"tool_name": "entity_extractor", "status": "started", "data": {"query": "What are the effects of cash transfers on education?"}}
Output: "Understanding your question about cash transfer effects on education"
```

**SQL Generator Tool (Repeat Run)**:
```
Input: {"tool_name": "sql_generator", "status": "finished", "is_repeat_run": true, "data": {"paper_ids": ["123", "456", "789"]}}
Output: "Found 3 additional studies about development economics interventions"
```

### 4. Frontend Integration

The system is designed for seamless frontend integration:

#### Server-Sent Events (SSE)

```javascript
// Frontend JavaScript example
const eventSource = new EventSource('/api/progress/conv_123');

eventSource.onmessage = function(event) {
    const status = JSON.parse(event.data);
    updateProgressUI(status.title, status.status);
};

function updateProgressUI(message, status) {
    document.getElementById('status-message').textContent = message;
    document.getElementById('status-indicator').className = `status-${status}`;
}
```

#### JSON Response Format

```json
{
    "title": "Understanding your question about cash transfer effectiveness",
    "status": "started"
}
```

## Redis Data Structures

### Execution Steps Storage

**Key Pattern**: `agent_execution_steps:{conversation_id}`  
**Data Type**: Redis List (ordered, append-only)

```json
{
  "tool_name": "entity_extractor",
  "status": "started",
  "executed_at": "2024-12-01T14:30:22.123456",
  "data": {
    "query": "What are the effects of cash transfers?",
    "user_intent": "causal_impact"
  }
}
```

### List Operations

- **RPUSH**: Append new execution steps (atomic)
- **LRANGE**: Retrieve steps for monitoring  
- **LLEN**: Get total step count
- **LTRIM**: Prevent unbounded growth

## Usage Examples

### Basic Integration

```python
from src.services.agent import ProgressMonitor
from src.services.cache import Cache

# Initialize progress monitoring
cache = Cache()
monitor = ProgressMonitor(
    cache=cache,
    conversation_id="conv_123",
    query="What are the effects of cash transfers on education?"
)

# Check for updates (typically called in a loop or async task)
status_update = await monitor.check_for_updates()

if status_update:
    print(f"Status: {status_update['title']} ({status_update['status']})")
```

### SSE Endpoint Implementation

```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse

app = FastAPI()

@app.get("/api/progress/{conversation_id}")
async def progress_stream(conversation_id: str):
    async def event_generator():
        monitor = ProgressMonitor(cache, conversation_id)
        
        while True:
            update = await monitor.check_for_updates()
            if update:
                yield f"data: {json.dumps(update)}\n\n"
            await asyncio.sleep(1)  # Poll every second
    
    return StreamingResponse(
        event_generator(), 
        media_type="text/plain",
        headers={"Cache-Control": "no-cache"}
    )
```

## Configuration

### Environment Variables

```bash
# Enable Redis for progress tracking
REDIS_CACHE_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM configuration for status generation
GOOGLE_API_KEY=your_api_key
```

### Fallback Behavior

The system gracefully degrades:
- **No Redis**: Uses in-memory storage (no cross-instance tracking)
- **No LLM**: Uses static fallback messages
- **Network Issues**: Continues with cached status messages

## Troubleshooting

### Common Issues

1. **No Status Updates Appearing**
   ```bash
   # Check Redis connection
   redis-cli ping
   
   # Verify execution steps are being logged
   redis-cli keys "agent_execution_steps:*"
   ```

2. **Generic Status Messages**
   ```bash
   # Check LLM API key
   echo $GOOGLE_API_KEY
   
   # Verify prompt file exists
   ls agent/config/prompts/status_message_generator.prompt
   ```

3. **Memory Growth in Redis**
   ```bash
   # Check list lengths
   redis-cli llen "agent_execution_steps:conv_123"
   
   # Manually trim if needed
   redis-cli ltrim "agent_execution_steps:conv_123" -100 -1
   ```

### Debugging

Enable verbose logging to see progress tracking in action:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or set environment variable
export LOG_LEVEL=DEBUG
```

### Performance Considerations

- **Redis Memory**: Each conversation stores ~1000 execution steps max
- **LLM Calls**: One API call per status update (typically 5-10 per query)
- **Network**: SSE connections are lightweight, ~1KB per update
- **Latency**: Status updates appear within 1-2 seconds of execution

## Best Practices

1. **Conversation Cleanup**: Implement TTL for old execution steps
2. **Error Handling**: Always provide fallback messages
3. **Rate Limiting**: Limit SSE connection frequency
4. **Monitoring**: Track Redis memory usage and LLM API costs
5. **Testing**: Test with Redis disabled to ensure fallback works

## Future Enhancements

- **Progress Percentages**: Calculate completion percentage based on pipeline steps
- **Estimated Time**: Predict remaining time based on historical performance
- **Rich Messages**: Include more detailed information like data counts
- **WebSocket Support**: Alternative to SSE for bidirectional communication
