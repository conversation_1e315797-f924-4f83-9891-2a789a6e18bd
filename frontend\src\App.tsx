import { useState } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { TransitionGroup, CSSTransition } from 'react-transition-group';
import Layout from './components/Layout/Layout';
import { MobileProvider } from './components/Layout/MobileContext';
import Conversation from './components/Conversation/Conversation';
import Settings from './components/Settings/Settings';
import AuthPage from './components/Auth/AuthPage';
import ProtectedRoute from "./components/Auth/ProtectedRoute";
import lightTheme from './theme/presets/light';
import { SnackbarProvider } from './components/Common/Snackbar/SnackbarContext';
import { LayoutProvider } from './components/Layout/LayoutContext';
import NotFound from './components/Common/NotFound';
import { Message } from './types/ConversationTypes';

function App() {
  const [conversationId, setConversationId] = useState<string>('');
  const [centeredContent, setCenteredContent] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | ''>('');
  const [refreshChatHistory, setRefreshChatHistory] = useState<boolean | ''>(false);
  const [appLoader, setAppLoader] = useState(false);
  const [isStreamingContent, setIsStreamingContent] = useState(false);
  const [activeMessageInfo, setActiveMessageInfo] = useState<Message | null>(null);
  const [messageList, setMessageList] = useState<Message[]>([]);
  const [activeMessageStreamingEnded, setActiveMessageStreamingEnded] = useState(false);
  const [streamingError, setStreamingError] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true);
  const [userExpandedSidebar, setUserExpandedSidebar] = useState(false);

  const updateConversationId = (conversationId: string) => {
    setConversationId(conversationId);
  };

  const updateErrorMessageState = (value: string) => {
    setErrorMessage(value);
  };

  const updateCenteredContentState = (value: boolean) => {
    setCenteredContent(value);
  };

  const updateRefreshChatHistory = (value: boolean | '') => {
    setRefreshChatHistory(value);
  };

  const updateAppLoaderState = (value: boolean) => {
    setAppLoader(value);
  };

  const updateIsStreamingContent = (value: boolean) => {
    setIsStreamingContent(value);
  };

  const updateActiveMessageInfo = (value: Message | null) => {
    setActiveMessageInfo(value);
  };

  const updateMessageList = (messages: Message[]) => {
    setMessageList(messages);
  };

  const updateActiveMessageStreamingEnded = (value: boolean) => {
    setActiveMessageStreamingEnded(value);
  };

  const updateStreamingErrorState = (flag: boolean) => {
    setStreamingError(flag);
  };

  const toggleSidebarCollapsed = (isUserExplicitAction: boolean = true) => {
    setIsSidebarCollapsed(prevCollapsed => {
      const newCollapsedState = !prevCollapsed;
      if (newCollapsedState === false) {
        setUserExpandedSidebar(isUserExplicitAction);
      } else {
        setUserExpandedSidebar(false);
      }
      return newCollapsedState;
    });
  };

  return (
    <ThemeProvider theme={lightTheme}>
      <Router>
        <MobileProvider>
          <LayoutProvider value={{
            centeredContent: centeredContent,
            updateCenteredContentState: updateCenteredContentState,
            errorMessage: errorMessage,
            updateErrorMessageState: updateErrorMessageState,
            refreshChatHistory: refreshChatHistory,
            updateRefreshChatHistory: updateRefreshChatHistory,
            conversationId: conversationId,
            updateConversationId: updateConversationId,
            appLoader: appLoader,
            updateAppLoaderState: updateAppLoaderState,
            isStreamingContent: isStreamingContent,
            updateIsStreamingContent: updateIsStreamingContent,
            activeMessageInfo: activeMessageInfo,
            updateActiveMessageInfo: updateActiveMessageInfo,
            messageList: messageList,
            updateMessageList: updateMessageList,
            activeMessageStreamingEnded: activeMessageStreamingEnded,
            updateActiveMessageStreamingEnded: updateActiveMessageStreamingEnded,
            streamingError: streamingError,
            updateStreamingErrorState: updateStreamingErrorState,
            isSidebarCollapsed: isSidebarCollapsed,
            toggleSidebarCollapsed: toggleSidebarCollapsed,
            setIsSidebarCollapsed: setIsSidebarCollapsed,
            userExpandedSidebar: userExpandedSidebar,
            setUserExpandedSidebar: setUserExpandedSidebar,
          }}>
            <Routes>
              <Route path="/auth/*" element={<AuthPage />} />
              <Route element={<ProtectedRoute />}>
                <Route
                  path="*"
                  element={
                    <Layout>
                      <TransitionGroup>
                        <CSSTransition classNames="fade" timeout={300}>
                          <Routes>
                            <Route
                              path=":conversationId?"
                              element={
                                <SnackbarProvider>
                                  <Conversation />
                                </SnackbarProvider>
                              }
                            />
                            <Route
                              path="settings"
                              element={<Settings updateRefreshChatHistory={updateRefreshChatHistory} />}
                            />
                            <Route path="*" element={<NotFound />} />
                          </Routes>
                        </CSSTransition>
                      </TransitionGroup>
                    </Layout>
                  }
                />
              </Route>
            </Routes>
          </LayoutProvider>
        </MobileProvider>
      </Router>
    </ThemeProvider>
  );
}

export default App;