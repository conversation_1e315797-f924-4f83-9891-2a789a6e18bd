"""Settings and configuration for the methodology pipeline."""

from dataclasses import dataclass, field
from pathlib import Path
from typing import List


@dataclass
class MethodologySettings:
    """Settings for the methodology pipeline and related tools."""

    # Documentation paths
    DOCS_DIR: Path = Path("docs/methodology")

    # Methodology document files
    METHODOLOGY_FILES: List[str] = field(
        default_factory=lambda: [
            "ImpactAI - Appendix A - Effect size standardization.md",
            "ImpactAI - Appendix B - Construction of quality score.md",
            "ImpactAI - Appendix C - Model choice.md",
        ]
    )

    # Prompt configuration
    PROMPT_TEMPLATE_PATH: str = "config/prompts/methodology.prompt"

    # Logging format
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    @property
    def full_methodology_paths(self) -> List[Path]:
        """Get full paths to methodology files."""
        return [self.DOCS_DIR / filename for filename in self.METHODOLOGY_FILES]

    @property
    def prompt_path(self) -> Path:
        """Get full path to prompt template."""
        return Path(self.PROMPT_TEMPLATE_PATH)
