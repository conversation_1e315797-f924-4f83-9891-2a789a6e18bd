from pydantic import BaseModel
from fastapi import APIRouter
import uuid
from services.authentication import jwt_encode
from services.authentication import (
    create_user,
    authenticate,
    handle_forgot_password,
    handle_change_password,
    AuthenticationError,
)
from services.cache import Cache
from utils.measure import measure_async_time

router = APIRouter()

cache = Cache()


class LoginBody(BaseModel):
    email: str
    password: str


class RegisterBody(BaseModel):
    email: str
    password: str


class ForgotPasswordBody(BaseModel):
    email: str


class ChangePasswordBody(BaseModel):
    token: str
    password: str


def generate_token(user_id: str):
    return jwt_encode({"user_id": str(user_id)})


def generate_user_id(email: str):
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, email))


@router.post("/auth/login")
@measure_async_time
async def post_auth_login(body: LoginBody):
    """Performs login using credentials supplied in body"""
    try:
        authed_user = await authenticate(body.email, body.password)
        return {
            "success": True,
            "data": {
                "token": generate_token(authed_user.id),
                "user": {
                    "id": authed_user.id,
                    "email": authed_user.email,
                    "created_at": authed_user.created_at,
                },
            },
        }
    except AuthenticationError as e:
        return {
            "success": False,
            "data": {
                "message": str(e.detail),
            },
        }


@router.post("/auth/register")
@measure_async_time
async def post_auth_register(body: RegisterBody):
    """Performs registration using credentials supplied in body"""

    try:
        created_user = await create_user(body.email, body.password)
        return {
            "success": True,
            "data": {
                "token": generate_token(created_user.id),
                "user": {
                    "id": created_user.id,
                    "email": created_user.email,
                    "created_at": created_user.created_at,
                },
            },
        }
    except AuthenticationError as e:
        return {
            "success": False,
            "data": {
                "message": str(e.detail),
            },
        }


@router.post("/auth/forgot-password")
@measure_async_time
async def post_auth_forgot_password(body: ForgotPasswordBody):
    """Performs forgot password flow using email in body"""
    try:
        await handle_forgot_password(body.email)
        return {
            "success": True,
        }
    except AuthenticationError as e:
        return {
            "success": False,
            "data": {
                "message": str(e.detail),
            },
        }


@router.post("/auth/change-password")
@measure_async_time
async def post_auth_change_password(
    body: ChangePasswordBody,
):
    """Performs change password flow using email and token in body"""
    try:
        user = await handle_change_password(body.token, body.password)
        return {
            "success": True,
            "data": {
                "token": generate_token(user.id),
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "created_at": user.created_at,
                },
            },
        }
    except AuthenticationError as e:
        return {
            "success": False,
            "data": {
                "message": str(e.detail),
            },
        }
