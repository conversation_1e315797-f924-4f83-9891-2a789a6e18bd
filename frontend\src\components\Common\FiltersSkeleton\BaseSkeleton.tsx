import {
    Box,
    Card,
    Grid,
    IconButton,
    Skeleton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import FilterListAltIcon from '@mui/icons-material/FilterListAlt';

const BaseSkeleton = ({ hideFiltersPanel, onClose, theme, rightHeaderContent, rightBodyContent, showFilterIcon }) => {
    return (
        <Card
            elevation={0}
            sx={{
                width: '100%',
                height: '100%',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: '8px',
                display: 'flex',
                flexDirection: 'column',
                maxHeight: "100%",
                background: theme.palette.common.white,
            }}
        >
            <Grid container spacing={0} sx={{ height: '100%', flexGrow: 1 }}>
                {!hideFiltersPanel && (
                    <Grid
                        size={{ xs: 12, md: 4 }}
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            height: '100%',
                            overflow: 'hidden',
                            borderRight: { xs: 'none', md: `1px solid ${theme.palette.divider}` },
                            borderBottom: { xs: `1px solid ${theme.palette.divider}`, md: 'none' },
                        }}
                    >
                        {/* Skeleton for FilterHeader */}
                        <Box
                            sx={{
                                p: 1.5,
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box
                                    sx={{
                                        width: '36px',
                                        height: '36px',
                                        padding: '8px',
                                        borderRadius: '4px',
                                        background: '#F2F6FC',
                                        aspectRatio: '1/1',
                                    }}
                                />
                            </Box>
                        </Box>
                        {/* Skeleton for FiltersPanel content */}
                        <Box sx={{ flexGrow: 1, p: 1.5 }}>
                            <Skeleton variant="rectangular" sx={{ width: '100%', height: '100%' }} />
                        </Box>
                    </Grid>
                )}

                <Grid
                    size={{ xs: 12, md: hideFiltersPanel ? 12 : 8 }}
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                        overflow: 'hidden',
                    }}
                >
                    <Box
                        sx={{
                            p: 2,
                            background: theme.palette.common.white,
                            zIndex: 1,
                            flexShrink: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            gap: 1,
                        }}
                    >
                        {showFilterIcon && (
                            <IconButton size="small" disabled>
                                <FilterListAltIcon />
                            </IconButton>
                        )}
                        {rightHeaderContent}
                        <IconButton size="small" onClick={onClose} disabled>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    {rightBodyContent}
                </Grid>
            </Grid>
        </Card>
    );
};

export default BaseSkeleton;