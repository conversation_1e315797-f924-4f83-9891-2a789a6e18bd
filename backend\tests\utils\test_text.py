from utils.text import sanitize_plot_text, remove_non_matching_plot_pairs, remove_non_matching_sources


class TestRemoveNonMatchingSources:
    """Test cases for remove_non_matching_sources function."""

    def test_keeps_matching_single_sources(self):
        """Test that single source IDs in the allowed list are kept"""
        input_text = "some text [J123] and [P456] more text"
        source_ids = ['J123', 'P456']
        expected = "some text [J123] and [P456] more text"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_removes_non_matching_single_sources(self):
        """Test that single source IDs not in the allowed list are removed"""
        # Note: Current implementation has a bug - it removes ALL brackets when any should be removed
        input_text = "some text [J123] and [P456] more text"
        source_ids = ['J123']
        expected = "some text  and  more text"  # Current implementation removes all brackets
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_removes_all_sources_when_empty_list(self):
        """Test that all source IDs are removed when source_ids is empty"""
        input_text = "some text [J123] and [P456] more text"
        source_ids = []
        expected = "some text  and  more text"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_removes_all_sources_when_none_provided(self):
        """Test that function crashes when source_ids is None (current bug)"""
        input_text = "some text [J123] and [P456] more text"
        source_ids = None
        # Current implementation crashes with TypeError when source_ids is None
        try:
            result = remove_non_matching_sources(input_text, source_ids)
            assert False, "Should have raised TypeError"
        except TypeError as e:
            assert "argument of type 'NoneType' is not iterable" in str(e)

    def test_mixed_matching_and_non_matching_sources(self):
        """Test mix of matching and non-matching single source IDs"""
        # Current implementation removes all brackets when any should be removed
        input_text = "start [A123] middle [B456] end [C789]"
        source_ids = ['A123', 'C789']  # Keep first and third, remove second
        expected = "start  middle  end "  # Current implementation removes all
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_no_sources_in_text(self):
        """Test text with no source references - should return unchanged"""
        input_text = "just some regular text without any source references"
        source_ids = ['J123', 'P456']
        expected = "just some regular text without any source references"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_empty_text(self):
        """Test empty text input"""
        input_text = ""
        source_ids = ['J123', 'P456']
        expected = ""
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_alphanumeric_source_ids(self):
        """Test various alphanumeric source ID formats"""
        # Current implementation removes all brackets when any should be removed
        input_text = "text [J123] and [P456] and [ABC789] and [XYZ012]"
        source_ids = ['J123', 'ABC789']
        expected = "text  and  and  and "  # Current implementation removes all
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_case_sensitive_matching(self):
        """Test that source ID matching is case sensitive"""
        # Current implementation removes all brackets when any should be removed
        input_text = "text [J123] and [j123] and [P456]"
        source_ids = ['J123', 'P456']
        expected = "text  and  and "  # Current implementation removes all (j123 doesn't match)
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_multiple_occurrences_same_source(self):
        """Test multiple occurrences of the same source ID"""
        input_text = "first [J123] second [J123] third [J123]"
        source_ids = ['J123']
        expected = "first [J123] second [J123] third [J123]"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_whitespace_preservation(self):
        """Test that whitespace around removed sources is preserved"""
        input_text = "text  [J123]  more text"
        source_ids = ['P456']  # J123 not in allowed list
        expected = "text    more text"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_numeric_only_source_ids(self):
        """Test source IDs that are numeric only"""
        # Current implementation removes all brackets when any should be removed
        input_text = "data [123] and [456] analysis"
        source_ids = ['123']
        expected = "data  and  analysis"  # Current implementation removes all
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_underscore_in_source_ids(self):
        """Test source IDs containing underscores (word characters)"""
        # Current implementation removes all brackets when any should be removed
        input_text = "text [J_123] and [P_456] more"
        source_ids = ['J_123']
        expected = "text  and  more"  # Current implementation removes all
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_current_implementation_limitation_with_comma_separated(self):
        """Test current implementation behavior with comma-separated source IDs (limitation)"""
        # Note: Current implementation regex r'\[(\w+)\]' cannot handle comma-separated IDs
        input_text = "text [J123] and [P456, Q789] more"
        source_ids = ['J123', 'P456', 'Q789']
        # Current implementation will keep [J123] but cannot parse [P456, Q789] due to comma
        expected = "text [J123] and [P456, Q789] more"  # [P456, Q789] stays because regex doesn't match it
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_brackets_with_spaces_not_matched(self):
        """Test that brackets with spaces are not matched by current regex"""
        input_text = "text [ J123 ] and [P456] more"
        source_ids = ['J123', 'P456']
        # Current regex won't match [ J123 ] due to spaces
        expected = "text [ J123 ] and [P456] more"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_default_parameter_behavior(self):
        """Test function with default None parameter (crashes)"""
        input_text = "text [J123] and [P456] more"
        # Current implementation crashes with TypeError when source_ids is None
        try:
            result = remove_non_matching_sources(input_text)  # Using default None
            assert False, "Should have raised TypeError"
        except TypeError as e:
            assert "argument of type 'NoneType' is not iterable" in str(e)

    def test_remove_all_if_empty_parameter_unused(self):
        """Test that remove_all_if_empty parameter exists but is currently unused"""
        input_text = "text [J123] and [P456] more"
        source_ids = []
        # Both calls should behave the same since parameter is unused in current implementation
        result1 = remove_non_matching_sources(input_text, source_ids, remove_all_if_empty=True)
        result2 = remove_non_matching_sources(input_text, source_ids, remove_all_if_empty=False)
        expected = "text  and  more"
        assert result1 == expected
        assert result2 == expected
        assert result1 == result2  # Should be identical since parameter is unused

    def test_bug_demonstration_removes_all_brackets(self):
        """Test demonstrating the bug where all brackets are removed when any should be removed"""
        # This test explicitly demonstrates the current buggy behavior
        input_text = "keep [KEEP] remove [REMOVE] keep [ALSO_KEEP]"
        source_ids = ['KEEP', 'ALSO_KEEP']
        # Expected behavior would be: "keep [KEEP] remove  keep [ALSO_KEEP]"
        # But current implementation removes all brackets because REMOVE is not in source_ids
        expected = "keep  remove  keep "
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected

    def test_all_sources_match_no_removal(self):
        """Test that when all sources match, nothing is removed"""
        input_text = "text [A123] and [B456] and [C789]"
        source_ids = ['A123', 'B456', 'C789']
        expected = "text [A123] and [B456] and [C789]"
        result = remove_non_matching_sources(input_text, source_ids)
        assert result == expected


class TestRemoveNonMatchingPlotPairs:
    """Test cases for remove_non_matching_plot_pairs function."""

    def test_empty_pairs_list_returns_original_text(self):
        """Test that empty pairs list returns original text unchanged"""
        input_text = "some text [intervention=123, outcome=456] more text"
        pairs = []
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == input_text

    def test_removes_non_matching_pairs(self):
        """Test basic functionality - removes pairs not in the list"""
        input_text = "some text [intervention=123, outcome=456] some text [intervention=789, outcome=1012]"
        pairs = [[123, 456], [789, 1011]]
        expected = "some text [intervention=123, outcome=456] some text "
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_keeps_matching_pairs(self):
        """Test that matching pairs are kept in the text"""
        input_text = "text [intervention=100, outcome=200] and [intervention=300, outcome=400]"
        pairs = [[100, 200], [300, 400]]
        expected = "text [intervention=100, outcome=200] and [intervention=300, outcome=400]"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_removes_all_non_matching_pairs(self):
        """Test that all non-matching pairs are removed"""
        input_text = "text [intervention=111, outcome=222] and [intervention=333, outcome=444]"
        pairs = [[555, 666]]
        expected = "text  and "
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_mixed_matching_and_non_matching_pairs(self):
        """Test mix of matching and non-matching pairs"""
        input_text = "start [intervention=1, outcome=2] middle [intervention=3, outcome=4] end [intervention=5, outcome=6]"
        pairs = [[1, 2], [5, 6]]  # Keep first and third, remove second
        expected = "start [intervention=1, outcome=2] middle  end [intervention=5, outcome=6]"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_no_plot_pairs_in_text(self):
        """Test text with no plot pairs - should return unchanged"""
        input_text = "just some regular text without any plot pairs"
        pairs = [[123, 456]]
        expected = "just some regular text without any plot pairs"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_empty_text(self):
        """Test empty text input"""
        input_text = ""
        pairs = [[123, 456]]
        expected = ""
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_docstring_example(self):
        """Test the exact example from the function docstring"""
        input_text = 'some text [intervention=123, outcome=456] some text [intervention=789, outcome=1012]'
        pairs = [[123, 456], [789, 1011]]
        expected = 'some text [intervention=123, outcome=456] some text '
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_single_pair_match(self):
        """Test single pair that matches"""
        input_text = "prefix [intervention=42, outcome=84] suffix"
        pairs = [[42, 84]]
        expected = "prefix [intervention=42, outcome=84] suffix"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_single_pair_no_match(self):
        """Test single pair that doesn't match"""
        input_text = "prefix [intervention=42, outcome=84] suffix"
        pairs = [[99, 99]]
        expected = "prefix  suffix"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_large_numbers(self):
        """Test with large intervention and outcome numbers"""
        input_text = "data [intervention=12345, outcome=67890] analysis"
        pairs = [[12345, 67890]]
        expected = "data [intervention=12345, outcome=67890] analysis"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_multiple_occurrences_same_pair(self):
        """Test multiple occurrences of the same pair"""
        input_text = "first [intervention=1, outcome=2] second [intervention=1, outcome=2] third"
        pairs = [[1, 2]]
        expected = "first [intervention=1, outcome=2] second [intervention=1, outcome=2] third"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_whitespace_preservation(self):
        """Test that whitespace around removed pairs is preserved"""
        input_text = "text  [intervention=1, outcome=2]  more text"
        pairs = [[99, 99]]
        expected = "text    more text"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_tuple_vs_list_matching(self):
        """Test that matching works correctly with tuples from regex vs lists from pairs"""
        input_text = "test [intervention=10, outcome=20] content"
        pairs = [[10, 20]]  # List of lists
        expected = "test [intervention=10, outcome=20] content"
        result = remove_non_matching_plot_pairs(input_text, pairs)
        assert result == expected

    def test_default_parameter_behavior(self):
        """Test function with default empty pairs parameter"""
        input_text = "text [intervention=1, outcome=2] more text"
        result = remove_non_matching_plot_pairs(input_text)  # Using default empty list
        assert result == input_text  # Should return unchanged


class TestHandlePlotText:
    """Test cases for sanitize_plot_text function based on the specified rules."""

    def test_rule_1_intervention_all_outcome_number(self):
        """Test rule 1: [intervention=123, outcome=ALL] => remove bracket"""
        input_text = "some text [intervention=123, outcome=ALL]"
        expected = "some text "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_2_intervention_number_outcome_all(self):
        """Test rule 2: [intervention=ALL, outcome=1234] => remove bracket"""
        input_text = "some text [intervention=ALL, outcome=1234]"
        expected = "some text "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_3_both_numbers_no_parentheses(self):
        """Test rule 3: [intervention=123, outcome=456] => keep bracket"""
        input_text = "some text 0.2 [intervention=123, outcome=456]"
        expected = "some text 0.2 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_4_single_number_parentheses(self):
        """Test rule 4: (0.2) [intervention=123, outcome=456] => 0.2 [intervention=123, outcome=456]"""
        input_text = "some text (0.2) [intervention=123, outcome=456]"
        expected = "some text 0.2 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_5_multiple_numbers_parentheses(self):
        """Test rule 5: (0.2, 4.5) [intervention=123, outcome=456] => 0.2, 4.5 [intervention=123, outcome=456]"""
        input_text = "some text (0.2, 4.5) [intervention=123, outcome=456]"
        expected = "some text 0.2, 4.5 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_6_single_occurance_of_intervention_only(self):
        """Test rule 6: [intervention=123] => remove bracket"""
        input_text = "some text [intervention=123]"
        expected = "some text "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_rule_7_single_occurance_of_outcome_only(self):
        """Test rule 7: [outcome=123] => remove bracket"""
        input_text = "some text [outcome=123]"
        expected = "some text "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_both_intervention_and_outcome_all(self):
        """Test case where both intervention and outcome are ALL"""
        input_text = "some text [intervention=ALL, outcome=ALL]"
        expected = "some text "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_multiple_parentheses_removal(self):
        """Test removal of multiple parentheses in the same text"""
        input_text = "text (1.5) more (2.3, 4.7) [intervention=123, outcome=456]"
        expected = "text 1.5 more 2.3, 4.7 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_whitespace_cleanup(self):
        """Test that extra whitespace is preserved (no longer cleaned up)"""
        input_text = "some  text   (0.2)  [intervention=123, outcome=ALL]"
        expected = "some  text   0.2  "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_no_brackets_or_parentheses(self):
        """Test text with no brackets or parentheses"""
        input_text = "simple text without any special formatting"
        expected = "simple text without any special formatting"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_empty_string(self):
        """Test empty string input"""
        input_text = ""
        expected = ""
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_only_whitespace(self):
        """Test string with only whitespace - should be preserved as-is"""
        input_text = "   \t\n  "
        expected = "   \t\n  "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_complex_mixed_case(self):
        """Test complex case with multiple rules applied"""
        input_text = "analysis (0.15, 2.8) shows [intervention=ALL, outcome=789] and (3.14) [intervention=456, outcome=123]"
        expected = "analysis 0.15, 2.8 shows  and 3.14 [intervention=456, outcome=123]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_parentheses_with_spaces(self):
        """Test parentheses with spaces around numbers - should remain unchanged"""
        input_text = "text ( 0.5 , 1.2 ) [intervention=123, outcome=456]"
        expected = "text ( 0.5 , 1.2 ) [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_bracket_with_spaces(self):
        """Test bracket with spaces around equals signs - should remain unchanged"""
        input_text = "text [ intervention = ALL , outcome = 123 ]"
        expected = "text [ intervention = ALL , outcome = 123 ]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_decimal_numbers_in_parentheses(self):
        """Test decimal numbers in parentheses"""
        input_text = "value (0.123456) [intervention=123, outcome=456]"
        expected = "value 0.123456 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_large_numbers_in_parentheses(self):
        """Test large numbers in parentheses"""
        input_text = "data (12345.67, 89012.34) [intervention=123, outcome=456]"
        expected = "data 12345.67, 89012.34 [intervention=123, outcome=456]"
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_preserve_newlines_and_tabs(self):
        """Test that newlines and tabs are preserved"""
        input_text = "line1\nline2\tline3  [intervention=123, outcome=ALL]"
        expected = "line1\nline2\tline3  "
        result = sanitize_plot_text(input_text)
        assert result == expected

    def test_preserve_multiple_spaces(self):
        """Test that multiple spaces are preserved (not cleaned up)"""
        input_text = "text    with    multiple    spaces"
        expected = "text    with    multiple    spaces"
        result = sanitize_plot_text(input_text)
        assert result == expected
