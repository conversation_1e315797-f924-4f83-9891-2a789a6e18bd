# Status Message Generation System

The agent uses a sophisticated AI-powered system to generate natural, contextual status messages that keep users informed during query processing.

## Overview

### The Challenge

Creating informative status messages is complex because:
- **Context Matters**: Messages should reference the user's specific question
- **Natural Language**: Technical tool names need human-friendly descriptions  
- **Repeat Handling**: Same tools may run multiple times (need different language)
- **Conciseness**: Messages must be short but informative

### The Solution

An LLM-powered system that:
- Analyzes execution step data and user context
- Generates natural, conversational status messages
- Handles first-run vs. repeat-run scenarios intelligently
- Ensures consistent length and formatting

## LLM Prompt Design

### Prompt Location
**File**: `agent/config/prompts/status_message_generator.prompt`

### Prompt Structure

The prompt includes several key components:

#### 1. Input Data Format
```json
STEP = {
  "tool_name": "entity_extractor",
  "status": "started",
  "executed_at": "2024-12-01T14:30:22",
  "data": {
    "query": "What are the effects of cash transfers?",
    "interventions": ["cash transfers"],
    "outcomes": ["education", "poverty"]
  }
}
```

#### 2. Context Variables
- **question**: The user's original query
- **entities_str**: Comma-separated key entities extracted
- **is_repeat_run**: Boolean indicating if this tool has run before
- **run_count**: Number of times this tool has executed

#### 3. Tool-Specific Rules

**Entity Extractor**:
- First run: "Understanding your question about {natural_subject}"
- Repeat run: "Refining understanding of {natural_subject}"

**SQL Generator**:
- First run: "Searching studies database for {natural_subject}"
- Repeat run: "Expanding search for {natural_subject}"

**Structured Data Organizer**:
- First run: "Organizing research data on {natural_subject}"
- Repeat run: "Refining organization of research on {natural_subject}"

**RAG Search**:
- First run: "Expanding knowledge on {natural_subject} from research papers"
- Repeat run: "Further expanding knowledge on {natural_subject}"

**Final Answer Generator**:
- First run: "Preparing comprehensive answer on {natural_subject}"
- Repeat run: "Improving answer on {natural_subject}"

#### 4. Natural Subject Transformation

The prompt converts technical queries into natural, conversational phrases:

| Original Query | Natural Subject |
|----------------|-----------------|
| "Are there any other studies by Mbiti?" | "other studies by author Mbiti" |
| "Which interventions improve social stigma?" | "interventions that improve social stigma" |
| "What is the effectiveness of cash transfers?" | "effectiveness of cash transfer programs" |
| "How do vaccines affect mortality?" | "vaccine effects on mortality" |

### Prompt Constraints

- **Maximum Length**: 100 characters (hard limit)
- **Single Line**: No quotes, emojis, or trailing punctuation
- **Present Tense**: Use active, present-tense language
- **Data-Driven**: Only use information from provided inputs
- **Natural Flow**: Conversational language that flows well

## Implementation Details

### LLM Client Integration

**Location**: `src/services/agent.py`

```python
def _build_status_prompt(self, step: Dict[str, Any]) -> str:
    """Create the robust prompt for the LLM to generate a ≤100 char message."""
    step_json = json.dumps(step, ensure_ascii=False)
    question = self.query or ""
    entities_str = self._extract_subject_terms(step.get("data"))
    
    # Check if this tool has been seen before
    tool_name = step.get("tool_name", "")
    tool_run_count = self.seen_tools.get(tool_name, 0)
    is_repeat_run = tool_run_count > 0
    
    return self.llm.load_prompt(
        "status_message_generator",
        step_json=step_json,
        question=question,
        entities_str=entities_str,
        is_repeat_run=is_repeat_run,
        run_count=tool_run_count + 1,
    )
```

### Entity Extraction for Context

The system extracts meaningful terms from execution data:

```python
def _extract_subject_terms(self, data: Any) -> str:
    """Extract subject terms from tool execution data for context."""
    if not data or not isinstance(data, dict):
        return ""
    
    terms = []
    
    # Extract interventions and outcomes
    if 'interventions' in data and data['interventions']:
        terms.extend(data['interventions'])
    if 'outcomes' in data and data['outcomes']:
        terms.extend(data['outcomes'])
    
    # Extract from paper titles or other text fields
    if 'papers' in data:
        # Extract key terms from paper titles
        pass
    
    return ", ".join(terms[:3])  # Limit to top 3 terms
```

### Fallback System

If LLM generation fails, the system uses static fallback messages:

```python
FALLBACK_DESCRIPTIONS = {
    "entity_extractor": "Understanding your research question",
    "sql_generator": "Searching studies database for relevant research", 
    "rag_search": "Expanding knowledge from research literature",
    "structured_data_organizer": "Organizing research data and findings",
    "final_answer_generator": "Preparing comprehensive research answer",
    "methodology_explainer": "Explaining research methodology",
}
```

### Message Sanitization

All LLM outputs are sanitized for consistency:

```python
@staticmethod
def _sanitize_llm_message(text: str, fallback: str) -> str:
    """Ensure the output is a single line ≤ 100 chars; fallback if empty."""
    if not text or not text.strip():
        return fallback
    
    # Take first line only, strip whitespace
    line = text.strip().split('\n')[0].strip()
    
    # Truncate if too long
    if len(line) > 100:
        line = line[:97] + "..."
    
    return line
```

## Example Status Generation Flow

### Input Example
```json
{
  "tool_name": "entity_extractor",
  "status": "finished", 
  "data": {
    "query": "What are the effects of cash transfers on education in Kenya?",
    "interventions": ["cash transfers"],
    "outcomes": ["education"],
    "regions": ["Kenya"]
  }
}
```

### Context Variables
```
question = "What are the effects of cash transfers on education in Kenya?"
entities_str = "cash transfers, education, Kenya"
is_repeat_run = false
run_count = 1
```

### Generated Prompt (Simplified)
```
You write ONE descriptive status message for entity_extractor that finished.

Input: {"tool_name": "entity_extractor", "status": "finished", ...}
Context: question="What are the effects of cash transfers on education in Kenya?"
entities="cash transfers, education, Kenya"

Rules:
- Max 100 characters
- First run: "Found 2 relevant concept(s): cash transfers, education"
- Include natural context about the user's question

Output: Return only the final message.
```

### LLM Output
```
"Found 2 relevant concepts: cash transfers and education effects in Kenya"
```

## Advanced Features

### Repeat Run Detection

The system tracks when tools run multiple times and adjusts language accordingly:

```python
# First run
"Understanding your question about cash transfer effectiveness"

# Second run  
"Refining understanding of development economics interventions"
```

### Dynamic Subject Extraction

The system intelligently extracts the most relevant terms from execution data:

1. **Priority Order**: interventions → outcomes → regions → other terms
2. **Natural Language**: Converts technical terms to readable phrases
3. **Length Limits**: Keeps entity lists concise for short messages

### Error Handling

Robust error handling ensures the system always provides useful feedback:

1. **LLM Failure**: Falls back to static messages
2. **Empty Data**: Uses generic but contextual descriptions
3. **Long Messages**: Automatically truncates with ellipsis
4. **Invalid JSON**: Safely handles malformed execution data

## Configuration

### LLM Settings

```python
# LLM configuration for status generation
llm_resp = await self.llm.generate(
    prompt=prompt,
    temperature=0.1,  # Low temperature for consistent output
    max_tokens=64,    # Sufficient for short messages
)
```

### Prompt Customization

To customize status messages, edit the prompt file:
```bash
agent/config/prompts/status_message_generator.prompt
```

Key areas to modify:
- Tool-specific rules and templates
- Natural subject transformation patterns
- Length constraints and formatting rules

## Testing and Validation

### Manual Testing

```python
from src.services.agent import ProgressMonitor

# Test status generation
monitor = ProgressMonitor(cache, "test_conv", "What is poverty?")
step_data = {
    "tool_name": "entity_extractor",
    "status": "started",
    "data": {"query": "What is poverty?"}
}

status = await monitor._generate_status_message(step_data)
print(status)  # Should be ≤100 chars and contextual
```

### Validation Criteria

Good status messages should be:
- ✅ **Under 100 characters**
- ✅ **Single line (no newlines)**
- ✅ **Present tense**
- ✅ **Contextual** (reference user's question)
- ✅ **Natural language** (not technical jargon)
- ✅ **Informative** (tell user what's happening)

## Performance Considerations

- **API Calls**: One LLM call per status update (~5-10 per query)
- **Latency**: Status generation adds ~200-500ms per update
- **Caching**: No caching (messages are contextual and unique)
- **Fallbacks**: Zero latency for static fallback messages

## Future Improvements

1. **Smart Caching**: Cache similar messages with context variations
2. **Progressive Detail**: Start generic, become more specific over time
3. **User Preferences**: Allow users to control message verbosity
4. **Multilingual Support**: Generate status messages in user's language
5. **Rich Messages**: Include progress percentages or estimated times
