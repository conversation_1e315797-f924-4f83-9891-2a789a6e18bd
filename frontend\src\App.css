/* Apply Roboto font to all elements */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

html {
  font-size: 16px;
}

body {
  min-height: 100vh;
  display: flex;
  background: #fff;
}

input[type="text"],
textarea,
input[type="password"],
input[type="email"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="number"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"] {
  font-family: inherit;
}

::-webkit-input-placeholder {
  font-family: inherit;
}
:-moz-placeholder {
  font-family: inherit;
}
::-moz-placeholder {
  font-family: inherit;
}
:-ms-input-placeholder {
  font-family: inherit;
}

#root {
  flex: 1;
}
.app {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.sm-padding {
  padding: 5px;
}

.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}
