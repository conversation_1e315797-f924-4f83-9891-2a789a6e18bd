from typing import Any, Dict, List

from database.core import get_core_db_session
from sqlalchemy import text

from services.files import FilesService

files_service = FilesService()


async def get_paper_abstracts_by_ids(paper_ids: List[int]) -> List[Dict[str, Any]]:
    """
    Fetches the abstracts for a list of paper ids.
    """
    async with get_core_db_session() as session:
        query = files_service.load_abstract_request()
        query = query.replace(":paper_ids", ",".join(str(x) for x in paper_ids))
        result = await session.execute(text(query))
        return [row._asdict() for row in result.all()]
